#!/usr/bin/env python3
"""
环境配置设置脚本
帮助用户快速配置.env文件
"""
import os
import sys
import logging

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def check_existing_env():
    """检查现有的.env文件"""
    if os.path.exists('.env'):
        logger.info("发现现有的.env文件")
        
        # 读取现有配置
        existing_vars = {}
        try:
            with open('.env', 'r', encoding='utf-8') as f:
                for line in f:
                    line = line.strip()
                    if line and not line.startswith('#') and '=' in line:
                        key, value = line.split('=', 1)
                        existing_vars[key.strip()] = value.strip()
        except Exception as e:
            logger.error(f"读取.env文件失败: {e}")
            return {}
        
        logger.info(f"找到 {len(existing_vars)} 个现有配置项")
        return existing_vars
    
    return {}

def create_env_file():
    """创建.env文件"""
    logger.info("创建新的.env文件...")
    
    # 检查现有配置
    existing_vars = check_existing_env()
    
    # 必需的配置项
    required_configs = {
        'SECRET_KEY': {
            'description': 'Flask密钥（用于会话加密）',
            'default': 'your-secret-key-change-this-in-production',
            'required': True
        },
        'MYSQL_HOST': {
            'description': 'MySQL数据库主机',
            'default': 'localhost',
            'required': True
        },
        'MYSQL_PORT': {
            'description': 'MySQL数据库端口',
            'default': '3306',
            'required': False
        },
        'MYSQL_USER': {
            'description': 'MySQL用户名',
            'default': '',
            'required': True
        },
        'MYSQL_PASSWORD': {
            'description': 'MySQL密码',
            'default': '',
            'required': True
        },
        'MYSQL_DB': {
            'description': 'MySQL数据库名',
            'default': 'yba_monitor',
            'required': True
        },
        'MYSQL_CHARSET': {
            'description': 'MySQL字符集',
            'default': 'utf8mb4',
            'required': False
        },
        'ADMIN_TOKEN': {
            'description': '管理员Token',
            'default': 'admin-token-change-this',
            'required': True
        },
        'FLASK_ENV': {
            'description': 'Flask环境（development/production）',
            'default': 'development',
            'required': False
        },
        'FLASK_DEBUG': {
            'description': '是否启用调试模式（true/false）',
            'default': 'true',
            'required': False
        },
        'FLASK_HOST': {
            'description': 'Web服务器监听地址',
            'default': '0.0.0.0',
            'required': False
        },
        'FLASK_PORT': {
            'description': 'Web服务器端口',
            'default': '5000',
            'required': False
        },
        'LOGIN_CHECK_INTERVAL': {
            'description': '登录状态检查间隔（秒）',
            'default': '300',
            'required': False
        },
        'ENABLE_PERIODIC_CHECK': {
            'description': '是否启用定期检查（true/false）',
            'default': 'true',
            'required': False
        }
    }
    
    # 收集配置
    configs = {}
    
    print("\n" + "="*60)
    print("YBA监控管理系统环境配置")
    print("="*60)
    print("请按提示输入配置信息，直接回车使用默认值")
    print()
    
    for key, config in required_configs.items():
        # 如果已存在配置，显示现有值
        current_value = existing_vars.get(key, config['default'])
        
        while True:
            prompt = f"{config['description']}"
            if current_value:
                prompt += f" [{current_value}]"
            prompt += ": "
            
            user_input = input(prompt).strip()
            
            # 如果用户没有输入，使用当前值
            if not user_input:
                value = current_value
            else:
                value = user_input
            
            # 验证必需项
            if config['required'] and not value:
                print(f"错误: {key} 是必需的配置项，请输入有效值")
                continue
            
            configs[key] = value
            break
    
    # 写入.env文件
    try:
        with open('.env', 'w', encoding='utf-8') as f:
            f.write("# YBA监控管理系统环境配置\n")
            f.write(f"# 生成时间: {os.popen('date').read().strip()}\n\n")
            
            # 按类别写入配置
            categories = {
                'Flask配置': ['SECRET_KEY', 'FLASK_ENV', 'FLASK_DEBUG', 'FLASK_HOST', 'FLASK_PORT'],
                '数据库配置': ['MYSQL_HOST', 'MYSQL_PORT', 'MYSQL_USER', 'MYSQL_PASSWORD', 'MYSQL_DB', 'MYSQL_CHARSET'],
                '管理员配置': ['ADMIN_TOKEN'],
                '系统配置': ['LOGIN_CHECK_INTERVAL', 'ENABLE_PERIODIC_CHECK']
            }
            
            for category, keys in categories.items():
                f.write(f"# {category}\n")
                for key in keys:
                    if key in configs:
                        f.write(f"{key}={configs[key]}\n")
                f.write("\n")
        
        logger.info("✓ .env文件创建成功")
        return True
        
    except Exception as e:
        logger.error(f"创建.env文件失败: {e}")
        return False

def validate_env():
    """验证.env文件配置"""
    if not os.path.exists('.env'):
        logger.error("未找到.env文件")
        return False
    
    logger.info("验证.env文件配置...")
    
    # 加载环境变量
    from app.utils.env_loader import EnvLoader
    EnvLoader.load_env_file()
    
    # 验证必需变量
    is_valid, missing_vars = EnvLoader.validate_required_env()
    
    if is_valid:
        logger.info("✓ 环境配置验证通过")
        
        # 显示配置摘要
        print("\n配置摘要:")
        print(f"  数据库: {os.environ.get('MYSQL_USER')}@{os.environ.get('MYSQL_HOST')}:{os.environ.get('MYSQL_PORT')}/{os.environ.get('MYSQL_DB')}")
        print(f"  Web服务: {os.environ.get('FLASK_HOST')}:{os.environ.get('FLASK_PORT')}")
        print(f"  调试模式: {os.environ.get('FLASK_DEBUG')}")
        print(f"  定期检查: {os.environ.get('ENABLE_PERIODIC_CHECK')}")
        
        return True
    else:
        logger.error(f"✗ 配置验证失败，缺少: {', '.join(missing_vars)}")
        return False

def test_database():
    """测试数据库连接"""
    logger.info("测试数据库连接...")
    
    try:
        from app.models import get_db_connection
        connection = get_db_connection()
        
        with connection.cursor() as cursor:
            cursor.execute("SELECT VERSION()")
            version = cursor.fetchone()[0]
            logger.info(f"✓ 数据库连接成功，版本: {version}")
        
        connection.close()
        return True
        
    except Exception as e:
        logger.error(f"✗ 数据库连接失败: {e}")
        return False

def main():
    """主函数"""
    print("YBA监控管理系统环境配置工具")
    print("="*40)
    
    if len(sys.argv) > 1:
        command = sys.argv[1]
        
        if command == 'create':
            return create_env_file()
        elif command == 'validate':
            return validate_env()
        elif command == 'test-db':
            return test_database()
        else:
            print(f"未知命令: {command}")
            print("可用命令: create, validate, test-db")
            return False
    
    # 交互式模式
    while True:
        print("\n请选择操作:")
        print("1. 创建/更新 .env 文件")
        print("2. 验证 .env 配置")
        print("3. 测试数据库连接")
        print("4. 退出")
        
        choice = input("\n请输入选择 (1-4): ").strip()
        
        if choice == '1':
            create_env_file()
        elif choice == '2':
            validate_env()
        elif choice == '3':
            test_database()
        elif choice == '4':
            print("退出")
            break
        else:
            print("无效选择，请重试")

if __name__ == '__main__':
    main()
