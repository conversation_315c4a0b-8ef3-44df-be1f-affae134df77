"""
环境变量加载工具
支持从.env文件加载环境变量，并提供默认值和类型转换
"""
import os
import logging

logger = logging.getLogger(__name__)


class EnvLoader:
    """环境变量加载器"""
    
    @staticmethod
    def load_env_file(env_file='.env'):
        """
        加载.env文件中的环境变量
        
        Args:
            env_file (str): 环境变量文件路径，默认为'.env'
            
        Returns:
            bool: 是否成功加载
        """
        if not os.path.exists(env_file):
            logger.warning(f"环境变量文件 {env_file} 不存在")
            return False
        
        try:
            loaded_count = 0
            with open(env_file, 'r', encoding='utf-8') as f:
                for line_num, line in enumerate(f, 1):
                    line = line.strip()
                    
                    # 跳过空行和注释行
                    if not line or line.startswith('#'):
                        continue
                    
                    # 检查是否包含等号
                    if '=' not in line:
                        logger.warning(f"{env_file}:{line_num} - 无效的环境变量格式: {line}")
                        continue
                    
                    # 分割键值对
                    key, value = line.split('=', 1)
                    key = key.strip()
                    value = value.strip()
                    
                    # 处理引号
                    if value.startswith('"') and value.endswith('"'):
                        value = value[1:-1]
                    elif value.startswith("'") and value.endswith("'"):
                        value = value[1:-1]
                    
                    # 只有当环境变量不存在时才设置
                    if key not in os.environ:
                        os.environ[key] = value
                        loaded_count += 1
                        logger.debug(f"加载环境变量: {key}")
            
            logger.info(f"从 {env_file} 加载了 {loaded_count} 个环境变量")
            return True
            
        except Exception as e:
            logger.error(f"加载环境变量文件失败: {str(e)}")
            return False
    
    @staticmethod
    def get_env(key, default=None, required=False, env_type=str):
        """
        获取环境变量，支持类型转换和默认值
        
        Args:
            key (str): 环境变量名
            default: 默认值
            required (bool): 是否必需
            env_type (type): 期望的类型
            
        Returns:
            转换后的环境变量值
            
        Raises:
            ValueError: 当required=True且环境变量不存在时
        """
        value = os.environ.get(key)
        
        if value is None:
            if required:
                raise ValueError(f"必需的环境变量 {key} 未设置")
            return default
        
        # 类型转换
        try:
            if env_type == bool:
                return value.lower() in ('true', '1', 'yes', 'on')
            elif env_type == int:
                return int(value)
            elif env_type == float:
                return float(value)
            elif env_type == list:
                return [item.strip() for item in value.split(',') if item.strip()]
            else:
                return env_type(value)
        except (ValueError, TypeError) as e:
            logger.warning(f"环境变量 {key} 类型转换失败: {str(e)}")
            return default
    
    @staticmethod
    def get_database_config():
        """
        获取数据库配置
        
        Returns:
            dict: 数据库配置字典
        """
        return {
            'host': EnvLoader.get_env('MYSQL_HOST', 'localhost'),
            'port': EnvLoader.get_env('MYSQL_PORT', 3306, env_type=int),
            'user': EnvLoader.get_env('MYSQL_USER', required=True),
            'password': EnvLoader.get_env('MYSQL_PASSWORD', required=True),
            'database': EnvLoader.get_env('MYSQL_DB', required=True),
            'charset': EnvLoader.get_env('MYSQL_CHARSET', 'utf8mb4')
        }
    
    @staticmethod
    def get_flask_config():
        """
        获取Flask配置
        
        Returns:
            dict: Flask配置字典
        """
        return {
            'SECRET_KEY': EnvLoader.get_env('SECRET_KEY', required=True),
            'DEBUG': EnvLoader.get_env('FLASK_DEBUG', False, env_type=bool),
            'HOST': EnvLoader.get_env('FLASK_HOST', '0.0.0.0'),
            'PORT': EnvLoader.get_env('FLASK_PORT', 5000, env_type=int),
            'ENV': EnvLoader.get_env('FLASK_ENV', 'production')
        }
    
    @staticmethod
    def get_admin_config():
        """
        获取管理员配置
        
        Returns:
            dict: 管理员配置字典
        """
        return {
            'ADMIN_TOKEN': EnvLoader.get_env('ADMIN_TOKEN', required=True)
        }
    
    @staticmethod
    def validate_required_env():
        """
        验证必需的环境变量是否都已设置
        
        Returns:
            tuple: (是否验证通过, 缺失的环境变量列表)
        """
        required_vars = [
            'SECRET_KEY',
            'MYSQL_HOST',
            'MYSQL_USER',
            'MYSQL_PASSWORD',
            'MYSQL_DB',
            'ADMIN_TOKEN'
        ]
        
        missing_vars = []
        for var in required_vars:
            if not os.environ.get(var):
                missing_vars.append(var)
        
        return len(missing_vars) == 0, missing_vars
    
    @staticmethod
    def create_env_example():
        """
        创建.env.example文件模板
        """
        template = """# Flask配置
SECRET_KEY=your-secret-key-here

# 数据库配置
MYSQL_HOST=localhost
MYSQL_PORT=3306
MYSQL_USER=your-mysql-user
MYSQL_PASSWORD=your-mysql-password
MYSQL_DB=your-database-name
MYSQL_CHARSET=utf8mb4

# 管理员配置
ADMIN_TOKEN=your-admin-token

# 应用配置
FLASK_ENV=development
FLASK_DEBUG=true
FLASK_HOST=0.0.0.0
FLASK_PORT=5000
"""
        
        try:
            with open('.env.example', 'w', encoding='utf-8') as f:
                f.write(template)
            logger.info("已创建 .env.example 文件")
            return True
        except Exception as e:
            logger.error(f"创建 .env.example 文件失败: {str(e)}")
            return False


# 便捷函数
def load_env(env_file='.env'):
    """加载环境变量的便捷函数"""
    return EnvLoader.load_env_file(env_file)


def get_env(key, default=None, required=False, env_type=str):
    """获取环境变量的便捷函数"""
    return EnvLoader.get_env(key, default, required, env_type)


# 自动加载环境变量
if not os.environ.get('ENV_LOADED'):
    EnvLoader.load_env_file()
    os.environ['ENV_LOADED'] = 'true'
