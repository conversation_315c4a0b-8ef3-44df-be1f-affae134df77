"""
统一数据模型定义

定义了适配器使用的标准化数据结构，确保不同版本API的数据能够统一处理。
"""

from dataclasses import dataclass, field
from typing import Optional, List, Dict, Any, Union
from datetime import datetime
from enum import Enum


class MessageType(Enum):
    """消息类型枚举"""
    TEXT = "text"
    IMAGE = "image"
    VIDEO = "video"
    VOICE = "voice"
    FILE = "file"
    LINK = "link"
    NAME_CARD = "name_card"
    EMOJI = "emoji"
    LOCATION = "location"
    MINI_APP = "mini_app"
    APP_MSG = "app_msg"
    SYSTEM = "system"
    UNKNOWN = "unknown"


class ContactType(Enum):
    """联系人类型枚举"""
    FRIEND = "friend"
    GROUP = "group"
    PUBLIC_ACCOUNT = "public_account"
    UNKNOWN = "unknown"


@dataclass
class Contact:
    """联系人信息"""
    wxid: str
    nickname: str
    contact_type: ContactType
    avatar_url: Optional[str] = None
    remark: Optional[str] = None
    is_friend: bool = False
    is_blocked: bool = False
    
    def to_dict(self) -> Dict[str, Any]:
        return {
            "wxid": self.wxid,
            "nickname": self.nickname,
            "contact_type": self.contact_type.value,
            "avatar_url": self.avatar_url,
            "remark": self.remark,
            "is_friend": self.is_friend,
            "is_blocked": self.is_blocked
        }


@dataclass
class GroupInfo:
    """群组信息"""
    group_id: str
    group_name: str
    member_count: int
    owner_wxid: str
    admin_list: List[str] = field(default_factory=list)
    member_list: List[str] = field(default_factory=list)
    announcement: Optional[str] = None
    avatar_url: Optional[str] = None
    
    def to_dict(self) -> Dict[str, Any]:
        return {
            "group_id": self.group_id,
            "group_name": self.group_name,
            "member_count": self.member_count,
            "owner_wxid": self.owner_wxid,
            "admin_list": self.admin_list,
            "member_list": self.member_list,
            "announcement": self.announcement,
            "avatar_url": self.avatar_url
        }


@dataclass
class MessageContent:
    """消息内容"""
    text: Optional[str] = None
    file_url: Optional[str] = None
    file_name: Optional[str] = None
    image_url: Optional[str] = None
    video_url: Optional[str] = None
    voice_url: Optional[str] = None
    voice_duration: Optional[int] = None
    link_url: Optional[str] = None
    link_title: Optional[str] = None
    link_desc: Optional[str] = None
    location_lat: Optional[float] = None
    location_lng: Optional[float] = None
    location_address: Optional[str] = None
    at_list: List[str] = field(default_factory=list)
    extra_data: Dict[str, Any] = field(default_factory=dict)
    
    def to_dict(self) -> Dict[str, Any]:
        return {
            "text": self.text,
            "file_url": self.file_url,
            "file_name": self.file_name,
            "image_url": self.image_url,
            "video_url": self.video_url,
            "voice_url": self.voice_url,
            "voice_duration": self.voice_duration,
            "link_url": self.link_url,
            "link_title": self.link_title,
            "link_desc": self.link_desc,
            "location_lat": self.location_lat,
            "location_lng": self.location_lng,
            "location_address": self.location_address,
            "at_list": self.at_list,
            "extra_data": self.extra_data
        }


@dataclass
class StandardMessage:
    """标准化消息结构"""
    message_id: str
    message_type: MessageType
    from_wxid: str
    to_wxid: str
    content: MessageContent
    timestamp: datetime
    is_self: bool = False
    is_group: bool = False
    group_id: Optional[str] = None
    raw_data: Dict[str, Any] = field(default_factory=dict)
    
    def to_dict(self) -> Dict[str, Any]:
        return {
            "message_id": self.message_id,
            "message_type": self.message_type.value,
            "from_wxid": self.from_wxid,
            "to_wxid": self.to_wxid,
            "content": self.content.to_dict(),
            "timestamp": self.timestamp.isoformat(),
            "is_self": self.is_self,
            "is_group": self.is_group,
            "group_id": self.group_id,
            "raw_data": self.raw_data
        }


@dataclass
class ApiResponse:
    """API响应结构"""
    success: bool
    data: Any = None
    error_code: Optional[str] = None
    error_message: Optional[str] = None
    raw_response: Dict[str, Any] = field(default_factory=dict)
    
    def to_dict(self) -> Dict[str, Any]:
        return {
            "success": self.success,
            "data": self.data,
            "error_code": self.error_code,
            "error_message": self.error_message,
            "raw_response": self.raw_response
        }


@dataclass
class SendMessageRequest:
    """发送消息请求"""
    to_wxid: str
    message_type: MessageType
    content: MessageContent
    
    def to_dict(self) -> Dict[str, Any]:
        return {
            "to_wxid": self.to_wxid,
            "message_type": self.message_type.value,
            "content": self.content.to_dict()
        }
