import logging
import xml.etree.ElementTree as ET
from openai import OpenAI
import os
import json
import time
import difflib
import re
import requests
from PIL import Image, ImageEnhance, ImageDraw, ImageFont, ImageFilter
import io
from collections import OrderedDict
from database import Database
import traceback
import datetime
import random
import string
import concurrent.futures
import socket
import base64
import numpy as np
import secrets


hamibot_api_token = 'hmp_8070c3ce71eefdfb12091ff778ff6cb03d85a51ebb319256e6745fd42f911857'

小月通知群 = "56131396190@chatroom"
塞罗通知群 = "53558241173@chatroom"
hang通知群 = "51952527622@chatroom"
通知群 = [塞罗通知群, hang通知群]
通知测试群 = "47672310999@chatroom"
小窝群 = "52273520344@chatroom"
YBA交流群 = ["51721032528@chatroom", "56163496162@chatroom", "53989030054@chatroom", "52473124675@chatroom"]
做链接群 = ["55949209083@chatroom"]
VIP监控群 = ["53754525037@chatroom", "56807498641@chatroom", "57745099278@chatroom", "56156699748@chatroom"]

product_keywords = ["公仔", "套装", "手办", "盲盒", "毛绒", "挂件", "吊卡", "搪胶", "系列", "耳机", "手机"]
series_keywords = ["labubu", "skullpanda", "molly", "dimoo", "pucky", "monsters", "mokoko", "crybaby", "hirono", "hacipupu", "zsiga", "tinytiny", "kubo", "jelly", "bunny", "nyota", "yuki"]

class MessageHandler:
    def __init__(self, bot):
        self.bot = bot
        self.logger = logging.getLogger(__name__)
        # 设置日志级别为INFO，只显示重要信息
        self.logger.setLevel(logging.INFO)
        
        # # 配置日志格式，添加时间戳
        # formatter = logging.Formatter('%(asctime)s - %(levelname)s - %(message)s')
        # # 如果已经有处理器，先移除
        # for handler in self.logger.handlers[:]:
        #     self.logger.removeHandler(handler)
        # # 添加新的处理器
        # handler = logging.StreamHandler()
        # handler.setFormatter(formatter)
        # self.logger.addHandler(handler)

        # 添加小程序发送缓冲过滤器（使用更高效的数据结构）
        self.miniapp_send_buffer = OrderedDict()  # 格式: {f"{store_id}_{product_id}": timestamp}
        self.miniapp_buffer_window = 300  # 缓冲窗口大小（秒）
        self.buffer_clean_interval = 60  # 清理间隔（秒）
        self.last_buffer_clean_time = time.time()  # 上次清理时间
        self.buffer_clean_threshold = 300  # 缓冲区大小超过此值时强制清理

        # 消息ID缓存优化
        self.message_cache = OrderedDict()  # 使用OrderedDict替代普通dict
        self.message_cache_size = 300  # 缓存大小
        self.message_cache_ttl = 30  # 缓存过期时间（秒）
        self.last_cache_cleanup_time = time.time()  # 上次清理时间
        self.cache_cleanup_interval = 30  # 清理间隔（秒）
        
        # 自定义广告设置
        self.waiting_ad_image = False
        self.ad_image_sender = None
        
        # 原始消息内容缓存，使用简单的循环ID
        self.original_message_cache = {}  # 格式: {id: message_content}
        self.current_message_id = 1  # 当前消息ID，从1开始，循环到99
        self.max_message_id = 99  # 最大消息ID
        
        # 从环境变量获取外部访问URL，默认使用localhost
        self.external_url = os.environ.get('EXTERNAL_URL', 'http://cloud.yaoboan.com:2533')
        
        # 添加发送图片频率控制
        self.last_image_send_time = 0  # 上次发送图片的时间
        self.image_send_interval = 1  # 发送图片的最小间隔（秒）
        self.image_send_count = 0  # 当前时间窗口内发送的图片数
        self.image_send_limit = 20  # 每个时间窗口允许发送的最大图片数
        self.image_send_window = 60  # 时间窗口大小（秒）
        self.image_send_window_start = time.time()  # 当前时间窗口的开始时间
        
        self.db = Database()

        # 加载店铺和商品信息
        self.store_info = self.db.get_all_stores()
        self.product_info = self.db.get_all_products()
        if self.bot.is_master:
            self.notify_product_ids = self.db.get_notify_product_ids('main')
        else:
            self.notify_product_ids = self.db.get_notify_product_ids(self.bot.wxid)
        
        # 添加商品索引结构
        self.product_index = self._build_product_index(self.product_info)

        # 创建product_qr_img文件夹（如果不存在）
        os.makedirs('product_qr_img', exist_ok=True)
        if self.bot.is_master:
            # self.image_root = f'product_qr_img/'
            # 初始化 DeepSeek 客户端
            self.deepseek_client = OpenAI(
                api_key=os.environ.get("DEEPSEEK_API_KEY", "***********************************"),
                base_url="https://api.deepseek.com"
                # api_key="***********************************",
                # base_url="https://dashscope.aliyuncs.com/compatible-mode/v1"
            )
            
        # 创建product_qr_img文件夹（如果不存在）
        self.image_root = f'product_qr_img/{self.bot.wxid}/'
        os.makedirs(f'product_qr_img/{self.bot.wxid}', exist_ok=True)
        
        # 添加 CDN 信息的内存缓存
        self.cdn_cache = {}  # 格式: {product_id: cdn_info}
        
        # 检查并生成缺失的商品合成图片和CDN信息
        self._check_and_generate_missing_images()
        self._check_and_update_cdn_info()

    def _check_and_add_store(self, store_id, store_name=None, group_id=None):
        """检查并添加店铺信息"""
        result = self.db.add_store(store_id, store_name or f"未知店铺_{store_id}", group_id)
        if result:
            # 更新内存中的店铺信息
            self.store_info = self.db.get_all_stores()
        return result

    def _check_and_add_product(self, product_id, product_name, product_img=None):
        """
        检查并添加商品信息
        
        Returns:
            - True: 新增商品成功
            - "updated": 更新图片成功
            - False: 添加失败
            - None: 商品已存在且无需更新
        """
        try:
            # 检查商品是否已存在
            existing_product = next((p for p in self.product_info if p['product_id'] == product_id), None)
            
            if existing_product:
                # 商品已存在，检查图片是否需要更新
                if product_img and existing_product.get('product_img') != product_img:
                    # 更新商品信息
                    result = self.db.add_product(product_id, product_name, product_img)
                    if result:
                        # 更新内存中的商品信息
                        self.product_info = self.db.get_all_products()
                        self.product_index = self._build_product_index(self.product_info)
                        return "updated"  # 表示图片已更新
                    return False  # 更新失败
                return None  # 商品已存在且无需更新
            else:
                # 新增商品
                result = self.db.add_product(product_id, product_name, product_img)
                if result:
                    # 更新内存中的商品信息
                    self.product_info = self.db.get_all_products()
                    self.product_index = self._build_product_index(self.product_info)
                    return True  # 表示新增成功
                return False  # 新增失败
                
        except Exception as e:
            self.logger.error(f"检查并添加商品失败: {str(e)}")
            return False

    def _check_and_add_notify_product(self, product_id):
        """检查并添加通知商品"""
        try:
            # 使用索引快速检查商品是否已存在
            if product_id not in self.product_index["id_map"]:
                self.logger.warning(f"商品不存在: {product_id}")
                return False
            
            # 添加通知商品
            wxid = 'main' if self.bot.is_master else self.bot.wxid
            result = self.db.add_notify_product(product_id, wxid)
            if result:
                # 更新内存中的通知商品ID列表
                self.notify_product_ids = self.db.get_notify_product_ids(wxid)
                self.logger.info(f"成功添加通知商品: {product_id}")
                return True
            return False
            
        except Exception as e:
            self.logger.error(f"添加通知商品失败: {str(e)}")
            return False

    def _remove_notify_product(self, product_id):
        """从通知列表中移除商品"""
        try:
            # 快速检查商品是否在通知列表中
            if product_id not in self.notify_product_ids:
                self.logger.warning(f"商品不在通知列表中: {product_id}")
                return False
            
            # 获取商品信息用于日志
            product = self.product_index["id_map"][product_id]
            product_name = product.get('nick_name') or product.get('product_name')
            
            # 移除通知商品
            wxid = 'main' if self.bot.is_master else self.bot.wxid
            result = self.db.remove_notify_product(product_id, wxid)
            if result:
                # 更新内存中的通知商品ID列表
                self.notify_product_ids = self.db.get_notify_product_ids(wxid)
                self.logger.info(f"成功移除通知商品: {product_name}({product_id})")
                return True
            return False
            
        except Exception as e:
            self.logger.error(f"移除通知商品失败: {str(e)}")
            return False

    def _save_product_info(self, product_to_save=None):
        """
        保存商品信息到数据库
        Args:
            product_to_save: dict, 需要保存的单个商品信息。如果为None，则保存所有商品
        Returns:
            bool: 保存成功返回True，否则返回False
        """
        try:
            if product_to_save:
                # 保存单个商品信息
                product_id = product_to_save.get('product_id')
                product_name = product_to_save.get('product_name')
                
                if not product_id or not product_name:
                    self.logger.error("商品信息不完整，缺少必要字段")
                    return False
                
                # 保存基本商品信息
                result = self.db.add_product(
                    product_id,
                    product_name,
                    product_to_save.get('product_img'),
                    product_to_save.get('nick_name')
                )
                
                if not result:
                    self.logger.error(f"保存商品基本信息失败: {product_name}")
                    return False
                
                # 如果有CDN信息，保存CDN信息
                if product_to_save.get('cdn_info'):
                    cdn_result = self.db.update_product_cdn_info(
                        product_id,
                        product_to_save['cdn_info'],
                        self.bot.wxid
                    )
                    if not cdn_result:
                        self.logger.warning(f"更新商品CDN信息失败: {product_name}")
                
                # 更新内存中的商品信息
                self.product_info = self.db.get_all_products()
                self.product_index = self._build_product_index(self.product_info)
                
                
                self.logger.info(f"商品 {product_name} 保存成功")
                return True
                
            else:
                # 批量保存所有商品（不建议频繁使用）
                success_count = 0
                total_count = len(self.product_info)
                
                for product in self.product_info:
                    try:
                        # 保存基本商品信息
                        result = self.db.add_product(
                            product['product_id'],
                            product['product_name'],
                            product.get('product_img'),
                            product.get('nick_name')
                        )
                        
                        if result:
                            # 如果有CDN信息，保存CDN信息
                            if product.get('cdn_info'):
                                cdn_result = self.db.update_product_cdn_info(
                                    product['product_id'],
                                    product['cdn_info'],
                                    self.bot.wxid
                                )
                                if not cdn_result:
                                    self.logger.warning(f"更新商品CDN信息失败: {product['product_name']}")
                            
                            success_count += 1
                        else:
                            self.logger.error(f"保存商品失败: {product['product_name']}")
                            
                    except Exception as e:
                        self.logger.error(f"保存商品时发生错误: {str(e)}")
                        continue
                
                # 更新内存中的索引
                self.product_info = self.db.get_all_products()
                self.product_index = self._build_product_index(self.product_info)
                
                self.logger.info(f"批量保存完成: 成功 {success_count}/{total_count}")
                return success_count > 0
            
        except Exception as e:
            self.logger.error(f"保存商品信息失败: {str(e)}")
            return False

    def _normalize_store_name(self, store_name):
        """
        标准化店铺名称
        
        Args:
            store_name (str): 原始店铺名称
            
        Returns:
            str: 标准化后的店铺名称
        """
        if not store_name:
            return ""
        # 移除空格、特殊字符和常见词
        normalized = store_name.replace(" ", "").replace("（新店）", "").replace("(新店)", "").replace("店", "").replace("泡泡玛特", "").replace("-", "").lower()
        # 移除末尾的"群"和数字
        normalized = re.sub(r'群\d*$|群$', '', normalized)
        return normalized

    def _normalize_product_name(self, product_name):
        """
        标准化商品名称
        
        Args:
            product_name (str): 原始商品名称
            
        Returns:
            str: 标准化后的商品名称
        """
        if not product_name:
            return ""
        # 移除空格、特殊字符和常见词
        # normalized = product_name.replace(" ", "").replace("-", "").lower()
        normalized = re.sub(r'[^\u4e00-\u9fa5a-zA-Z0-9]', '', product_name).strip().lower()
        if ("怪味" in normalized):
            normalized = normalized.replace("手办", "")
            normalized = normalized.replace("便利店", "便利")
        normalized = normalized.replace("衍生品", "")
        return normalized

    def _find_best_match_store(self, store_name):
        """
        查找最匹配的店铺名及其store_id
        
        Args:
            store_name (str): 店铺名称
            
        Returns:
            tuple: (store_name, store_id) 如果找到匹配的店铺，否则返回 (None, None)
        """
        if not store_name:
            return None, None
            
        try:
            # 标准化输入的店铺名称
            normalized_store_name = self._normalize_store_name(store_name)
            
            with self.db.get_connection() as conn:
                cursor = conn.cursor()
                
                # 获取所有店铺别名
                cursor.execute('''
                    SELECT s.store_id, s.original_name, sa.alias
                    FROM stores s
                    JOIN store_aliases sa ON s.store_id = sa.store_id
                ''')
                stores = cursor.fetchall()
                
                # 计算相似度
                best_match = None
                highest_ratio = 0
                
                for store_id, original_name, alias in stores:
                    # 使用标准化后的名称进行匹配
                    ratio = difflib.SequenceMatcher(None, normalized_store_name, alias).ratio()
                    if ratio > highest_ratio:
                        highest_ratio = ratio
                        best_match = (original_name, store_id)
                
                # 如果相似度大于0.66，认为是有效匹配
                if highest_ratio >= 0.66:
                    return best_match
                else:
                    if best_match:
                        # print(f"最相似店铺：{best_match[0]}，相似度：{highest_ratio}")
                        # 记录未匹配的店铺信息到日志文件
                        with open('store_error.log', 'a', encoding='utf-8') as f:
                            f.write(f"店铺匹配失败: 查询店铺={store_name}(标准化后={normalized_store_name}), 最相似店铺={best_match[0]}, 相似度={highest_ratio}\n")
                return None, None
                
        except Exception as e:
            self.logger.error(f"数据库查询店铺失败: {str(e)}")
            return None, None

    def _find_store_by_group_id(self, group_id):
        """
        通过群ID查找店铺信息
        
        Args:
            group_id (str): 群ID
            
        Returns:
            tuple: (store_name, store_id) 如果找到匹配的店铺，否则返回 (None, None)
        """
        if not group_id:
            return None, None
            
        try:
            with self.db.get_connection() as conn:
                cursor = conn.cursor()
                cursor.execute('''
                    SELECT s.original_name, s.store_id
                    FROM stores s
                    JOIN store_groups sg ON s.store_id = sg.store_id
                    WHERE sg.group_id = ?
                ''', (group_id,))
                result = cursor.fetchone()
                if result:
                    return result[0], result[1]
        except Exception as e:
            self.logger.error(f"通过群ID查找店铺失败: {str(e)}")
            
        return None, None

    def _get_product_mappings(self, data_source=None):
        """
        获取商品映射，用于商品匹配
        
        Args:
            data_source: 指定的数据源，如果为None则使用所有商品
            
        Returns:
            tuple: (product_map, alias_map) 
                - product_map: 商品ID到商品信息的映射
                - alias_map: 标准化名称到商品ID的映射
        """
        try:
            # 使用数据库方法获取产品数据
            all_products = self.db.get_all_products()
            
            # 构建商品名称到商品信息的映射和别名映射
            product_map = {}   # 用于存储商品ID到商品信息的映射
            alias_map = {}     # 用于存储标准化名称到商品ID的映射
            
            for product in all_products:
                product_id = product['product_id']
                product_map[product_id] = product
                
                # 使用标准化函数处理产品名称
                normalized_name = self._normalize_product_name(product['product_name'])
                alias_map[normalized_name] = product_id
                
                # 如果有昵称且不为空，使用昵称作为另一个匹配项
                if product.get('nick_name'):
                    normalized_nick = self._normalize_product_name(product['nick_name'])
                    alias_map[normalized_nick] = product_id
                    
            return product_map, alias_map
            
        except Exception as e:
            self.logger.error(f"获取商品映射失败: {str(e)}")
            traceback.print_exc()
            return {}, {}

    def _match_product(self, product_name, threshold=0.9, return_object=False, custom_data_source=None, enable_substring_match=False):
        """
        在指定数据源中查找匹配的商品
        
        Args:
            product_name (str): 商品名称
            threshold (float): 相似度阈值，默认为0.9
            return_object (bool): 是否直接返回匹配的商品对象，默认为False
            custom_data_source (list): 自定义数据源，如果提供则优先使用
            enable_substring_match (bool): 是否启用子串匹配，默认为False
        
        Returns:
            dict或object: 匹配结果字典或匹配的商品对象
        """
        # 初始化匹配结果
        match_result = {
            "商品名称": product_name,
            "匹配状态": "未匹配",
            "匹配位置": None
        }
        
        try:
            index = self.product_index
            
            if not index or not index["id_map"]:
                return None if return_object else match_result
            
            normalized_name = self._normalize_product_name(product_name)
            matched_info = None
            match_type = None
            similarity = 1.0
            match_position = None
            
            # 1. 精确匹配 - 使用索引
            if normalized_name in index["normalized_names"]:
                product_id = index["normalized_names"][normalized_name]
                matched_info = index["id_map"][product_id]
                match_type = "exact"
            
            # 2. 子串匹配
            elif enable_substring_match:
                longest_match_length = 0
                best_match_id = None
                best_match_position = (0, 0)
                
                # 使用索引中的标准化名称进行匹配
                for db_name, pid in index["normalized_names"].items():
                    if db_name in normalized_name:
                        # 数据库中的名称是输入名称的子串
                        match_len = len(db_name)
                        if match_len > longest_match_length:
                            longest_match_length = match_len
                            best_match_id = pid
                            start_pos = normalized_name.find(db_name)
                            best_match_position = (start_pos, start_pos + match_len)
                    # elif normalized_name in db_name:
                    #     # 输入名称是数据库中名称的子串
                    #     match_len = len(normalized_name)
                    #     if match_len > longest_match_length:
                    #         longest_match_length = match_len
                    #         best_match_id = pid
                    #         best_match_position = (0, match_len)
                
                if best_match_id:
                    matched_info = index["id_map"][best_match_id]
                    match_type = "substring"
                    match_position = best_match_position
            
            # 3. 相似度匹配
            if not matched_info:
                highest_ratio = 0
                best_match_id = None
                
                # 使用索引中的标准化名称进行相似度匹配
                for db_name, pid in index["normalized_names"].items():
                    ratio = difflib.SequenceMatcher(None, normalized_name, db_name).ratio()
                    if ratio > highest_ratio:
                        highest_ratio = ratio
                        best_match_id = pid
                
                # 应用阈值
                if highest_ratio > threshold and best_match_id:
                    matched_info = index["id_map"][best_match_id]
                    match_type = "similarity"
                    similarity = highest_ratio
            
            # 返回结果
            if matched_info:
                if return_object:
                    return matched_info
                    
                match_result.update({
                    "匹配状态": "已匹配",
                    "匹配商品ID": matched_info['product_id'],
                    "匹配商品名称": matched_info['product_name'],
                    "匹配商品对象": matched_info,
                    "相似度": similarity,
                    "匹配类型": match_type,
                    "匹配位置": match_position
                })
            
            return None if return_object else match_result
            
        except Exception as e:
            self.logger.error(f"匹配商品失败: {str(e)}")
            traceback.print_exc()
            return None if return_object else match_result

    def handle_notify_info(self, message):
        """处理NotifyInfo消息"""
        try:
            store_info = message.get('Data', {}).get('store')
            product_info = message.get('Data', {}).get('product')
            time_info = message.get('Data', {}).get('time')
            if store_info and product_info:
                #如果product_id在notify_product_ids中，则发送小程序
                if product_info.get('product_id') in self.notify_product_ids:
                    self._send_miniapp(product_info, store_info, self.bot.master_group, time=time_info)
                else:
                    self.logger.debug(f"商品{product_info.get('product_id')}不在notify_product_ids中，不发送小程序")
            else:
                self.logger.error("store_info或product_info为空")
        except Exception as e:
            self.logger.error(f"处理NotifyInfo消息失败: {str(e)}")
            

    def handle_message(self, message):
        """处理接收到的消息"""
        try:
            # 消息排重检查
            new_msg_id = message.get('Data', {}).get('NewMsgId')
            if new_msg_id:
                current_time = time.time()
                
                # 检查是否是重复消息
                if new_msg_id in self.message_cache:
                    last_time = self.message_cache[new_msg_id]
                    if current_time - last_time <= self.message_cache_ttl:
                        self.logger.debug(f"收到重复消息，已忽略: {new_msg_id}")
                        return
                
                # 定期清理缓存（每60秒检查一次）
                if current_time - self.last_cache_cleanup_time > self.cache_cleanup_interval:
                    self._cleanup_message_cache(current_time)
                    self.last_cache_cleanup_time = current_time
                
                # 添加新消息到缓存（使用OrderedDict自动维护插入顺序）
                self.message_cache[new_msg_id] = current_time
                
                # 如果缓存超过大小限制，移除最早的消息
                if len(self.message_cache) > self.message_cache_size:
                    self.message_cache.popitem(last=False)  # 移除第一个插入的项

            msg_type = message.get('TypeName')
            if not msg_type:
                self.logger.error("消息类型为空")
                return

            if msg_type == "AddMsg":
                self._handle_add_msg(message.get('Data', {}))
            elif not hasattr(self.bot, 'is_master') or not self.bot.is_master:
                return
            elif msg_type == "ModContacts":
                self._handle_mod_contacts(message.get('Data', {}))
            elif msg_type == "DelContacts":
                self._handle_del_contacts(message.get('Data', {}))
            elif msg_type == "Offline":
                self._handle_offline(message)
            elif msg_type == "FriendRequest":  # 新增处理好友请求
                self._handle_friend_request(message.get('Data', {}))
            else:
                self.logger.warning(f"未处理的消息类型: {msg_type}")

        except Exception as e:
            self.logger.error(f"处理消息失败: {str(e)}")

    def _handle_add_msg(self, data):
        """处理新消息"""
        try:
            # with open('data.json', 'a', encoding='utf-8') as f:
            #     json.dump(data, f, ensure_ascii=False)
            #     f.write('\n')  # 添加换行符以分隔每次追加的数据

            msg_type = data.get('MsgType')
            from_user = data.get('FromUserName', {}).get('string', '')
            to_user = data.get('ToUserName', {}).get('string', '')
            content = data.get('Content', {}).get('string', '')

            # 如果是副机器人，只处理文本消息
            if not hasattr(self.bot, 'is_master') or not self.bot.is_master:
                if msg_type == 1:  # 文本消息
                    self._handle_text_message(from_user, to_user, content)
                elif msg_type == 49:  # 各种类型的消息，需要进一步解析
                    self._handle_type_49_message(from_user, to_user, content)
                elif msg_type == 3:  # 图片消息
                    self._handle_image_message(from_user, to_user, content)
                return

            # 处理好友请求消息
            if msg_type == 37:  # 好友请求消息类型
                try:
                    # return #!!!
                    if to_user != self.bot.wxid:
                        return
                    # 解析xml内容
                    root = ET.fromstring(content)
                    
                    # 提取必要的参数
                    scene = int(root.get('scene', 0))  # 从根节点获取scene
                    v3 = root.get('encryptusername', '')
                    v4 = root.get('ticket', '')
                    fromnickname = root.get('fromnickname', '')
                    fromusername = root.get('fromusername', '')
                    
                    # 自动同意好友请求
                    self.bot.client.add_contacts(
                        app_id=self.bot.app_id,
                        scene=scene,  # 使用消息中的scene值
                        option=3,     # 3表示同意好友请求
                        v3=v3,
                        v4=v4,
                        content="您好"#\n对我说【加群】获取群邀请链接\n对我说【教程】查看教程"
                    )
                    self.logger.info(f"已自动同意好友请求 - 来自: {fromusername}")
                    
                    # 等待一段时间确保好友添加成功
                    time.sleep(2)
                    join_reply = "hi~" + fromnickname + "，很高兴为您服务\n全国线下门店通知分享群\n自助：#小程序://闲鱼/RmRsGfDjJNu7ZYA" 
                    # join_reply = "hi~" + fromnickname + "，很高兴为您服务，对我说：\n【加群】获取交流群链接\n【脚本】获取脚本链接\n【VIP监控】获取监控群链接\n使用【帮助】查看更多指令，机器人不回复消息，有问题请加群主" 
                    self.bot.client.post_text(self.bot.app_id, fromusername, join_reply)

                    
                    
                except ET.ParseError as e:
                    self.logger.error(f"解析好友请求XML失败: {str(e)}")
                except Exception as e:
                    self.logger.error(f"处理好友请求失败: {str(e)}")
                return
            
            elif msg_type == 1:  # 文本消息
                self._handle_text_message(from_user, to_user, content)
            elif msg_type == 3:  # 图片消息
                self._handle_image_message(from_user, to_user, content)
            elif msg_type == 34:  # 语音消息
                self._handle_voice_message(from_user, to_user, content)
            elif msg_type == 43:  # 视频消息
                self._handle_video_message(from_user, to_user, content)
            elif msg_type == 49:  # 各种类型的消息，需要进一步解析
                self._handle_type_49_message(from_user, to_user, content)
            elif msg_type == 10002:  # 系统消息
                self._handle_system_message(from_user, to_user, content)
            elif msg_type == 51:  # 查看
                print(to_user)
                if to_user.endswith("@chatroom"):
                    # 获取群名称并更新店铺信息
                    try:
                        # 获取群信息
                        group_info_response = self.bot.client.get_detail_info(self.bot.app_id, [to_user])
                        print(group_info_response)
                        if group_info_response and group_info_response.get('ret') == 200 and group_info_response.get('data'):
                            group_data = group_info_response['data'][0]
                            if group_data.get('remark') == None:
                                group_name = group_data.get('nickName', '')
                            else:
                                group_name = group_data.get('remark', '')
                            if group_name:
                                # 更新店铺信息
                                self._update_store_group_id(to_user, group_name)
                    except Exception as e:
                        self.logger.error(f"获取群信息失败: {str(e)}")
                
            else:
                # self.logger.info(f"未处理的消息类型: {msg_type}")
                pass

        except Exception as e:
            self.logger.error(f"处理AddMsg失败: {str(e)}")

    def _handle_text_message(self, from_user, to_user, content):
        """处理文本消息"""
        try:
            # 不处理自己发送的消息
            if from_user == self.bot.wxid:
                return
            
            # 检查是否是群消息
            if from_user.endswith("@chatroom"):
                # 解析群消息发送者
                sender_wxid = content.split(':', 1)[0] if ':' in content else from_user
                # 不处理自己发送的消息
                if sender_wxid == self.bot.wxid:
                    return

                # 提取实际消息内容
                actual_content = content.split(':', 1)[1].strip() if ':' in content else content

                if not hasattr(self.bot, 'is_master') or not self.bot.is_master:
                    if sender_wxid in self.bot.master_wxid:
                        self._handle_command(from_user, sender_wxid, actual_content)
                    return

                if sender_wxid.endswith("@openim"):
                    self._handle_openim_message(from_user, actual_content)
                    return
                
                # 检查是否@机器人或者来自特定群
                bot_nickname = getattr(self.bot, 'nickname', 'YBA_BOT')
                is_at_bot = f"@{bot_nickname}" in actual_content
                is_target_group = from_user in 通知群
                is_test_group = from_user == 通知测试群

                # 如果是@机器人的消息，尝试处理命令
                if is_at_bot:
                    # 判断是否是VIP用户
                    vip_info = self.db.get_vip_user(sender_wxid)
                    if (vip_info and vip_info.get('is_active') == 1):
                        # 移除@标记，获取纯消息内容
                        msg_content = actual_content.replace(f"@{bot_nickname}", "").strip()
                    else:
                        self.bot.client.post_text(self.bot.app_id, from_user, '功能仅向VIP开放')
                        return
                else:
                    msg_content = actual_content

                if is_at_bot or is_test_group:
                    target_group = from_user
                else:
                    target_group = self.bot.master_group

                if from_user in YBA交流群 or is_test_group or from_user in self.bot.master_group:
                    if self._handle_command(from_user, sender_wxid, msg_content):
                        return
                        
                if is_at_bot or is_target_group or is_test_group:
                    try:
                        if is_target_group:
                            # 检查消息是否以"扫下方二维码"开头，如果是则忽略
                            if msg_content.strip().startswith("扫下方二维码"):
                                return
                            # 移除msg_content的最后一行
                            if '\n' in msg_content:
                                lines = msg_content.split('\n')
                                msg_content = '\n'.join(lines[:-1])
                        
                        # 使用新的处理函数，传入消息内容和目标群
                        if is_at_bot:
                            response_data = self._process_store_products(msg_content, target_group, notify=False)
                        else:
                            response_data = self._process_store_products(msg_content, target_group, notify=True)
                            if response_data and response_data.get("匹配店铺名称"):
                                self.logger.debug(f"商品匹配结果: {response_data}")
                        if not response_data or not response_data.get("匹配店铺名称"):
                            self.logger.error("未找到匹配的店铺或商品")
                        
                    except Exception as e:
                        self.logger.error(f"处理店铺匹配失败: {str(e)}")
                        error_reply = "消息太多，忙不过来啦"
                        self.bot.client.post_text(self.bot.app_id, target_group, error_reply)
            else:
                sender_wxid = from_user
                # 特殊命令：确认广告图片设置
                if self.waiting_ad_image and content == "确认":
                    return self._confirm_ad_image(from_user)
                    
                # 处理私聊消息
                if self._handle_command(from_user, sender_wxid, content):
                    return
                
                # # 如果不是命令，尝试处理商品匹配
                # response_data = self._process_store_products(content, from_user, data_source=self.product_info)
                # if not response_data or not response_data.get("匹配店铺名称"):
                #     self.logger.error("未找到匹配的店铺或商品")
            
        except Exception as e:
            self.logger.error(f"处理文本消息失败: {str(e)}")

    def _handle_type_49_message(self, from_user, to_user, content):
        """处理类型49的消息（链接、文件等）"""
        try:
            # 不处理自己发送的消息
            if from_user == self.bot.wxid:
                return

            if from_user.endswith("@chatroom"):
                # 解析群消息发送者
                sender_wxid = content.split(':', 1)[0] if ':' in content else from_user
                # 不处理自己发送的消息
                if sender_wxid == self.bot.wxid:
                    return
                
                # 提取实际消息内容
                actual_content = content.split(':', 1)[1].strip() if ':' in content else content
            else:
                sender_wxid = from_user
                actual_content = content
            root = ET.fromstring(actual_content)
            appmsg = root.find('appmsg')
            if appmsg is not None:
                type_num = int(appmsg.find('type').text)
                
                # 处理群公告消息
                if type_num == 87:  # 群公告消息
                    try:
                        # 提取公告内容
                        text_announcement = appmsg.find('textannouncement')
                        if text_announcement is not None and text_announcement.text:
                            announcement_content = text_announcement.text
                            self.logger.info(f"收到群公告 - 来自: {from_user}, 内容: {announcement_content}")
                            
                            # 使用_handle_openim_message处理公告内容
                            self._handle_openim_message(from_user, announcement_content)
                            return
                    except Exception as e:
                        self.logger.error(f"处理群公告消息失败: {str(e)}")
                
                # 处理其他类型的消息
                title = appmsg.find('title').text if appmsg.find('title') is not None else ""
                
                type_map = {
                    5: "链接消息",
                    6: "文件消息",
                    33: "小程序消息",
                    87: "群公告消息",
                    2000: "转账消息",
                    2001: "红包消息"
                }
                
                msg_type = type_map.get(type_num, f"未知类型({type_num})")

                # 处理小程序消息
                if type_num == 33:  # 小程序消息
                    with open('data.json', 'a', encoding='utf-8') as f:
                        json.dump(content, f, ensure_ascii=False)
                        f.write('\n')  # 添加换行符以分隔每次追加的数据
                    weapp_info = appmsg.find('weappinfo')
                    if weapp_info is not None:
                        appid = weapp_info.find('appid').text
                        
                        # 判断是否是目标小程序
                        if appid == "wx9627eb7f4b1c69d5":
                            # 提取更多小程序信息
                            pagepath = weapp_info.find('pagepath').text if weapp_info.find('pagepath') is not None else ""
                            
                            # 获取商品图片链接
                            thumb_url = weapp_info.find('weapppagethumbrawurl')
                            image_url = thumb_url.text if thumb_url is not None else ""
                            
                            # 从标题中提取商品名称和店铺名称
                            product_name = title
                            store_name = None
                            
                            # 检查标题中是否包含店铺名称【】
                            store_match = re.search(r'【(.+?)】', title)
                            if store_match:
                                store_name = store_match.group(1)
                                # 移除店铺名称部分，剩下的作为商品名称
                                product_name = title.replace(f"【{store_name}】", "").strip()
                            
                            try:
                                # 解析pagepath中的参数
                                if "goodsSpuId" in pagepath and "storeID" in pagepath:
                                    # 提取商品ID和店铺ID
                                    import urllib.parse
                                    parsed_params = dict(param.split('=') for param in pagepath.split('?')[1].split('&'))
                                    goods_id = parsed_params.get('goodsSpuId')
                                    store_id = parsed_params.get('storeID')
                                    print(store_id,store_name)
                                    # 根据店铺ID设置shopType
                                    shop_type = "1" if store_id == "1" else "2"
                                    
                                    # 检查并添加店铺信息（无需判断来源）
                                    store_added = False
                                    if self.bot.is_master and store_id:
                                        store_added = self._check_and_add_store(store_id, store_name)
                                        
                                    # 检查并添加商品信息（需要判断来源）
                                    product_result = False
                                    if goods_id and product_name:
                                        # 添加商品信息时包含CDN信息
                                        product_result = self._check_and_add_product(goods_id, product_name, image_url)
                                        
                                        # 只有在新增商品或图片URL发生变化时才生成合并图片和更新CDN信息
                                        if product_result is True or product_result == "updated":
                                            # 生成合成图片
                                            merged_image_path = self._merge_images(image_url, goods_id)
                                            if merged_image_path:
                                                self.logger.info(f"成功生成合成图片: {merged_image_path}")
                                                # 获取图片的完整URL
                                                merged_image_url = self._get_image_url(goods_id)
                                                # 获取新生成图片的CDN信息
                                                new_cdn_info = self._get_cdn_info(merged_image_url)
                                                if new_cdn_info:
                                                    # 更新商品的CDN信息
                                                    self._update_product_cdn_info(goods_id, new_cdn_info)
                                                else:
                                                    self.logger.error("获取CDN信息失败")
                                            else:
                                                self.logger.error(f"生成合成图片失败: {goods_id}")
                                        else:
                                            self.logger.info(f"商品已存在且无需更新: {goods_id}")
                                    
                                    # 回复消息
                                    if sender_wxid in self.bot.master_wxid and to_user == self.bot.wxid:
                                        self.bot.client.post_text(self.bot.app_id, from_user, goods_id + " " + product_name)
                            except Exception as e:
                                self.logger.error(f"解析小程序参数失败: {str(e)}")

        except Exception as e:
            self.logger.error(f"解析type49消息失败: {str(e)}")

    def _handle_system_message(self, from_user, to_user, content):
        """处理系统消息"""
        try:
            if "撤回了一条消息" in content:
                self.logger.debug(f"消息撤回 - 来自: {from_user}")
                pass
            else:
                self.logger.debug(f"系统消息 - 来自: {from_user}, 内容: {content}")
                pass
        except Exception as e:
            self.logger.error(f"处理系统消息失败: {str(e)}")

    def _handle_mod_contacts(self, data):
        """处理联系人变更"""
        try:
            username = data.get('UserName', {}).get('string', '')
            nickname = data.get('NickName', {}).get('string', '')
            
            # 检查是否是群聊
            if username.endswith("@chatroom") and nickname:
                self.logger.debug(f"群信息变更: {username}, 群名: {nickname}")
                # 使用_update_store_group_id方法更新店铺信息
                self._update_store_group_id(username, nickname)
            else:
                self.logger.debug(f"联系人信息变更: {username}")
        except Exception as e:
            self.logger.error(f"处理联系人变更失败: {str(e)}")

    def _handle_del_contacts(self, data):
        """处理联系人删除"""
        username = data.get('UserName', {}).get('string', '')
        self.logger.debug(f"联系人删除: {username}")

    def _handle_offline(self, message):
        """处理掉线通知"""
        self.logger.warning(f"账号掉线: {message.get('Wxid')}")

    def _handle_friend_request(self, data):
        """处理好友请求"""
        try:
            from_user = data.get('FromUserName', {}).get('string', '')
            # 自动同意好友请求
            self.bot.client.add_contacts(self.bot.app_id, scene=1, option=1, v3='', v4='', content=from_user)
            self.logger.info(f"已自动同意好友请求来自: {from_user}")

        except Exception as e:
            self.logger.error(f"处理好友请求失败: {str(e)}")

    def _query_deepseek(self, content):
        """
        调用DeepSeek API进行查询
        返回解析后的店铺名称和商品列表
        """
        try:
            lines = content.split('\n')
            content = ''
            for line in lines:
                if "广告" in line or "没有补" in line or "没补" in line or "监控联系" in line:
                    continue
                content += line + "\n"
            print(content)
            # 调用 DeepSeek API
            response = self.deepseek_client.chat.completions.create(
                model="deepseek-chat",
                # model="deepseek-v3",
                messages=[
                    {"role": "system", "content": '从文本中提取"店铺名"（删除"POPMART"、"泡泡玛特"）、"时间"和"商品列表"（排除没补货的商品或广告商品，去除商品名后的线上线下少量相关信息），输出为JSON格式，包含店铺名、时间和商品列表。没有内容就返回空。'},
                    {"role": "user", "content": content}
                ],
                response_format={
                    'type': 'json_object'
                },
                temperature=0.1,
                top_p=0.2,
                presence_penalty=0.0,
                stream=False
            )
            
            # 获取回复内容
            reply = response.choices[0].message.content
            print(reply)
            # 解析JSON响应
            parsed_response = json.loads(reply)
            return parsed_response
            
        except Exception as e:
            self.logger.error(f"DeepSeek查询失败: {str(e)}")
            return None 

    def _merge_images(self, product_image_url, product_id, thumbnail = False):
        """
        将商品图片与二维码图片合并，并添加自适应水印，保存为JPEG格式到product_qr_img文件夹
        """
        try:
            # 下载商品图片
            if product_image_url.startswith('http'):
                response = requests.get(product_image_url)
                product_img = Image.open(io.BytesIO(response.content))
            elif product_image_url:
                product_img = Image.open("product_qr_img/demo.jpeg")
            
            # product_img = product_img.resize((750, 600), Image.Resampling.LANCZOS)
            # product_img = product_img.resize((175, 140), Image.Resampling.LANCZOS)

            # 确保图片模式正确
            if product_img.mode != 'RGBA':
                product_img = product_img.convert('RGBA')
            
            # 创建一个新图像，初始为商品图片的副本
            merged_img = product_img.copy()

            if False:
            # if self.bot.is_show_ads():
            
                # 打开二维码图片
                try:
                    if not product_image_url.startswith('http'):
                        qr_img = Image.open(product_image_url)
                    else:
                        qr_img = Image.open(f'{self.image_root}AD.png')
                except:
                    qr_img = Image.open('AD.png')
                
                if qr_img.mode != 'RGBA':
                    qr_img = qr_img.convert('RGBA')
                
                # 调整二维码大小，基于高度进行缩放并保持纵横比
                qr_height = int(product_img.height * 0.5)
                qr_width = int(qr_height * qr_img.width / qr_img.height)  # 保持纵横比
                qr_img = qr_img.resize((qr_width, qr_height), Image.Resampling.LANCZOS)
                
                # 计算二维码位置（居中）
                position = (
                    0,#(product_img.width - qr_size) // 2,
                    (product_img.height - qr_height) // 2
                )
                
                
                # 创建一个与二维码大小相同的区域
                region = merged_img.crop((
                    position[0],
                    position[1],
                    position[0] + qr_width,
                    position[1] + qr_height
                ))
                
                # 将区域转换为灰度图
                gray_region = region.convert('L')
                
                # 计算区域的平均亮度
                region_array = gray_region.getdata()
                avg_brightness = sum(region_array) / len(region_array)
                
                # 目标亮度（200-220之间）
                target_brightness = 210
                
                # 计算需要的亮度调整因子
                if avg_brightness < target_brightness:
                    # 计算需要增加的亮度比例
                    brightness_factor = target_brightness / avg_brightness
                    # 限制最大亮度因子为4.0
                    brightness_factor = min(brightness_factor, 4.0)
                    # 根据亮度差异调整对比度
                    if brightness_factor > 3.0:
                        contrast_factor = 0.3  # 深色背景，大幅降低对比度
                    elif brightness_factor > 2.0:
                        contrast_factor = 0.6  # 中等深色背景，降低对比度
                    else:
                        contrast_factor = 0.9  # 较浅背景，轻微降低对比度
                else:
                    # 如果已经足够亮，保持原样
                    brightness_factor = 1.0
                    contrast_factor = 0.8
                
                # 先降低对比度
                contrast_enhancer = ImageEnhance.Contrast(region)
                reduced_contrast = contrast_enhancer.enhance(contrast_factor)
                
                # 再增加亮度
                brightness_enhancer = ImageEnhance.Brightness(reduced_contrast)
                enhanced_region = brightness_enhancer.enhance(brightness_factor)
                
                # 将处理后的区域放回原图
                merged_img.paste(enhanced_region, position)
                
                # 在变淡的区域上粘贴二维码
                merged_img.paste(qr_img, position, qr_img)


            # else:
            if False:
                # 添加水印
                watermark_text = self.bot.wxid
                font_size = int(merged_img.width * 0.08)
                
                # 步骤1: 准备字体
                try:
                    font = ImageFont.truetype("front/MSYHBD.TTC", font_size)
                except:
                    try:
                        font = ImageFont.truetype("/usr/share/fonts/truetype/dejavu/DejaVuSans.ttf", font_size)
                    except:
                        font = ImageFont.load_default()
                
                # 步骤2: 计算文字大小和位置
                draw_temp = ImageDraw.Draw(Image.new("RGB", (1, 1)))
                text_bbox = draw_temp.textbbox((0, 0), watermark_text, font=font)
                text_width = text_bbox[2] - text_bbox[0]
                text_height = text_bbox[3] - text_bbox[1]
                
                # 增加额外空间以确保完整显示
                text_height = int(text_height * 1.5)
                
                # 计算边距和位置
                margin = int(min(merged_img.width, merged_img.height) * 0.02)
                x = int((merged_img.width - text_width) / 2)# - margin
                y = merged_img.height - text_height - margin
                
                # 步骤3: 截取背景图像区域
                background = merged_img.crop((x, y, x + text_width, y + text_height))
                background = background.convert("RGB")  # 确保为RGB模式
                
                # 步骤4: 创建空白的水印图像
                watermark_img = Image.new("RGBA", (text_width, text_height), (0, 0, 0, 0))
                
                # 步骤5: 创建文字蒙版
                mask_img = Image.new("L", (text_width, text_height), 0)
                mask_draw = ImageDraw.Draw(mask_img)
                mask_draw.text((0, 0), watermark_text, font=font, fill=255)
                
                # 步骤6: 逐像素处理
                bg_pixels = background.load()
                wm_pixels = watermark_img.load()
                mask_pixels = mask_img.load()
                
                for j in range(text_height):
                    for i in range(text_width):
                        if mask_pixels[i, j] > 128:
                            # 获取背景像素
                            r, g, b = bg_pixels[i, j]

                            # 计算反色
                            inv_r = 255 - r
                            inv_g = 255 - g
                            inv_b = 255 - b
                            
                            # 设置透明度
                            opacity = 255
                            
                            # 设置水印像素
                            wm_pixels[i, j] = (inv_r, inv_g, inv_b, opacity)
                
                # 步骤7: 将水印粘贴到原图
                merged_img.paste(watermark_img, (x, y), watermark_img)

            # 打开LOGO
            try:
                logo_img = Image.open(f'{self.image_root}PPMT.png')
            except:
                logo_img = Image.open('PPMT.png')
            merged_img.paste(logo_img, (20, 20))
            
            # 将图片转换为RGB模式（JPEG不支持RGBA）
            merged_img = merged_img.convert('RGB')

            if thumbnail:
                merged_img = merged_img.resize((175, 140), Image.Resampling.LANCZOS)
            
            # 保存合并后的图片，使用JPEG格式
            output_path = f'{self.image_root}{product_id}.jpeg'
            merged_img.save(output_path, format='JPEG', quality=100)
            
            return output_path
            
        except Exception as e:
            self.logger.error(f"合并图片失败: {str(e)}")
            return None

    def _get_image_url(self, product_id):
        """
        获取商品图片的外部访问URL
        
        Args:
            product_id: 商品ID
            
        Returns:
            str: 图片的外部访问URL
        """
        return f"{self.external_url}/{self.image_root}{product_id}.jpeg"

    def _check_and_generate_missing_images(self):
        """检查所有商品并生成缺失的合成图片，同时更新CDN信息"""
        try:
            self.logger.info("开始检查商品图片...")
            generated_count = 0
            skipped_count = 0
            updated_cdn_count = 0
            
            for product in self.product_info:
                product_id = product.get('product_id')
                product_img = product.get('product_img')
                
                if not product_id or not product_img:
                    skipped_count += 1
                    continue
                
                # 检查是否已存在合成图片
                image_path = f'{self.image_root}{product_id}.jpeg'
                need_update_cdn = False
                
                if not os.path.exists(image_path):
                    self.logger.debug(f"生成商品[{product.get('product_name', '未知商品')}]的合成图片")
                    merged_image_path = self._merge_images(product_img, product_id)
                    if merged_image_path:
                        generated_count += 1
                        need_update_cdn = True
                    else:
                        skipped_count += 1
                        continue
                
                # 检查CDN信息是否需要更新
                cdn_info = self._get_product_cdn_info(product_id)
                if need_update_cdn or not cdn_info or not cdn_info.get('fileId'):
                    # 获取图片的完整URL
                    image_url = self._get_image_url(product_id)
                    self.logger.debug(f"更新商品[{product.get('product_name', '未知商品')}]的CDN信息")
                    new_cdn_info = self._get_cdn_info(image_url)
                    if new_cdn_info:
                        self._update_product_cdn_info(product_id, new_cdn_info)
                        updated_cdn_count += 1
                else:
                    skipped_count += 1
            
            self.logger.info(f"图片检查完成：新生成 {generated_count} 张，已存在 {skipped_count} 张")
            self.logger.info(f"CDN信息更新：更新 {updated_cdn_count} 个")
            
        except Exception as e:
            self.logger.error(f"检查和生成图片时发生错误: {str(e)}")

    def _check_and_update_cdn_info(self):
        """检查并更新所有商品的CDN信息"""
        updated = False
        for product in self.product_info:
            product_id = product['product_id']
            product_img = product.get('product_img', '')
            
            # 获取CDN信息
            cdn_info = self._get_product_cdn_info(product_id)
            
            if product_img and (not cdn_info or not cdn_info.get('fileId')):
                new_cdn_info = self._get_cdn_info(product_img)
                if new_cdn_info:
                    self._update_product_cdn_info(product_id, new_cdn_info)
                    updated = True
        
        # 无需重新加载商品信息，因为商品信息中已不包含CDN信息

    def _get_product_cdn_info(self, product_id):
        """
        从内存缓存获取商品的 CDN 信息，如果缓存中不存在则从数据库获取
        
        Args:
            product_id (str): 商品ID
            wxid (str): 微信ID参数保留用于兼容性，但不再使用
            
        Returns:
            dict: CDN信息字典
        """
        # 先从缓存中获取
        cdn_info = self.cdn_cache.get(product_id)
        if cdn_info is not None:
            return cdn_info
            
        # 缓存中不存在，从数据库获取
        # cdn_info = self.db.get_product_cdn_info(product_id, self.bot.wxid)
        cdn_info = self.db.get_product_cdn_info(product_id, 'YBA-19990312')
        if cdn_info:
            # 更新缓存
            self.cdn_cache[product_id] = cdn_info
            
        return cdn_info

    def _update_product_cdn_info(self, product_id, cdn_info):
        """
        更新商品的 CDN 信息，同时更新数据库和内存缓存
        
        Args:
            product_id (str): 商品ID
            cdn_info (dict): CDN信息
            wxid (str): 微信ID参数保留用于兼容性，但不再使用
            
        Returns:
            bool: 更新是否成功
        """
        # 更新数据库
        if self.db.update_product_cdn_info(product_id, cdn_info, self.bot.wxid):
            # 更新成功后更新缓存
            self.cdn_cache[product_id] = cdn_info
            return True
        return False

    def _clear_cdn_cache(self):
        """清空 CDN 信息缓存"""
        self.cdn_cache.clear()

    def _check_image_send_rate(self):
        """
        检查是否可以发送图片，控制发送频率
        Returns:
            bool: 是否可以发送图片
            float: 需要等待的时间（秒）
        """
        current_time = time.time()
        
        # 检查是否需要重置时间窗口
        if current_time - self.image_send_window_start >= self.image_send_window:
            self.image_send_window_start = current_time
            self.image_send_count = 0
        
        # 检查发送间隔
        time_since_last_send = current_time - self.last_image_send_time
        if time_since_last_send < self.image_send_interval:
            return False, self.image_send_interval - time_since_last_send
        
        # 检查时间窗口内的发送限制
        if self.image_send_count >= self.image_send_limit:
            time_until_window_reset = self.image_send_window - (current_time - self.image_send_window_start)
            return False, time_until_window_reset
        
        return True, 0

    def _get_cdn_info(self, image_url):
        """
        通过发送图片消息获取CDN信息，包含频率控制
        """
        try:
            # 检查发送频率
            can_send, wait_time = self._check_image_send_rate()
            if not can_send:
                self.logger.info(f"发送频率过高，等待 {wait_time:.1f} 秒")
                time.sleep(wait_time)
            
            # 调用发送图片接口获取CDN信息
            response = self.bot.client.post_image(
                self.bot.app_id,
                self.bot.wxid,  # 发送给自己
                image_url
            )
            
            # 更新发送计数器和时间
            current_time = time.time()
            self.last_image_send_time = current_time
            self.image_send_count += 1
            
            if response and isinstance(response, dict):
                # 检查返回格式是否正确
                # with open('mp.log', 'a', encoding='utf-8') as f:
                #     f.write(str(response))
                if response.get('ret') == 200 and response.get('data'):
                    data = response['data']
                    cdn_info = {
                        "aesKey": data.get("aesKey", ""),
                        "fileId": data.get("fileId", ""),
                        "length": data.get("length", 0),
                        "width": data.get("width", 0),
                        "height": data.get("height", 0),
                        "md5": data.get("md5", "")
                    }
                    self.logger.info(f"成功获取CDN信息: {cdn_info}")

#                     # 转发图片到YBA-ZXY
#                     try:
#                         target_wxid = "YBA-ZXY"
#                         # 构建图片XML
#                         image_xml = f'''<?xml version="1.0"?>
# <msg>
#     <img aeskey="{cdn_info['aesKey']}" 
#          encryver="1" 
#          cdnthumbaeskey="{cdn_info['aesKey']}" 
#          cdnthumburl="{cdn_info['fileId']}" 
#          cdnthumblength="{cdn_info['length']}" 
#          cdnthumbheight="{cdn_info['height']}" 
#          cdnthumbwidth="{cdn_info['width']}" 
#          cdnmidheight="0" 
#          cdnmidwidth="0" 
#          cdnhdheight="0" 
#          cdnhdwidth="0" 
#          cdnmidimgurl="{cdn_info['fileId']}" 
#          length="{cdn_info['length']}" 
#          md5="{cdn_info['md5']}" />
#     <platform_signature></platform_signature>
#     <imgdatahash></imgdatahash>
# </msg>'''
#                         # 使用forward_image API转发图片
#                         self.bot.client.forward_image(self.bot.app_id, target_wxid, image_xml)
#                         self.logger.info(f"成功转发图片到 {target_wxid}")
#                     except Exception as e:
#                         self.logger.error(f"转发图片失败: {str(e)}")
                    
                    return cdn_info
                else:
                    self.logger.error(f"获取CDN信息返回格式错误: {response}")
            return None
        except Exception as e:
            self.logger.error(f"获取CDN信息失败: {str(e)}")
            return None

    def _generate_miniapp_xml(self, product_info, store_name, store_id, shop_type, notify_time = None, msg_id = None):
        """
        生成小程序XML
        Args:
            product_info: dict, 包含商品信息（product_name, product_id, product_img）
            store_name: str, 店铺名称
            store_id: str, 店铺ID
            shop_type: str, 商店类型
            notify_time: str, 通知时间
        Returns:
            str: 生成的XML字符串
        """
        try:
            if msg_id == None:
                msg_id = ""
            # 优先使用nick_name，如果不存在或为空则使用product_name
            product_name = product_info.get('nick_name') if product_info.get('nick_name') else product_info['product_name']
            product_id = product_info['product_id']
            original_image_url = product_info.get('product_img', '')

            # 计算字符串长度，字母和数字算1个单位，中文算2个单位
            # 使用单次遍历完成计算和截断
            cut_index = len(product_name)
            total_len = 0
            for i, char in enumerate(product_name):
                char_len = 2 if ord(char) > 127 else 1
                total_len += char_len
                if total_len > 16:
                    cut_index = i
                    break
                    
            if cut_index < len(product_name):
                product_name = product_name[:cut_index] + '...'


            
            # 从内存缓存获取CDN信息
            cdn_info = self._get_product_cdn_info(product_id) or {}
            
            notify_time = notify_time if notify_time else time.strftime("%H:%M:%S", time.localtime())
            
            # 提取CDN信息，使用get方法设置默认值避免KeyError
            cdn_data = {
                'fileId': cdn_info.get('fileId', ''),
                'md5': cdn_info.get('md5', ''),
                'length': cdn_info.get('length', 0),
                'height': cdn_info.get('height', 0),
                'width': cdn_info.get('width', 0),
                'aesKey': cdn_info.get('aesKey', '')
            }

            xml_template = f"""<?xml version="1.0"?>
<msg>
    <appmsg appid="" sdkver="0">
        <title><![CDATA[【{store_name}】{product_name}\n{notify_time}]]></title>
        <des><![CDATA[{self.bot.wxid}]]></des>
        <type>33</type>
        <showtype>0</showtype>
        <appattach>
            <attachid />
            <cdnthumburl>{cdn_data['fileId']}</cdnthumburl>
            <cdnthumbmd5>{cdn_data['md5']}</cdnthumbmd5>
            <cdnthumblength>{cdn_data['length']}</cdnthumblength>
            <cdnthumbheight>{cdn_data['height']}</cdnthumbheight>
            <cdnthumbwidth>{cdn_data['width']}</cdnthumbwidth>
            <cdnthumbaeskey>{cdn_data['aesKey']}</cdnthumbaeskey>
            <aeskey>{cdn_data['aesKey']}</aeskey>
            <encryver>1</encryver>
            <fileext>jpg</fileext>
            <islargefilemsg>0</islargefilemsg>
        </appattach>
        <androidsource>0</androidsource>
        <sourceusername>gh_9d8815c84ea4@app</sourceusername>
        <sourcedisplayname><![CDATA[{store_name}-{notify_time}]]></sourcedisplayname>
        <md5>{cdn_data['md5']}</md5>
        <weappinfo>
            <pagepath><![CDATA[packages/storeGoGoodsDetail/pages/goodsDetail/goodsDetail.html?goodsSpuId={product_id}&storeID={store_id}&shopType={shop_type}&moduleName=分享卡片]]></pagepath>
            <username>gh_9d8815c84ea4@app</username>
            <appid>wxc463b1f7bf8f4bf7</appid>
            <weappiconurl><![CDATA[http://cloud.yaoboan.com:2533/product_qr_img/icon.jpg]]></weappiconurl>
            <weapppagethumbrawurl><![CDATA[{original_image_url}]]></weapppagethumbrawurl>
            <videopageinfo>
                <thumbwidth>500</thumbwidth>
                <thumbheight>400</thumbheight>
                <fromopensdk>0</fromopensdk>
            </videopageinfo>
        </weappinfo>
	</appmsg>
</msg>"""

            return xml_template
        except Exception as e:
            self.logger.error(f"生成小程序XML失败: {str(e)}")
            return None

    def _generate_my_miniapp_xml(self, product_info, store_name, store_id, shop_type, notify_time = None, msg_id = None):
        """
        生成小程序XML
        Args:
            product_info: dict, 包含商品信息（product_name, product_id, product_img）
            store_name: str, 店铺名称
            store_id: str, 店铺ID
            shop_type: str, 商店类型
            notify_time: str, 通知时间
        Returns:
            str: 生成的XML字符串
        """
        try:
            if msg_id == None:
                msg_id = ""
            # 优先使用nick_name，如果不存在或为空则使用product_name
            product_name = product_info.get('nick_name') if product_info.get('nick_name') else product_info['product_name']
            product_id = product_info['product_id']
            original_image_url = product_info.get('product_img', '')
            ps_id = self._encode_shop_goods(product_id, store_id)
            print(f"ps_id: {ps_id}")
            # store_name = store_name[:2] + "***"

            # 计算字符串长度，字母和数字算1个单位，中文算2个单位
            # 使用单次遍历完成计算和截断
            cut_index = len(product_name)
            total_len = 0
            for i, char in enumerate(product_name):
                char_len = 2 if ord(char) > 127 else 1
                total_len += char_len
                if total_len > 16:
                    cut_index = i
                    break
                    
            if cut_index < len(product_name):
                product_name = product_name[:cut_index] + '...'

            # 从内存缓存获取CDN信息
            cdn_info = self._get_product_cdn_info(product_id) or {}
            
            notify_time = notify_time if notify_time else time.strftime("%H:%M:%S", time.localtime())
            
            # 提取CDN信息，使用get方法设置默认值避免KeyError
            cdn_data = {
                'fileId': cdn_info.get('fileId', ''),
                'md5': cdn_info.get('md5', ''),
                'length': cdn_info.get('length', 0),
                'height': cdn_info.get('height', 0),
                'width': cdn_info.get('width', 0),
                'aesKey': cdn_info.get('aesKey', '')
            }

            xml_template = f"""<?xml version="1.0"?>
<msg>
    <appmsg appid="" sdkver="0">
        <title><![CDATA[{msg_id}#@{product_name}\n【{store_name}】{notify_time}]]></title>
        <des><![CDATA[{self.bot.wxid}]]></des>
        <type>33</type>
        <showtype>0</showtype>
        <appattach>
            <attachid />
            <cdnthumburl>{cdn_data['fileId']}</cdnthumburl>
            <cdnthumbmd5>{cdn_data['md5']}</cdnthumbmd5>
            <cdnthumblength>{cdn_data['length']}</cdnthumblength>
            <cdnthumbheight>{cdn_data['height']}</cdnthumbheight>
            <cdnthumbwidth>{cdn_data['width']}</cdnthumbwidth>
            <cdnthumbaeskey>{cdn_data['aesKey']}</cdnthumbaeskey>
            <aeskey>{cdn_data['aesKey']}</aeskey>
            <encryver>1</encryver>
            <fileext />
            <islargefilemsg>0</islargefilemsg>
        </appattach>
        <androidsource>0</androidsource>
        <sourceusername>gh_84d259b4887f@app</sourceusername>
        <sourcedisplayname><![CDATA[{store_name}-{notify_time}]]></sourcedisplayname>
        <md5>{cdn_data['md5']}</md5>
        <weappinfo>
            <pagepath><![CDATA[pages/share-entry/index.html?shopGoods={ps_id}]]></pagepath>
            <username>gh_84d259b4887f@app</username>
            <appid>wxc463b1f7bf8f4bf7</appid>
            <weappiconurl><![CDATA[http://cloud.yaoboan.com:2533/product_qr_img/icon.jpg]]></weappiconurl>
            <weapppagethumbrawurl><![CDATA[{original_image_url}]]></weapppagethumbrawurl>
            <videopageinfo>
                <thumbwidth>500</thumbwidth>
                <thumbheight>400</thumbheight>
                <fromopensdk>0</fromopensdk>
            </videopageinfo>
        </weappinfo>
	</appmsg>
</msg>"""
            return xml_template
        except Exception as e:
            self.logger.error(f"生成小程序XML失败: {str(e)}")
            return None



    def _handle_image_message(self, from_user, to_user, content):
        """处理图片消息"""
        try:
            # 先检查是否是在等待接收广告图片
            if self.waiting_ad_image:
                # 检查发送者是否为要求设置广告的用户
                if from_user == self.ad_image_sender:
                    # 从XML中提取图片信息
                    image_xml = content
                    # 处理广告图片
                    self._process_ad_image(from_user, image_xml)
                    return True
                else:
                    # 忽略不是来自同一发送者的图片
                    self.logger.info(f"忽略非命令发送者的广告图片: {from_user}，预期发送者: {self.ad_image_sender}")
                    return False
            
            # 处理图片消息的逻辑
            # with open('mp.log', 'a', encoding='utf-8') as f:
            #     f.write(content)
            pass
        except Exception as e:
            self.logger.error(f"处理图片消息失败: {str(e)}")
            
    def _handle_voice_message(self, from_user, to_user, content):
        """处理语音消息"""
        try:
            # 处理语音消息的逻辑
            pass
        except Exception as e:
            self.logger.error(f"处理语音消息失败: {str(e)}")

    def _handle_video_message(self, from_user, to_user, content):
        """处理视频消息"""
        try:
            # 处理视频消息的逻辑
            pass
        except Exception as e:
            self.logger.error(f"处理视频消息失败: {str(e)}")

    def _extract_and_match_product_info(self, message_text, store_name = None):
        """
        从消息中提取店铺名、时间和商品信息，并查找匹配的商品
        
        Args:
            message_text (str): 接收到的消息文本
            store_name (str): 店铺名称，如果已知则直接使用
        
        Returns:
            dict: 包含提取和匹配结果的字典
        """
        # 初始化结果字典
        result = {
            "店铺名": None,
            "时间": None,
            "商品列表": [],
            "匹配结果": []
        }
        
        try:
            if store_name == None:
                # 提取店铺名和时间
                # 假设店铺名在消息的开头，后面跟着多个破折号，然后是监控信息和时间
                store_time_pattern = r'^(.*?)[-]+.*?(\d{1,2}:\d{1,2}:\d{1,2})'
                store_time_match = re.search(store_time_pattern, message_text)
                if store_time_match:
                    # 提取店铺名，去除可能的空格
                    result["店铺名"] = store_time_match.group(1).strip()
                    
                    # 提取时间
                    result["时间"] = store_time_match.group(2)
                    
                    # 删除已提取的内容
                    message_text = re.sub(store_time_pattern, '', message_text, 1).strip()
                else:
                    # 使用正则表达式提取店铺名
                    store_pattern = r'^(?:POP MART|泡泡玛特)\s*(.*?)\s'
                    store_match = re.search(store_pattern, message_text, re.MULTILINE)
                    if store_match:
                        result["店铺名"] = store_match.group(1).strip()
                        # 删除已提取的店铺名部分
                        message_text = re.sub(store_pattern, '', message_text, 1).strip()
                    else:
                        # 尝试以第一行匹配店铺名
                        result["店铺名"] = message_text.split('\n')[0].strip()
                        
                    # 尝试提取时间（如果之前没有提取到）
                    time_pattern = r'(\d{1,2}:\d{1,2}(?::\d{1,2})?)'
                    time_match = re.search(time_pattern, message_text)
                    if time_match:
                        result["时间"] = time_match.group(1)
                        # 删除已提取的时间
                        message_text = re.sub(time_pattern, '', message_text, 1).strip()
            else:
                result["店铺名"] = store_name
                result["时间"] = time.strftime("%H:%M:%S", time.localtime())
            
            # 提取商品名称
            # 按行分割消息
            lines = message_text.split('\n')
            
            # 提取所有可能的商品行
            for line in lines:
                line = line.strip()
                # 跳过空行或只包含表情符号的行
                if not line or line.startswith('POP MART'):
                    continue
                # # 排除明确的非商品信息，如"广告是广告没有补"
                # if "广告" in line or "没有补" in line or "没补" in line or "监控联系" in line:
                #     continue
                # 如果商品名称非空，添加到结果中并进行递归匹配
                product_name = self._normalize_product_name(line)
                if len(product_name) > 2:
                    self._recursive_match_products(product_name, result)
        
        except Exception as e:
            self.logger.error(f"提取和匹配商品信息失败: {str(e)}")
            result["错误"] = str(e)
        
        return result

    def _recursive_match_products(self, text, result):
        """
        递归匹配商品名称，同时处理子串匹配和相似度匹配
        
        Args:
            text (str): 要匹配的文本
            result (dict): 存储匹配结果的字典
        """
        # 如果文本为空，直接返回
        if not text.strip():
            return
        # 排除明确的非商品信息，如"广告是广告没有补"
        if "广告" in text or "没有补" in text or "没补" in text or "监控联系" in text:
            return
        # 在递归匹配前先去除空格，保持原始文本用于后续处理
        original_text = text
        text_no_space = self._normalize_product_name(text)
            
        # 尝试匹配商品，传入已去除空格的文本和数据源
        match_result = self._match_product(
            text_no_space,
            threshold=0.9,
            custom_data_source=self.product_info,
            enable_substring_match=True
        )
        
        # 如果匹配成功
        if match_result["匹配状态"] == "已匹配":
            # 添加匹配结果，保留原始文本作为显示
            match_result["提取商品名称"] = original_text
            result["匹配结果"].append(match_result)
            result["商品列表"].append(match_result["匹配商品名称"])
            
            # 如果是子串匹配，处理剩余文本
            if match_result["匹配类型"] == "substring" and match_result.get("匹配位置"):
                start, end = match_result["匹配位置"]
                # 提取剩余文本并递归匹配
                remaining_text = text_no_space[:start].strip() + " " + text_no_space[end:].strip()
                remaining_text = remaining_text.strip()
                
                if len(remaining_text) > 2:
                    # 检查剩余文本是否包含商品关键词
                    if (any(keyword in remaining_text for keyword in product_keywords) or 
                        any(series in remaining_text for series in series_keywords)):
                        self._recursive_match_products(remaining_text, result)
            
            # 如果是相似度匹配且相似度不是100%，可能还有其他商品
            elif match_result["匹配类型"] == "similarity" and match_result.get("相似度", 1.0) < 1.0:
                # 从原文本中移除已匹配的部分
                matched_name = self._normalize_product_name(match_result["匹配商品名称"])
                remaining_text = text_no_space
                
                # 尝试找到匹配商品名称的位置
                match_pos = remaining_text.find(matched_name)
                if match_pos != -1:
                    # 提取剩余文本
                    remaining_text = remaining_text[:match_pos].strip() + " " + remaining_text[match_pos + len(matched_name):].strip()
                    remaining_text = remaining_text.strip()
                    
                    if len(remaining_text) > 2:
                        # 检查剩余文本是否包含商品关键词
                        if (any(keyword in remaining_text for keyword in product_keywords) or 
                            any(series in remaining_text for series in series_keywords)):
                            self._recursive_match_products(remaining_text, result)
        else:
            # 如果没有找到匹配，检查是否包含分隔符
            separators = ["、", "，", ",", "+", ";", "；"]
            for separator in separators:
                if separator in original_text:
                    # 按分隔符拆分文本
                    parts = [p.strip() for p in original_text.split(separator) if p.strip()]
                    # 对每个部分进行递归匹配
                    for part in parts:
                        if len(part) > 2:
                            # 检查部分文本是否包含商品关键词
                            if (any(keyword in part.lower() for keyword in product_keywords) or 
                                any(series in part.lower() for series in series_keywords)):
                                self._recursive_match_products(part, result)
                    return
            
            # 如果没有分隔符且文本不为空，检查是否包含商品关键词
            if len(original_text.strip()) > 2:
                if (any(keyword in original_text.lower() for keyword in product_keywords) or 
                    any(series in original_text.lower() for series in series_keywords)):
                    result["商品列表"].append(original_text)
                    match_result["商品名称"] = original_text
                    result["匹配结果"].append(match_result)
    
    def _process_store_products(self, content, target_group=通知测试群, notify=True, group_id=None):
        """
        处理店铺商品信息
        
        Args:
            content (str): 消息内容
            target_group (str): 目标群ID，默认为通知测试群
            group_id (str): 消息来源群ID，用于查找店铺信息，默认为None
            
        Returns:
            dict: 包含处理结果的字典
        """
        if "已售罄" in content:
            return
            
        # 缓存原始消息内容，分配一个简单的ID
        msg_id = self.current_message_id
        self.original_message_cache[msg_id] = content
        
        # 更新当前消息ID，确保在1-99之间循环
        self.current_message_id = (self.current_message_id % self.max_message_id) + 1
        
        response_data = {
            "匹配店铺名称": "",
            "匹配店铺ID": "",
            "商品匹配结果": [],
            "消息ID": msg_id  # 添加消息ID到响应数据中
        }
        
        try:
            
            # 如果提供了group_id，首先通过群ID查找店铺信息
            matched_store_name = None
            store_id = None
            matched_result = None
            
            if group_id:
                matched_store_name, store_id = self._find_store_by_group_id(group_id)
                if matched_store_name and store_id:
                    self.logger.info(f"通过群ID {group_id} 找到匹配的店铺: {matched_store_name}({store_id})")
                    matched_result = self._extract_and_match_product_info(content, matched_store_name)
            else:
                # 如果没有提供group_id，尝试从消息中提取店铺和商品信息
                matched_result = self._extract_and_match_product_info(content)
                # print(matched_result)
                if matched_result.get("店铺名") and matched_result.get("商品列表"):
                    # 查找匹配的店铺
                    matched_store_name, store_id = self._find_best_match_store(matched_result["店铺名"])

            if not matched_store_name or not store_id or not matched_result.get("商品列表"):
                # 如果提取失败，尝试使用DeepSeek
                query_result = self._query_deepseek(content)
                if not query_result:
                    self.logger.error("DeepSeek查询失败")
                    return response_data
                
                if not matched_store_name or not store_id:
                    store_name = query_result.get("店铺名")
                    if store_name:
                        matched_store_name, store_id = self._find_best_match_store(self._normalize_store_name(store_name))
                if not matched_result.get("商品列表"):
                    # 使用递归匹配处理DeepSeek返回的商品列表
                    deepseek_result = {"商品列表": [], "匹配结果": []}
                    for product_name in query_result.get("商品列表", []):
                        if len(product_name) > 2:
                            # print(self._normalize_product_name(product_name))
                            self._recursive_match_products(self._normalize_product_name(product_name), deepseek_result)
                    matched_result = deepseek_result
            
            # 如果没有找到匹配的店铺或商品，返回空结果
            if not matched_store_name or not store_id or not matched_result.get("商品列表"):
                self.logger.debug(f"未找到匹配的店铺或商品: 店铺={matched_store_name}, 店铺ID={store_id}, 商品数量={len(matched_result.get('商品列表', [])) if matched_result else 0}")
                return response_data
            
            # 更新响应数据
            response_data["匹配店铺名称"] = matched_store_name
            response_data["匹配店铺ID"] = store_id
            
            # 创建店铺信息对象
            store_info = {
                "store_name": matched_store_name,
                "store_id": store_id
            }
            
            # print(self.notify_product_ids)
            # print(matched_result)
            # 处理匹配结果
            for match_result in matched_result.get("匹配结果", []):
                if match_result["匹配状态"] == "已匹配":
                    matched_product = match_result["匹配商品对象"]
                    product_id = matched_product['product_id']
                    # print(product_id)
                    # 只处理在通知ID列表中的商品
                    if (not notify) or (product_id in self.notify_product_ids):
                        # 发送小程序
                        self._send_miniapp(matched_product, store_info, target_group, time=matched_result.get("时间"), msg_id=msg_id)
                    
                    if target_group == self.bot.master_group or (isinstance(target_group, list) and set(target_group) == set(self.bot.master_group)):
                        # 转发到其他副机器人的端口上
                        if hasattr(self.bot, 'is_master') and self.bot.is_master and self.bot.bots_info:
                            forward_data = {
                                "TypeName": "NotifyInfo",
                                "Wxid": "ALL",
                                "Data": {
                                    "store": store_info,
                                    "product": matched_product,
                                    "time": matched_result.get("时间")
                                }
                            }
                            
                            # 使用新的广播函数发送数据
                            self._broadcast_to_sub_bots(forward_data)

                response_data["商品匹配结果"].append(match_result)
            
            return response_data
            
        except Exception as e:
            self.logger.error(f"处理店铺和商品信息失败: {str(e)}")
            return response_data

    def _handle_openim_message(self, from_user, content):
        """处理openim消息"""
        try:
            # 检查消息第一行是否包含"欢迎进入"
            first_line = content.split('\n')[0] if '\n' in content else content
            if "欢迎进入" in first_line:
                self.logger.debug(f"跳过欢迎消息: {first_line}")
                return
            if "监测提示" in first_line:
                self.logger.debug(f"跳过提醒消息: {first_line}")
                return
            
            # 使用新的处理函数，传入消息内容、目标群和来源群ID
            response_data = self._process_store_products(content, self.bot.master_group, notify=True, group_id=from_user)
            
        except Exception as e:
            self.logger.error(f"处理openim消息失败: {str(e)}")

    def _check_miniapp_send_buffer(self, store_id, product_id, target_group=None):
        """
        检查是否应该发送小程序，优化的缓冲区管理
        
        Args:
            store_id: str, 店铺ID
            product_id: str, 商品ID
            target_group: str, 目标群ID，如果指定则与群ID一起作为缓冲键
            
        Returns:
            bool: 如果应该发送返回True，否则返回False
        """
        current_time = time.time()
        # 如果指定了目标群，将群ID加入缓冲键
        buffer_key = f"{store_id}_{product_id}" if not target_group else f"{store_id}_{product_id}_{target_group}"
        
        # 检查是否需要清理缓冲区（周期性清理）
        need_clean = False
        if current_time - self.last_buffer_clean_time > self.buffer_clean_interval:
            need_clean = True
        # 当缓冲区大小超过阈值时也进行清理
        elif len(self.miniapp_send_buffer) > self.buffer_clean_threshold:
            need_clean = True
        
        # 执行清理
        if need_clean:
            self._clean_expired_buffer(current_time)
            self.last_buffer_clean_time = current_time
        
        # 检查是否在缓冲期内
        if buffer_key in self.miniapp_send_buffer:
            self.logger.debug(f"小程序发送被缓冲过滤: 店铺ID={store_id}, 商品ID={product_id}, 群ID={target_group}")
            return False
        
        # 添加到缓冲区，最新的添加到末尾
        self.miniapp_send_buffer[buffer_key] = current_time
        return True

    def _clean_expired_buffer(self, current_time=None):
        """
        清理过期的缓冲记录
        
        Args:
            current_time: float, 当前时间戳，如果为None则获取当前时间
        """
        if current_time is None:
            current_time = time.time()
            
        # 优化：使用列表推导快速找出过期的键
        expired_keys = [
            key for key, timestamp in self.miniapp_send_buffer.items() 
            if current_time - timestamp > self.miniapp_buffer_window
        ]
        
        # 删除过期的记录
        for key in expired_keys:
            del self.miniapp_send_buffer[key]
        
        if expired_keys:
            self.logger.debug(f"已清理 {len(expired_keys)} 个过期的小程序发送记录")

    def _get_buffer_stats(self):
        """
        获取缓冲区统计信息，用于监控和调试
        
        Returns:
            dict: 包含缓冲区统计信息的字典
        """
        current_time = time.time()
        active_items = 0
        expired_items = 0
        
        for timestamp in self.miniapp_send_buffer.values():
            if current_time - timestamp <= self.miniapp_buffer_window:
                active_items += 1
            else:
                expired_items += 1
        
        return {
            "total_items": len(self.miniapp_send_buffer),
            "active_items": active_items,
            "expired_items": expired_items,
            "time_since_last_clean": current_time - self.last_buffer_clean_time,
            "buffer_window": self.miniapp_buffer_window,
            "clean_interval": self.buffer_clean_interval
        }

    def _send_miniapp(self, matched_product, matched_store, target_group=None, time = None, msg_id = None):
        """
        发送小程序卡片
        
        Args:
            matched_product: dict, 商品信息
            matched_store: dict, 店铺信息
            target_group: str或list, 目标群组，可以是单个群ID或群ID列表
            time: str, 通知时间
            
        Returns:
            bool: 是否发送成功
        """
        try:
            if target_group == None:
                target_groups = self.bot.master_group
            elif isinstance(target_group, list):
                target_groups = target_group
            else:
                target_groups = [target_group]
                
            if not target_groups:
                self.logger.error("发送小程序失败: 未指定目标群组")
                return False
                
            store_name = matched_store.get('store_name')
            store_id = matched_store.get('store_id')
            
            if not store_name or not store_id or not matched_product:
                self.logger.error("发送小程序失败: 店铺或商品信息不完整")
                return False

            # 获取CDN信息
            product_id = matched_product['product_id']
            cdn_info = self._get_product_cdn_info(product_id)
            
            if not cdn_info or not cdn_info.get('fileId'):
                # 如果没有CDN信息，先重新生成合成图片
                product_img = matched_product.get('product_img', '')
                if not product_img:
                    self.logger.error(f"商品图片URL为空: {matched_product['product_name']}")
                    return False

                # 重新生成合成图片
                merged_image_path = self._merge_images(product_img, product_id)
                if not merged_image_path:
                    self.logger.error(f"生成合成图片失败: {matched_product['product_name']}")
                    return False

                # 获取合成图片的完整URL
                image_url = self._get_image_url(product_id)
                
                # 获取新的CDN信息
                new_cdn_info = self._get_cdn_info(image_url)
                if new_cdn_info:
                    # 更新商品的CDN信息
                    self._update_product_cdn_info(product_id, new_cdn_info)
                    cdn_info = new_cdn_info
                else:
                    self.logger.error(f"获取CDN信息失败: {matched_product['product_name']}")
                    return False
            
            if cdn_info:
                # 根据店铺ID设置shopType
                shop_type = "1" if store_id == "1" else "2"
                
                success_count = 0
                for group in target_groups:
                    # 先检查是否应该发送小程序（缓冲过滤）
                    if not self._check_miniapp_send_buffer(store_id, matched_product['product_id'], group):
                        self.logger.debug(f"小程序发送被缓冲过滤: 店铺={store_name}({store_id}), 商品={matched_product['product_name']}({matched_product['product_id']}), 微信ID={group}")
                        continue
                        
                    if group in YBA交流群 or group == 小窝群:
                        # 生成小程序XML
                        xml_content = self._generate_miniapp_xml(matched_product, store_name, store_id, shop_type, time, msg_id)
                    else:
                        xml_content = self._generate_my_miniapp_xml(matched_product, store_name, store_id, shop_type, time, msg_id)
                    
                    if xml_content:
                        # 使用转发接口发送小程序
                        self.bot.client.forward_mini_app(
                            self.bot.app_id,
                            group,
                            xml_content,
                            ''
                        )
                        success_count += 1
                    else:
                        self.logger.error(f"生成小程序XML失败: 目标群={group}")
                
                return success_count > 0
            else:
                self.logger.error(f"获取商品CDN信息失败: {matched_product['product_name']}")
                return False
        except Exception as e:
            self.logger.error(f"发送小程序失败: {str(e)}")
            return False

    def _broadcast_to_sub_bots(self, data: dict, port: int = 12580) -> bool:
        """通过UDP广播发送数据到所有副机器人
        
        Args:
            data: 要发送的数据字典
            port: UDP广播端口，默认12580
            
        Returns:
            bool: 发送是否成功
        """
        try:
            # 将数据转换为JSON字符串
            json_data = json.dumps(data)
            
            # 创建UDP socket
            sock = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
            sock.setsockopt(socket.SOL_SOCKET, socket.SO_BROADCAST, 1)
            
            try:
                # 如果数据大于UDP包大小限制(65507字节)，进行分片发送
                MAX_UDP_SIZE = 65507
                data_bytes = json_data.encode('utf-8')
                total_chunks = (len(data_bytes) + MAX_UDP_SIZE - 1) // MAX_UDP_SIZE
                
                # 添加分片信息到每个包
                for i in range(total_chunks):
                    chunk = data_bytes[i * MAX_UDP_SIZE : (i + 1) * MAX_UDP_SIZE]
                    chunk_data = {
                        "chunk_id": i,
                        "total_chunks": total_chunks,
                        "data": base64.b64encode(chunk).decode('utf-8')
                    }
                    chunk_json = json.dumps(chunk_data)
                    
                    # 发送到广播地址
                    sock.sendto(chunk_json.encode('utf-8'), ('<broadcast>', port))
                    time.sleep(0.01)  # 短暂延迟，避免包丢失
                
                return True
                
            finally:
                sock.close()
                
        except Exception as e:
            self.logger.error(f"UDP广播发送失败: {str(e)}")
            return False

    def _update_store_group_id(self, group_id, group_name):
        """更新店铺的群组ID"""
        store_name, store_id = self._find_best_match_store(group_name)
        if store_id:
            self._check_and_add_store(store_id, store_name, group_id)
            return True
        return False

    def _build_product_index(self, product_list):
        """
        构建商品索引，提高匹配效率
        
        Args:
            product_list: 商品列表
            
        Returns:
            dict: 包含多种索引的字典
        """
        if not product_list:
            return {"id_map": {}, "normalized_names": {}}
        
        index = {
            "id_map": {},      # 商品ID到商品对象的映射
            "normalized_names": {}  # 标准化的商品名称到商品ID的映射
        }
        
        for product in product_list:
            product_id = product.get("product_id")
            if not product_id:
                continue
                
            # 创建ID索引
            index["id_map"][product_id] = product
            
            # 创建名称索引
            product_name = product.get("product_name", "")
            if product_name:
                # 创建标准化名称索引（去除空格和小写）
                normalized_name = self._normalize_product_name(product_name)
                index["normalized_names"][normalized_name] = product_id
        
        return index

    def _update_indexes(self):
        """更新商品索引，在商品数据变更后调用"""
        self.product_index = self._build_product_index(self.product_info)

    def _handle_command(self, from_user, sender_wxid, command):
        """处理命令消息"""
        try:
            # 移除可能的@标记
            command = command.replace(f"@{self.bot.nickname}", "").strip()
            master_wxid = self.bot.master_wxid

            if command == '帮助' or command == '菜单':
                return True #!!!
                # self.bot.client.post_mini_app(
                #     self.bot.app_id,
                #     from_user,
                #     'wxc463b1f7bf8f4bf7',
                #     '123',
                #     '',
                #     '',
                #     '',
                #     'gh_84d259b4887f@app'
                # )
                return self._handle_help_command(from_user)
            elif command.lower() == "查wxid":
                self.bot.client.post_text(self.bot.app_id, from_user, from_user)
                return True
            elif "脚本" in command or "软件" in command or "点击器" in command or "连点器" in command or "下载" in command:
                if hasattr(self.bot, 'is_master') and self.bot.is_master:
                    return True
                    return self._handle_script_command(from_user)

            # 主机器人
            if hasattr(self.bot, 'is_master') and self.bot.is_master:
                # 加入监控群命令
                if command == '监控':
                    return self._handle_join_vip_monitor_group_command(sender_wxid)
                # 加入群命令
                elif command == '加群' or command == '交流群':
                    return self._handle_join_group_command(sender_wxid)
                # 脚本命令
                elif command == '脚本':
                    return True
                    return self._handle_script_command(sender_wxid)
                # VIP监控命令
                elif command.lower() == 'vip监控' or command == 'vip监控群'or command == 'vip 监控':
                    return self._handle_join_vip_monitor_group_command(sender_wxid)
                elif command == '登录' or command == '登陆':
                    self._handle_login_command(sender_wxid)
                    return True
                elif command.lower() == 'vip':
                    return self._handle_vip_command(from_user, sender_wxid)
                elif command.strip().isascii() and command.strip().isalnum() and len(command.strip()) == 24:  # 先去除空白字符再判断
                    return self._handle_order_command(from_user, sender_wxid, command.strip())
                elif command.strip().isascii() and command.strip().isalnum() and len(command.strip()) == 18:  # 处理18位卡密
                    return self._handle_card_command(from_user, sender_wxid, command.strip())
                elif command.startswith("生成卡密"):  # 处理生成卡密命令
                    if sender_wxid not in master_wxid:
                        self.bot.client.post_text(self.bot.app_id, from_user, "抱歉，您没有权限执行此操作")
                        return True
                    return self._handle_generate_card_command(from_user, sender_wxid, command)
                elif command.startswith("购买"):  # 处理购买命令
                    return self._handle_purchase_command(from_user, sender_wxid, command)
                elif command == "确认购买":  # 处理确认购买命令
                    return self._handle_confirm_purchase(from_user, sender_wxid)
                elif command == "解除绑定" or command == "解绑":
                    return self._handle_unbind_command(from_user, sender_wxid)
                elif command.startswith("查文字 "):
                    # 判断是否是VIP用户
                    vip_info = self.db.get_vip_user(sender_wxid)
                    if (vip_info and vip_info.get('is_active') == 1):
                        return self._handle_query_message_command(from_user, command)
                    else:
                        self.bot.client.post_text(self.bot.app_id, from_user, '功能仅向VIP开放')
                        return True
                elif command.startswith("查商品 "):
                    # 判断是否是VIP用户
                    vip_info = self.db.get_vip_user(sender_wxid)
                    if (vip_info and vip_info.get('is_active') == 1):
                        return self._handle_query_product_command(from_user, command)
                    else:
                        self.bot.client.post_text(self.bot.app_id, from_user, '功能仅向VIP开放')
                        return True
                    
            
                
                # 机器人管理命令 - 仅限主机器人
                if (command.startswith('/bot')):
                    if sender_wxid in master_wxid:
                        return self._handle_bot_command(sender_wxid, command)

                        
            if command == "通知列表":
                return self._handle_notify_product_list_command(from_user)
            elif command == "通知帮助":
                return self._handle_notify_product_help_command(from_user)
            elif command.startswith("添加通知"):
                # 检查权限：只有主人可以添加通知商品
                if sender_wxid not in master_wxid:
                    self.bot.client.post_text(self.bot.app_id, from_user, "抱歉，您没有权限执行此操作")
                    return True
                return self._handle_add_notify_product_command(from_user, command)
            elif command.startswith("删除通知"):
                # 检查权限：只有主人可以移除通知商品
                if sender_wxid not in master_wxid:
                    self.bot.client.post_text(self.bot.app_id, from_user, "抱歉，您没有权限执行此操作")
                    return True
                return self._handle_remove_notify_product_command(from_user, command)
                
            # 副机器人控制命令 - 仅限副机器人的管理员使用
            if (command.startswith('/bot')):
                if sender_wxid in master_wxid:
                    return self._handle_control_command(sender_wxid, command)
                
            return False
        except Exception as e:
            self.logger.error(f"处理命令失败: {str(e)}")
            return False

    def _handle_bot_command(self, from_user, command):
        """处理机器人管理命令"""
        # 检查是否为主机器人
        if not hasattr(self.bot, 'is_master') or not self.bot.is_master:
            self.bot.client.post_text(self.bot.app_id, from_user, "此功能仅适用于主机器人")
            return True
            
        # 检查是否为管理员
        if from_user not in self.bot.master_wxid:
            self.bot.client.post_text(self.bot.app_id, from_user, "您没有权限执行此命令")
            return True
            
        # 解析命令
        parts = command.split(' ')
        if len(parts) < 2:
            self.bot.client.post_text(self.bot.app_id, from_user, "命令格式错误。使用'/bot 操作 [参数]'，操作可以是: 列表, 创建, 启动, 停止, 登录, 命令, 过期")
            return True
            
        action = parts[1]
        
        if action.lower() == "清理vip群":
            # 清理VIP群中不符合条件的成员
            if self.maintain_vip_users(manual_check=True):  # 手动清理时总是检查试用期订单
                self.bot.client.post_text(self.bot.app_id, from_user, "VIP群成员清理完成")
            else:
                self.bot.client.post_text(self.bot.app_id, from_user, "VIP群成员清理失败")
            return True

        elif action in ['广播', '发送广播']:
            if len(parts) < 3:
                self.bot.client.post_text(self.bot.app_id, from_user, "命令格式错误。使用'/bot 广播 [msg]'")
                return True
            # 转发到其他副机器人的端口上
            if hasattr(self.bot, 'is_master') and self.bot.is_master and self.bot.bots_info:
                # 构建简化的转发数据
                post_data = {
                    "TypeName": "PostText",
                    "Wxid": "ALL",
                    "Data": {
                        "msg": parts[2]
                    }
                }
                
                # 使用新的广播函数发送数据
                self._broadcast_to_sub_bots(post_data)
            return True
        
        # 获取机器人列表
        elif action in ['列表', 'list']:
            bots = self.bot.bots_info
            if not bots:
                self.bot.client.post_text(self.bot.app_id, from_user, "当前没有副机器人")
                return True
                
            bot_list = "副机器人列表:\n"
            for wxid, info in bots.items():
                expiry = f", 过期: {info.get('expiry_date', '永不')}" if info.get('expiry_date') else ""
                bot_list += f"- {wxid} ({info.get('nickname', '未命名')}): {info.get('status', '未知')}{expiry}\n"
                
            self.bot.client.post_text(self.bot.app_id, from_user, bot_list)
            return True
            
        # 创建副机器人
        if action in ['创建', 'create']:
            if len(parts) < 3:
                self.bot.client.post_text(self.bot.app_id, from_user, "命令格式错误。使用'/bot 创建 [wxid] [ip] [port]'")
                return True
                
            wxid = parts[2]

            if len(parts) < 4:
                base_url = None
            else:
                ip = parts[3]
                if len(parts) < 5:
                    port = "2531"  # 默认端口
                else:
                    port = parts[4]
                base_url = f"http://{ip}:{port}/v2/api"
            
            if self.bot.create_sub_bot(wxid, base_url):
                self.bot.client.post_text(self.bot.app_id, from_user, f"已创建副机器人: {wxid}")
            else:
                self.bot.client.post_text(self.bot.app_id, from_user, f"创建副机器人失败: {wxid}")
            return True
            
        # 启动副机器人
        if action in ['启动', 'start']:
            if len(parts) < 3:
                self.bot.client.post_text(self.bot.app_id, from_user, "请提供副机器人的wxid")
                return True
                
            wxid = parts[2]
            if self.bot.start_sub_bot(wxid):
                self.bot.client.post_text(self.bot.app_id, from_user, f"已启动副机器人: {wxid}")
            else:
                self.bot.client.post_text(self.bot.app_id, from_user, f"启动副机器人失败: {wxid}")
            return True
            
        # 停止副机器人
        if action in ['停止', 'stop']:
            if len(parts) < 3:
                self.bot.client.post_text(self.bot.app_id, from_user, "请提供副机器人的wxid")
                return True
                
            wxid = parts[2]
            if self.bot.stop_sub_bot(wxid):
                self.bot.client.post_text(self.bot.app_id, from_user, f"已停止副机器人: {wxid}")
            else:
                self.bot.client.post_text(self.bot.app_id, from_user, f"停止副机器人失败: {wxid}")
            return True
            
        # 获取登录二维码
        if action in ['登录', 'login']:
            if len(parts) < 3:
                self.bot.client.post_text(self.bot.app_id, from_user, "请提供副机器人的wxid")
                return True

            wxid = parts[2]
            if self._handle_login_command(wxid, from_user):
                self.bot.client.post_text(self.bot.app_id, from_user, f"登录成功: {wxid}")
            else:
                self.bot.client.post_text(self.bot.app_id, from_user, f"登录失败: {wxid}")
            return True
            
        # 设置副机器人过期时间
        if action in ['过期', 'expire']:
            if len(parts) < 3:
                self.bot.client.post_text(self.bot.app_id, from_user, "请提供副机器人的wxid")
                return True
                
            wxid = parts[2]
            
            # 如果没有提供过期时间，则清除过期时间（设为永不过期）
            if len(parts) < 4:
                if self.bot.set_bot_expiry(wxid, ""):
                    self.bot.client.post_text(self.bot.app_id, from_user, f"已将副机器人 {wxid} 设为永不过期")
                else:
                    self.bot.client.post_text(self.bot.app_id, from_user, f"设置副机器人过期时间失败")
                return True
                
            # 处理过期时间参数
            expiry_str = parts[3]
            try:
                # 尝试解析日期格式
                expiry_time = time.strptime(expiry_str, "%Y-%m-%d %H:%M:%S")
                if self.bot.set_bot_expiry(wxid, expiry_str):
                    self.bot.client.post_text(self.bot.app_id, from_user, f"已设置副机器人 {wxid} 的过期时间为 {expiry_str}")
                else:
                    self.bot.client.post_text(self.bot.app_id, from_user, f"设置副机器人过期时间失败")
            except ValueError:
                self.bot.client.post_text(self.bot.app_id, from_user, "日期格式错误，请使用: YYYY-MM-DD HH:MM:SS")
            return True
            
        # 向副机器人发送命令
        if action in ['命令', 'command']:
            if len(parts) < 4:
                self.bot.client.post_text(self.bot.app_id, from_user, "命令格式错误。使用'/bot 命令 [wxid] [命令内容]'")
                return True
                
            wxid = parts[2]
            cmd = parts[3]
            
            if self.bot.send_command_to_sub_bot(wxid, cmd):
                self.bot.client.post_text(self.bot.app_id, from_user, f"已发送命令到副机器人: {wxid}")
            else:
                self.bot.client.post_text(self.bot.app_id, from_user, f"发送命令失败")
            return True
            
        # 未知机器人操作
        self.bot.client.post_text(self.bot.app_id, from_user, f"未知机器人操作: {action}。支持的操作: 列表, 创建, 启动, 停止, 登录, 命令, 过期")
        return True

    def _handle_control_command(self, from_user, command):
        """处理副机器人控制命令"""
        if hasattr(self.bot, 'is_master') and self.bot.is_master:
            self.bot.client.post_text(self.bot.app_id, from_user, "此功能仅适用于副机器人")
            return True

        # 检查是否为管理员
        if from_user not in self.bot.master_wxid:
            self.bot.client.post_text(self.bot.app_id, from_user, "您没有权限执行此命令")
            return True
            
        parts = command.split(' ')
        if len(parts) < 2:
            self.bot.client.post_text(self.bot.app_id, from_user, "命令格式错误。使用'/bot 操作'")
            return True
            
        action = parts[1]
        
        # 获取信息
        if action in ['信息', 'info']:
            status_info = f"机器人状态:\nwxid: {self.bot.wxid}\n昵称: {self.bot.nickname}\n管理员: {self.bot.master_wxid}\n目标群: {self.bot.master_group}"
            self.bot.client.post_text(self.bot.app_id, from_user, status_info)
            return True

        # 设置广告图片
        if action in ['设置广告', 'set_ad']:
            # 检查是否有自定义广告权限
            if not hasattr(self.bot, 'bots_info'):
                self.bot.bots_info = self.bot.load_bots_info()
            
            if not self.bot.wxid or self.bot.wxid not in self.bot.bots_info or not self.bot.bots_info[self.bot.wxid].get('show_ads', False):
                self.bot.client.post_text(self.bot.app_id, from_user, "您需要先购买自定义广告位服务才能使用此功能")
                return True
            
            # 设置正在等待接收广告图片的状态
            self.waiting_ad_image = True
            # 记录发送命令的用户ID
            self.ad_image_sender = from_user
            
            # 发送示例图片和说明
            ad_help = "请发送您的广告图片，最大为500*200\n支持透明(PNG)图片，请使用电脑端上传。\n广告位置如下:"
            self.bot.client.post_text(self.bot.app_id, from_user, ad_help)
            
            # 发送示例图片
            image_url = f"{self.bot.ext_url}/product_qr_img/demo1.jpeg"
            self.bot.client.post_image(self.bot.app_id, from_user, image_url)
            return True

        # 设置通知群
        if action in ['设置通知群', 'set_group']:
            if len(parts) < 3:
                self.bot.client.post_text(self.bot.app_id, from_user, "命令格式错误。使用'/bot 设置通知群 [群wxid]'")
                return True
                
            group_id = parts[2]
            if not group_id.endswith("@chatroom"):
                self.bot.client.post_text(self.bot.app_id, from_user, "无效的群ID，群ID必须以@chatroom结尾")
                return True
                
            # 验证群是否存在并获取群信息
            group_info = self.bot.client.get_detail_info(self.bot.app_id, [group_id])
            if not group_info or group_info.get('ret') != 200 or not group_info.get('data'):
                self.bot.client.post_text(self.bot.app_id, from_user, "无法获取群信息")
                return True
                
            # 获取群的详细信息
            group_data = group_info['data'][0]
            group_name = group_data.get('remark') or group_data.get('nickName', '')
            
            # 设置通知群
            self.bot.master_group = group_id
            self.bot.save_bot_config()
            
            # 构建回复消息
            reply = f"已成功设置通知群:\n"
            reply += f"群ID: {group_id}\n"
            reply += f"群名称: {group_name}"
            
            self.bot.client.post_text(self.bot.app_id, from_user, reply)
            return True

        # 未知控制操作
        self.bot.client.post_text(self.bot.app_id, from_user, f"未知控制操作: {action}。支持的操作: 信息，启动，停止，登录")
        return True

    def _handle_login_command(self, wxid, from_user=None):
        """处理登录命令"""
        if from_user == None:
            from_user = wxid
            
        # 生成随机安全密钥
        # 生成32位随机字符串作为安全令牌
        token = secrets.token_hex(16)
        
        # 保存令牌到缓存，便于后续验证，有效期五分钟
        if hasattr(self.bot, 'cache'):
            self.bot.cache.set(f"login_token_{token}_{wxid}", True, timeout=300)
        
        # 生成登录链接，包含随机令牌
        login_url = f"{self.bot.ext_url}/login/{wxid}/{token}"
        
        # 发送登录链接
        self.bot.client.post_text(self.bot.app_id, from_user, f"请使用其他设备打开以下链接进行登录，链接有效期5分钟，登录成功后自动启动机器人：\n{login_url}")
        
        # 不再使用旧的登录方式
        # if self.bot.send_qrcode_to_bot(wxid, from_user):
        #     return True
        # else:
        #     return False
        
        return True

    def _handle_help_command(self, from_user):
        """处理帮助命令"""
        # 检查是否启用了主机器人独有功能
        is_master = hasattr(self.bot, 'is_master') and self.bot.is_master
        is_sub_bot = not is_master
        
        help_text = """基础命令:
· 加群 - 获取交流群邀请
· 监控 - 获取监控群邀请(弃用)
· 监控 - 获取VIP监控群邀请
· 脚本 - 获取脚本链接
· VIP - 查询VIP状态信息
· 24位订单号 - 绑定脚本订单编号
· 解除绑定 - 解除绑定脚本订单编号
· 18位余额兑换码 - 兑换余额
· 购买菜单 - 使用余额兑换服务"""

        if from_user in self.bot.master_wxid:
            help_text += """
管理员命令:
· 通知帮助 - 自定义通知列表的使用说明
· 通知列表 - 查看当前通知商品列表
· 转发商品小程序到机器人 - 查看商品ID和名称，并添加新商品
· 添加通知 [商品ID] - 添加商品到通知列表
· 删除通知 [商品ID] - 从通知列表移除商品"""
            # 添加主机器人特有命令
            if is_master:
                help_text += """
主机器人【管理员】命令:
· /bot 列表 - 显示所有副机器人
· /bot 创建 [wxid] - 创建新副机器人
· /bot 启动 [wxid] - 启动副机器人
· /bot 停止 [wxid] - 停止副机器人
· /bot 登录 [wxid] [接收者wxid] - 获取登录二维码
· /bot 过期 [wxid] [日期] - 设置副机器人过期时间
· /bot 命令 [wxid] [命令] - 向副机器人发送命令"""
        
            # 添加副机器人特有命令
            if is_sub_bot:
                help_text += """
子机器人【管理员】命令:
· 查wxid - 查询当前聊天窗口的微信ID
· /bot 信息 - 显示信息
· /bot 设置通知群 [wxid] - 设置机器人发送监控通知的群
· /bot 设置广告 - 重新设置广告图片"""
        
        self.bot.client.post_text(self.bot.app_id, from_user, help_text)
        return True

    def _handle_notify_product_help_command(self, from_user):
        """处理通知商品帮助命令"""
        # 检查是否为管理员
        if from_user not in self.bot.master_wxid:
            self.bot.client.post_text(self.bot.app_id, from_user, "您没有权限执行此命令")
            return True
        help_text = """自定义通知使用说明:
· 没有使用过此功能时，默认使用YBA_BOT的通知列表，跟随主机器人通知变更
· 使用过添加或删除后，将切换为自定义的通知列表，不再跟随主机器人

添加通知商品：
1、将要添加的商品小程序分享给你的机器人，机器人会回复该商品的ID和名称
2、向机器人发送：
添加通知 [ID]
- 添加通知商品，例如添加马卡龙，就发送：添加通知 2473

删除通知商品：
1、使用"通知列表"指令查询当前所有通知的商品列表
2、找到需要删除的商品ID
3、向机器人发送：
删除通知 [ID]
- 删除该商品的通知
"""
        self.bot.client.post_text(self.bot.app_id, from_user, help_text)
        return True


    def _handle_notify_product_list_command(self, from_user):
        """处理获取通知商品列表命令"""
        try:
            # 构建商品列表消息
            product_list = []
            for product in self.product_info:
                if product['product_id'] in self.notify_product_ids:
                    # 使用原始商品名称，并添加商品ID
                    product_list.append(f"({product['product_id']}){product['product_name']}")
            
            # 如果没有商品，返回提示
            if not product_list:
                self.bot.client.post_text(self.bot.app_id, from_user, "暂无通知商品")
                return True
            
            # 构建分页消息，每页10个商品
            page_size = 10
            pages = [product_list[i:i+page_size] for i in range(0, len(product_list), page_size)]
            
            # 发送每页消息
            for i, page in enumerate(pages):
                page_msg = f"通知列表 ({i+1}/{len(pages)}):\n" + "\n".join(page)
                self.bot.client.post_text(self.bot.app_id, from_user, page_msg)
            
            return True
        except Exception as e:
            self.logger.error(f"获取商品列表失败: {str(e)}")
            return False

    def _handle_add_notify_product_command(self, from_user, command):
        """处理添加通知商品命令"""
        product = {}
        try:
            # 使用正则表达式提取商品ID和名称
            pattern = r'添加通知\s+(\w+)'
            match = re.match(pattern, command)
            
            if not match:
                self.bot.client.post_text(self.bot.app_id, from_user, "格式错误，请使用: 添加通知 商品ID")
                return False
            
            product_id = match.group(1)
            
            # 添加通知商品，使用当前机器人的wxid
            wxid = 'main' if self.bot.is_master else self.bot.wxid
            if product_id in self.product_index["id_map"]:
                if self.db.add_notify_product(product_id, wxid):
                    self.notify_product_ids = self.db.get_notify_product_ids(wxid)
                    self.bot.client.post_text(self.bot.app_id, from_user, f"添加成功: ({product_id}){self.product_index['id_map'][product_id]['product_name']}")
                    return True
                self.bot.client.post_text(self.bot.app_id, from_user, "添加失败，商品已在通知列表中")
                return False
            
            self.bot.client.post_text(self.bot.app_id, from_user, "添加失败，商品不存在")
            return False
        except Exception as e:
            self.logger.error(f"添加通知商品失败: {str(e)}")
            return False

    def _handle_remove_notify_product_command(self, from_user, command):
        """处理移除通知商品命令"""
        try:
            # 使用正则表达式提取商品ID
            pattern = r'删除通知\s+(\w+)'
            match = re.match(pattern, command)
            
            if not match:
                self.bot.client.post_text(self.bot.app_id, from_user, "格式错误，请使用: 删除通知 商品ID")
                return False
            
            product_id = match.group(1)
            
            # 查找商品名称
            product_name = None
            if product_id in self.product_index["id_map"]:
                product = self.product_index["id_map"][product_id]
                product_name = product.get('nick_name') or product.get('product_name')
            
            # 移除通知商品，使用当前机器人的wxid
            wxid = 'main' if self.bot.is_master else self.bot.wxid
            if self.db.remove_notify_product(product_id, wxid):
                self.notify_product_ids = self.db.get_notify_product_ids(wxid)
                success_msg = f"移除成功: {product_name}({product_id})" if product_name else f"移除成功: {product_id}"
                self.bot.client.post_text(self.bot.app_id, from_user, success_msg)
                return True
            else:
                self.bot.client.post_text(self.bot.app_id, from_user, "移除失败，商品不在通知列表中")
                return False
        except Exception as e:
            self.logger.error(f"移除通知商品失败: {str(e)}")
            return False

    def _handle_join_group_command(self, from_user):
        """处理加群命令"""
        try:
            # chatroom_id = YBA交流群[2]
            # self.bot.client.invite_member(
            #     self.bot.app_id, 
            #     from_user, 
            #     chatroom_id, 
            #     ""
            # )
            self.bot.client.post_text(self.bot.app_id, from_user, "暂无交流群，有问题可直接问")
            return True
        except Exception as e:
            self.logger.error(f"发送加群邀请失败: {str(e)}")
            return False

    def _handle_join_monitor_group_command(self, from_user):
        """处理加入监控群命令"""
        try:
            chatroom_id = 普通监控群#self.bot.master_group
            self.bot.client.invite_member(
                self.bot.app_id, 
                from_user, 
                chatroom_id, 
                ""
            )
            
            self.bot.client.post_text(self.bot.app_id, from_user, "已邀请\n普通监控群已弃用\nVIP监控群18.8/月\n订阅脚本赠送VIP\n购买VIP联系群主")
            # self.bot.client.post_text(self.bot.app_id, from_user, "暂不加人")
            return True
        except Exception as e:
            self.logger.error(f"发送监控群邀请失败: {str(e)}")
            return False

    def _handle_join_vip_monitor_group_command(self, from_user):
        """处理加入VIP监控群命令"""
        try:
            # 先检查用户是否是有效的VIP
            vip_info = self.db.get_vip_user(from_user)
            if not vip_info or vip_info['is_active'] == 0:
                self.bot.client.post_text(self.bot.app_id, from_user, "抱歉，只有VIP用户才能加入监控群，购买链接：#小程序://闲鱼/RmRsGfDjJNu7ZYA")
                return True
            
            # # 检查用户是否有绑定订单
            # if not vip_info.get('order_id'):
            #     self.bot.client.post_text(self.bot.app_id, from_user, "抱歉，您需要成功绑定订单才能加入VIP监控群")
            #     return True
            
            # 检查VIP是否过期
            if vip_info['expires_at']:
                try:
                    expires_at = datetime.datetime.strptime(vip_info['expires_at'], "%Y-%m-%d %H:%M:%S")
                    if expires_at.timestamp() <= time.time():
                        self.bot.client.post_text(self.bot.app_id, from_user, "抱歉，您的VIP已过期，请续费后再试")
                        return True
                except ValueError:
                    # 如果时间格式不匹配，尝试转换
                    expires_at_str = self._convert_iso_time(vip_info['expires_at'])
                    expires_at = datetime.datetime.strptime(expires_at_str, "%Y-%m-%d %H:%M:%S")
                    if expires_at.timestamp() <= time.time():
                        self.bot.client.post_text(self.bot.app_id, from_user, "抱歉，您的VIP已过期，请续费后再试")
                        return True
            # print(vip_info)
            # VIP验证通过，发送群邀请
            chatroom_id = VIP监控群[0]
            self.bot.client.invite_member(
                self.bot.app_id, 
                from_user, 
                chatroom_id, 
                ""
            )
            
            self.bot.client.post_text(self.bot.app_id, from_user, "已邀请")
            return True
        except Exception as e:
            self.logger.error(f"发送监控群邀请失败: {str(e)}")
            return False

    def _handle_script_command(self, from_user):
        """处理获取脚本命令"""
        try:
            script_message = "分享个 Hamibot 脚本「泡泡玛特小程序抢购」，网址是：https://hamibot.com/marketplace/pzyj7 复制链接到浏览器打开"
            self.bot.client.post_text(self.bot.app_id, from_user, script_message)
            return True
        except Exception as e:
            self.logger.error(f"发送脚本链接失败: {str(e)}")
            return False

    def _convert_iso_time(self, iso_time_str):
        """将ISO 8601格式的UTC时间转换为北京时间字符串"""
        try:
            # 移除毫秒部分
            iso_time = iso_time_str.split('.')[0]
            # 如果以Z结尾，移除Z并添加+00:00表示UTC
            if iso_time.endswith('Z'):
                iso_time = iso_time[:-1] + '+00:00'
            # 解析ISO时间字符串
            dt = datetime.datetime.fromisoformat(iso_time)
            # 转换为北京时间（UTC+8）
            beijing_dt = dt + datetime.timedelta(hours=8)
            # 返回格式化的时间字符串
            return beijing_dt.strftime("%Y-%m-%d %H:%M:%S")
        except Exception as e:
            logger.error(f"时间格式转换失败: {str(e)}")
            return iso_time_str 

    def _handle_vip_command(self, from_user, sender_wxid):
        """处理VIP命令，查询用户的VIP信息"""
        try:
            # 获取用户的VIP信息
            vip_info = self.db.get_vip_user(sender_wxid)
            if not vip_info:
                self.db.add_vip_user(sender_wxid)
                vip_info = self.db.get_vip_user(sender_wxid)

            # 获取用户的订单信息
            order_info = self.db.get_user_order(sender_wxid)

            # 构建回复消息
            current_time = time.time()
            is_active = False
            
            # 处理过期时间
            order_expires = None
            expires_at = None
            
            if order_info:
                # 处理订单过期时间
                if order_info['expires_at']:
                    try:
                        order_expires = datetime.datetime.strptime(order_info['expires_at'], "%Y-%m-%d %H:%M:%S")
                    except ValueError:
                        # 如果时间格式不匹配，尝试转换
                        expires_at_str = self._convert_iso_time(order_info['expires_at'])
                        order_expires = datetime.datetime.strptime(expires_at_str, "%Y-%m-%d %H:%M:%S")
            if vip_info:
                # 检查VIP状态
                if vip_info.get('expires_at'):
                    try:
                        expires_at = datetime.datetime.strptime(vip_info['expires_at'], "%Y-%m-%d %H:%M:%S")
                        if expires_at.timestamp() > current_time:
                            is_active = True
                    except ValueError:
                        expires_at_str = self._convert_iso_time(vip_info['expires_at'])
                        expires_at = datetime.datetime.strptime(expires_at_str, "%Y-%m-%d %H:%M:%S")
                        if expires_at.timestamp() > current_time:
                            is_active = True

            # 构建回复消息
            reply = []
            reply.append(f"VIP状态：{'有效' if is_active else '已过期'}")

            if expires_at:
                reply.append(f"到期时间：{expires_at.strftime('%Y-%m-%d %H:%M:%S')}")

            # 显示VIP信息
            if is_active:
                # 显示订单信息
                if order_info:
                    order_section = ["\n订单信息："]
                    order_section.append(f"- 订单编号：{order_info['order_id'][:3]}***{order_info['order_id'][-3:]}")
                    order_section.append(f"- 计划名称：{order_info['plan_name']}")
                    
                    # 计算总绑定数量
                    total_count = order_info['max_vip_count']
                    if order_info.get('bonus_count'):
                        total_count += order_info['bonus_count']
                        order_section.append(f"- 绑定数量：{order_info['bound_count']}/{total_count} (含赠送{order_info['bonus_count']}个)")
                    else:
                        order_section.append(f"- 绑定数量：{order_info['bound_count']}/{total_count}")
                        
                    if order_expires:
                        if order_expires.timestamp() > current_time:
                            order_section.append(f"- 到期时间：{order_expires.strftime('%Y-%m-%d %H:%M:%S')}")
                            reply.append("\n".join(order_section))


            # 显示余额信息
            if vip_info:
                reply.append(f"\n积分：{vip_info['balance']}")
            
            # 添加解绑提示
            if order_info:
                reply.append("\n发送「解除绑定」可解除当前账号与订单的关联")

            self.bot.client.post_text(self.bot.app_id, from_user, "\n".join(reply))
            return True

        except Exception as e:
            self.logger.error(f"处理VIP命令失败: {str(e)}")
            self.bot.client.post_text(self.bot.app_id, from_user, "查询VIP信息失败，请稍后重试")
            return False

    def _handle_order_command(self, from_user, sender_wxid, order_id):
        """处理订单命令，查询并更新订单信息"""
        try:
            current_user = self.db.get_vip_user(sender_wxid)
            if current_user and current_user.get("order_id"):
                self.bot.client.post_text(self.bot.app_id, from_user, f"已经绑定了订单{current_user['order_id'][:3]}***{current_user['order_id'][-3:]}\n如果需要绑定新订单，请先【解除绑定】")
                return True
            current_time = time.time()
            # 发送正在查询的提示
            self.bot.client.post_text(self.bot.app_id, from_user, "正在查询订单信息，请稍候...")
            
            # 查询Hamibot订单API
            order_info = self._query_hamibot_order(order_id)
            self.logger.info(f"订单查询结果: {order_info}")
            
            if not order_info:
                self.bot.client.post_text(self.bot.app_id, from_user, "订单查询失败，请稍后重试")
                return True
            elif order_info == 404:
                self.bot.client.post_text(self.bot.app_id, from_user, "未找到订单信息，请确认订单号是否正确")
                return True

            # # 转换过期时间格式
            # expires_at = self._convert_iso_time(order_info['expiresAt'])
            # self.logger.info(f"转换后的过期时间: {expires_at}")

            # # 计算基础绑定数量
            # base_vip_count = self._get_max_vip_count(order_info['planName'])
            # self.logger.info(f"订单绑定数: {base_vip_count}")

            # # 添加订单信息到数据库
            # success = self.db.add_vip_order(
            #     order_id=order_id,
            #     script_id=order_info['slug'],
            #     script_name=order_info['name'],
            #     amount=round(float(order_info['amount']), 2),
            #     plan_name=order_info['planName'],
            #     period_unit=order_info['periodUnit'],
            #     free_trial=order_info.get('freeTrial', 0),
            #     expires_at=expires_at,
            #     max_vip_count=base_vip_count
            # )

            # if not success:
            #     self.logger.error("订单信息保存到数据库失败")
            #     self.bot.client.post_text(self.bot.app_id, from_user, "订单信息保存失败，请稍后重试")
            #     return True

            # 绑定订单
            bind_result = self.db.bind_vip_order(order_id, sender_wxid)
            self.logger.info(f"订单绑定结果: {bind_result}")

            # 获取订单信息和用户列表
            order_detail = self.db.get_vip_orders(order_id)
            if not order_detail:
                self.logger.error("获取订单详情失败")
                self.bot.client.post_text(self.bot.app_id, from_user, "获取订单详情失败，请稍后重试")
                return True
                
            order_detail = order_detail[0]  # 获取第一个结果
            order_users = self.db.get_order_users(order_id)

            # 构建回复消息
            if bind_result['success']:
                reply = f"订单绑定成功！\n\n"
            else:
                if "已经绑定" in bind_result['message']:
                    reply = f"您已绑定此订单！\n\n"
                else:
                    reply = f"订单绑定失败：{bind_result['message']}\n\n"
            
            reply += f"订单信息：\n"
            reply += f"脚本：{order_detail['script_name']}\n"
            reply += f"套餐：{order_detail['plan_name']}\n"
            reply += f"到期时间：{order_detail['expires_at']}\n"
            total_max = order_detail['max_vip_count'] + order_detail['bonus_count']
            reply += f"已绑定数量：{bind_result['current_count']}/{total_max}"
            if order_detail['bonus_count'] > 0:
                reply += f"（含赠送{order_detail['bonus_count']}个）"
            reply += "\n"
            
            if order_users:
                reply += "\n已绑定的微信号：\n"
                for user in order_users:
                    reply += f"- {user['wxid']} (绑定时间: {user['bind_time']})\n"
            
            # 如果绑定失败且达到最大数量，提供解绑提示
            if not bind_result['success'] and bind_result['current_count'] >= total_max:
                reply += f"\n提示：订单已达最大绑定数量({total_max})，请联系已绑定用户解除绑定后再试"

            self.bot.client.post_text(self.bot.app_id, from_user, reply)
            return True

        except Exception as e:
            self.logger.error(f"处理订单命令失败: {str(e)}\n{traceback.format_exc()}")
            self.bot.client.post_text(self.bot.app_id, from_user, "订单处理失败，请稍后重试")
            return False
            
    def _handle_unbind_command(self, from_user, sender_wxid):
        """处理解除绑定命令"""
        try:
            # 发送正在处理的提示
            # self.bot.client.post_text(self.bot.app_id, from_user, "正在处理解绑请求，请稍候...")
            
            # 查询用户当前绑定的订单
            user_info = self.db.get_vip_user(sender_wxid)
            if not user_info or not user_info.get('order_id'):
                self.bot.client.post_text(self.bot.app_id, from_user, "您当前没有绑定任何订单")
                return True
                
            # 记录当前订单ID，用于后续查询
            order_id = user_info.get('order_id')
                
            # 解除绑定
            success = self.db.unbind_vip_order(sender_wxid)
            
            if success:
                # 获取当前订单绑定状态
                order_detail = self.db.get_vip_orders(order_id)
                bound_count = 0
                max_count = 0
                
                if order_detail:
                    order_detail = order_detail[0]
                    bound_count = order_detail['bound_count']
                    max_count = order_detail['max_vip_count']
                
                reply = f"您已成功解除订单绑定\n"
                reply += f"此订单当前绑定数量：{bound_count}/{max_count}"
                self.bot.client.post_text(self.bot.app_id, from_user, reply)
            else:
                self.bot.client.post_text(self.bot.app_id, from_user, "解除绑定失败，请稍后重试")
                
            return True
            
        except Exception as e:
            self.logger.error(f"处理解绑命令失败: {str(e)}")
            self.bot.client.post_text(self.bot.app_id, from_user, "解除绑定失败，请稍后重试")
            return False

    def _query_hamibot_order(self, order_id):
        """查询Hamibot订单API，添加超时设置"""
        try:
            # 构建API请求
            url = f"https://api.hamibot.com/v1/orders/{order_id}"
            headers = {
                'Authorization': hamibot_api_token
            }

            # 发送请求，设置超时时间为10秒
            response = requests.get(url, headers=headers, timeout=30)
            if response.status_code == 200:
                order_info = response.json()
                # 转换过期时间格式
                expires_at = self._convert_iso_time(order_info['expiresAt'])
                order_info['expiresAt'] = expires_at
                self.logger.info(f"转换后的过期时间: {expires_at}")

                # 计算基础绑定数量
                base_vip_count = self._get_max_vip_count(order_info['planName'])
                self.logger.info(f"订单绑定数: {base_vip_count}")

                # 如果是试用，先判断过期时间是否小于29天，如果小于29天，不是试用
                if order_info.get('freeTrial', 0) and (datetime.datetime.strptime(expires_at, "%Y-%m-%d %H:%M:%S") - datetime.datetime.now()).days < 29:
                    order_info['freeTrial'] = 0

                # 添加订单信息到数据库
                success = self.db.add_vip_order(
                    order_id=order_id,
                    script_id=order_info['slug'],
                    script_name=order_info['name'],
                    amount=round(float(order_info['amount']), 2),
                    plan_name=order_info['planName'],
                    period_unit=order_info['periodUnit'],
                    free_trial=order_info.get('freeTrial', 0),
                    expires_at=expires_at,
                    max_vip_count=base_vip_count
                )

                return order_info
            elif response.status_code == 404:
                self.db.add_vip_order(
                    order_id=order_id,
                    free_trial=0
                )
                self.logger.error(f"查询订单API失败: 订单不存在(404) - {order_id}")
                return 404
            else:
                self.logger.error(f"查询订单API失败: 状态码 {response.status_code} - {order_id}")
                return None

        except requests.exceptions.Timeout:
            self.logger.error(f"查询订单API超时: {order_id}")
            return None
        except requests.exceptions.RequestException as e:
            self.logger.error(f"查询订单API网络错误: {str(e)}")
            return None
        except Exception as e:
            self.logger.error(f"查询订单API异常: {str(e)}")
            return None

    def _get_max_vip_count(self, plan_name):
        """根据套餐名称获取最大VIP数量"""
        try:
            num = 0
            # 从套餐名称中提取数字
            match = re.search(r'(\d+)', plan_name)
            if match:
                num = int(match.group(1))
            elif plan_name == "无限制":
                num = 100

            if num > 10:
                num = 10
            return num
        except Exception:
            return 0
            
    def maintain_vip_users(self, manual_check: bool = False):
        """维护VIP用户数据
        
        每小时执行一次，执行以下操作：
        1. 查询所有有效的VIP用户
        2. 对于过期的VIP用户，查询订单获取最新过期时间
        3. 如果订单依然过期，则更新用户状态为已过期
        4. 检查每个订单的绑定数量，如果超过限制则移除最早绑定的用户
        5. 从VIP监控群中移除非VIP用户
        
        每天15点或手动清理时执行：
        6. 维护试用期订单，如果订单不存在说明已取消试用，则取消该用户VIP
        
        Args:
            manual_check: bool, 是否是手动清理。手动清理时总是检查试用期订单。
        """
        # return
        try:
            self.logger.info("开始维护VIP用户数据...")
            current_time = time.time()
            
            expired_count = 0
            updated_count = 0
            removed_count = 0
            
            # 检查是否需要维护试用期订单（15点或手动清理）
            current_hour = datetime.datetime.now().hour
            if current_hour == 15 or manual_check:
                self.logger.info("开始维护试用期订单...")
                try:
                    # 获取所有试用期订单
                    trial_orders = self.db.get_vip_orders_by_type(free_trial=True)
                    self.logger.info(f"找到 {len(trial_orders)} 个试用期订单")
                    
                    for order in trial_orders:
                        order_id = order['order_id']
                        # 查询Hamibot订单API
                        order_info = self._query_hamibot_order(order_id)
                        
                        if order_info == 404:  # 订单不存在，说明已取消试用
                            self.logger.info(f"试用期订单 {order_id} 已取消")
                            
                            # 获取该订单的所有绑定用户
                            bound_users = self.db.get_order_users(order_id)
                            for user in bound_users:
                                wxid = user['wxid']
                                
                                # 先解除订单绑定
                                if self.db.unbind_vip_order(wxid):
                                    self.logger.info(f"已解除用户 {wxid} 的试用期订单绑定")
                                    
                                    # 获取用户最新状态
                                    user_info = self.db.get_vip_user(wxid)
                                    if user_info:
                                        # 检查用户是否有其他有效期
                                        has_valid_time = False
                                        if user_info['is_active'] == 1:
                                            has_valid_time = True
                                        
                                        # 根据是否有其他有效期发送不同的通知
                                        if has_valid_time:
                                            notice = (
                                                "您的试用期订单已被取消\n\n"
                                                f"订单编号：{order_id[:3]}***{order_id[-3:]}\n\n"
                                                "由于您有剩余VIP时长，VIP功能将继续保持有效。"
                                            )
                                        else:
                                            notice = (
                                                "您的VIP已被取消\n\n"
                                                f"原因：试用期订单 {order_id[:3]}***{order_id[-3:]} 已被取消\n\n"
                                                "如需继续使用VIP功能，请购买正式订单。"
                                            )
                                        
                                        self.bot.client.post_text(self.bot.app_id, wxid, notice)
                                        
                                        # 更新用户状态
                                        if not has_valid_time:
                                            self.logger.info(f"已设置试用期用户 {wxid} 为非活跃状态")
                                else:
                                    self.logger.error(f"解除用户 {wxid} 的试用期订单绑定失败")
                                
                                time.sleep(1)  # 避免请求过于频繁
                except Exception as e:
                    self.logger.error(f"维护试用期订单失败: {str(e)}")
            
            # 获取所有有效的VIP用户
            vip_users = self.db.get_all_vip_users()
            self.logger.info(f"总共找到 {len(vip_users)} 个有效VIP用户")
            
            # 先处理VIP用户状态
            for user in vip_users:
                # 跳过没有过期时间的用户
                if not user['expires_at']:
                    continue
                    
                # 检查是否过期
                try:
                    expires_at = datetime.datetime.strptime(user['expires_at'], "%Y-%m-%d %H:%M:%S")
                except ValueError:
                    # 尝试转换格式
                    expires_at_str = self._convert_iso_time(user['expires_at'])
                    expires_at = datetime.datetime.strptime(expires_at_str, "%Y-%m-%d %H:%M:%S")
                
                # 如果已过期，则重新查询订单
                if expires_at.timestamp() <= current_time:
                    self.logger.info(f"发现过期VIP用户: {user['wxid']}, 过期时间: {user['expires_at']}")
                    
                    # 如果没有订单ID，直接设为过期
                    if not user['order_id']:
                        self.db.add_vip_user(user['wxid'], is_active=False)
                        expired_count += 1
                        continue
                    
                    # 查询订单获取最新状态
                    order_info = self._query_hamibot_order(user['order_id'])
                    if not order_info:
                        self.logger.warning(f"无法获取订单信息: {user['order_id']}")
                        continue
                    elif order_info == 404:
                        self.logger.info(f"用户 {user['wxid']} 订单不存在，解除绑定")
                        # 先解除订单绑定
                        if self.db.unbind_vip_order(user['wxid']):
                            # 获取用户最新状态
                            user_info = self.db.get_vip_user(user['wxid'])
                            if user_info:
                                # 检查用户是否有其他有效期
                                has_valid_time = False
                                if user_info['is_active'] == 1:
                                    has_valid_time = True
                                
                                # 根据是否有其他有效期发送不同的通知
                                if has_valid_time:
                                    notice = (
                                        "您的订单已过期\n\n"
                                        f"订单编号：{user['order_id'][:3]}***{user['order_id'][-3:]}\n\n"
                                        "由于您有剩余VIP时长，VIP功能将继续保持有效。"
                                    )
                                else:
                                    notice = (
                                        "您的VIP已被取消\n\n"
                                        f"原因：订单 {user['order_id'][:3]}***{user['order_id'][-3:]} 已失效\n\n"
                                        "如需继续使用VIP功能，请购买新的订单。"
                                    )
                                try:
                                    self.bot.client.post_text(self.bot.app_id, user['wxid'], notice)
                                except Exception as e:
                                    self.logger.error(f"发送通知给用户 {user['wxid']} 失败: {str(e)}")
                                
                                # 更新用户状态
                                if not has_valid_time:
                                    expired_count += 1
                        else:
                            self.logger.error(f"解除用户 {user['wxid']} 的订单绑定失败")
                        continue
                    
                    # 获取新的过期时间
                    new_expires_at = order_info['expiresAt']
                    new_expires_at_dt = datetime.datetime.strptime(new_expires_at, "%Y-%m-%d %H:%M:%S")
                    
                    # 如果新的过期时间仍然是过期的，则解除订单绑定
                    if new_expires_at_dt.timestamp() <= current_time:
                        self.logger.info(f"用户 {user['wxid']} 订单已过期，解除绑定")
                        # 先解除订单绑定
                        if self.db.unbind_vip_order(user['wxid']):
                            # 获取用户最新状态
                            user_info = self.db.get_vip_user(user['wxid'])
                            if user_info:
                                # 检查用户是否有其他有效期
                                has_valid_time = False
                                if user_info['expires_at']:
                                    try:
                                        user_expires_at = datetime.datetime.strptime(user_info['expires_at'], "%Y-%m-%d %H:%M:%S")
                                        if user_expires_at.timestamp() > current_time:
                                            has_valid_time = True
                                    except ValueError:
                                        expires_at_str = self._convert_iso_time(user_info['expires_at'])
                                        user_expires_at = datetime.datetime.strptime(expires_at_str, "%Y-%m-%d %H:%M:%S")
                                        if user_expires_at.timestamp() > current_time:
                                            has_valid_time = True
                                
                                # 根据是否有其他有效期发送不同的通知
                                if has_valid_time:
                                    notice = (
                                        "您的订单已过期\n\n"
                                        f"订单编号：{user['order_id'][:3]}***{user['order_id'][-3:]}\n\n"
                                        "由于您有剩余VIP时长，VIP功能将继续保持有效。"
                                    )
                                else:
                                    notice = (
                                        "您的VIP已被取消\n\n"
                                        f"原因：订单 {user['order_id'][:3]}***{user['order_id'][-3:]} 已过期\n\n"
                                        "如需继续使用VIP功能，请购买新的订单。"
                                    )
                                
                                self.bot.client.post_text(self.bot.app_id, user['wxid'], notice)
                                
                                # 更新用户状态
                                if not has_valid_time:
                                    expired_count += 1
                        else:
                            self.logger.error(f"解除用户 {user['wxid']} 的订单绑定失败")
                    else:
                        # 更新用户的过期时间
                        self.logger.info(f"更新用户 {user['wxid']} 的过期时间: {new_expires_at}")
                        self.db.add_vip_user(user['wxid'], expires_at=new_expires_at)
                        updated_count += 1
            
            self.logger.info(f"VIP用户状态维护完成: {expired_count} 个用户已过期，{updated_count} 个用户已更新")
            
            # 检查每个订单的绑定数量
            try:
                # 获取所有订单信息
                orders = self.db.get_vip_orders()
                for order in orders:
                    order_id = order['order_id']
                    max_total = order['max_vip_count'] + order['bonus_count']
                    bound_count = order['bound_count']
                    
                    if bound_count > max_total:
                        self.logger.info(f"订单 {order_id} 超出绑定限制: {bound_count}/{max_total}")
                        
                        # 获取该订单的所有绑定用户，按绑定时间排序
                        bound_users = self.db.get_order_users(order_id)
                        # 计算需要移除的数量
                        remove_count = bound_count - max_total
                        
                        # 从最早绑定的用户开始移除
                        for i in range(remove_count):
                            if i < len(bound_users):
                                user_to_remove = bound_users[i]
                                wxid = user_to_remove['wxid']
                                
                                # 发送通知
                                self.bot.client.post_text(
                                    self.bot.app_id,
                                    wxid,
                                    "由于订单绑定数超出限制，您的订单已被解除绑定"
                                )
                                
                                # 解除绑定
                                if self.db.unbind_vip_order(wxid):
                                    self.logger.info(f"已解除用户 {wxid} 的订单绑定")
                                    removed_count += 1
                                else:
                                    self.logger.error(f"解除用户 {wxid} 的订单绑定失败")
                                
                                time.sleep(1)  # 避免请求过于频繁
            except Exception as e:
                self.logger.error(f"检查订单绑定数量失败: {str(e)}")
            
            # 从VIP监控群移除非VIP用户
            try:
                # 重新获取所有VIP用户（已更新状态）
                vip_users = self.db.get_all_vip_users()
                
                # 获取有效VIP用户的wxid集合
                active_vip_wxids = set()
                for user in vip_users:
                    # 如果没有过期时间，则不加入集合
                    if not user['expires_at']:
                        continue
                    
                    if user.get('is_active', 0) == 1:
                        active_vip_wxids.add(user['wxid'])
                
                # 对所有配置的群组进行清理
                groups_to_clean = []
                
                # 添加master_group中的群
                if isinstance(self.bot.master_group, list):
                    for group in self.bot.master_group:
                        if group not in groups_to_clean:
                            groups_to_clean.append(group)
                elif self.bot.master_group and self.bot.master_group not in groups_to_clean:
                    groups_to_clean.append(self.bot.master_group)
                
                self.logger.info(f"准备清理 {len(groups_to_clean)} 个群组的非VIP用户")
                
                # 对每个群组进行清理
                for group_id in groups_to_clean:
                    try:
                        self.logger.info(f"开始清理群组 {group_id} 的非VIP用户")
                        
                        # 获取群成员列表
                        member_list_result = self.bot.client.get_chatroom_member_list(self.bot.app_id, group_id)
                        if member_list_result.get('ret') == 200 and member_list_result.get('data'):
                            group_data = member_list_result['data']
                            group_members = group_data.get('memberList', [])
                            chatroomOwner = group_data.get('chatroomOwner', '')  # 获取群主
                            adminWxids = group_data.get('adminWxid', [])  # 获取管理员列表
                            exclude_wxids = set()
                            for adminWxid in adminWxids:
                                if isinstance(adminWxid, dict):
                                    exclude_wxids.add(adminWxid.get('string', ''))
                                else:
                                    exclude_wxids.add(adminWxid)
                            exclude_wxids.add(chatroomOwner)
                            exclude_wxids.add(self.bot.wxid)  # 排除机器人自己
                            
                            # 找出需要移除的非VIP用户,排除群主和管理员
                            users_to_remove = []
                            for member in group_members:
                                wxid = member['wxid']
                                if wxid not in exclude_wxids and wxid not in active_vip_wxids:
                                    users_to_remove.append(wxid)
                            
                            # 批量移除非VIP用户
                            if users_to_remove:
                                self.logger.info(f"从群组 {group_id} 中发现 {len(users_to_remove)} 个非VIP用户需要移除")
                                
                                # 发送通知给每个用户
                                for user_remove in users_to_remove:
                                    # 获取用户信息
                                    user_info = self.db.get_vip_user(user_remove)
                                    if user_info:
                                        # 构建详细的通知消息
                                        notice = "您已被移出VIP群组\n\n原因："
                                        if not user_info.get('expires_at'):
                                            notice += "未设置VIP时长"
                                        else:
                                            try:
                                                expires_at = datetime.datetime.strptime(user_info['expires_at'], "%Y-%m-%d %H:%M:%S")
                                            except ValueError:
                                                expires_at_str = self._convert_iso_time(user_info['expires_at'])
                                                expires_at = datetime.datetime.strptime(expires_at_str, "%Y-%m-%d %H:%M:%S")
                                            
                                            if expires_at.timestamp() <= current_time:
                                                notice += f"VIP已于 {user_info['expires_at']} 到期"
                                            else:
                                                notice += "VIP状态异常"
                                        
                                        notice += "\n\n如需继续使用VIP功能，请重新购买或绑定新的订单。\n自助：#小程序://闲鱼/RmRsGfDjJNu7ZYA"
                                        
                                        try:
                                            self.bot.client.post_text(self.bot.app_id, user_remove, notice)
                                        except Exception as e:
                                            self.logger.error(f"发送通知给用户 {user_remove} 失败: {str(e)}")
                                    else:
                                        # 如果找不到用户信息，发送默认消息
                                        try:
                                            self.bot.client.post_text(self.bot.app_id, user_remove, "您已被移出VIP群组\n\n原因：VIP状态异常\n\n如需继续使用VIP功能，请重新购买或绑定新的订单。")
                                        except Exception as e:
                                            self.logger.error(f"发送通知给用户 {user_remove} 失败: {str(e)}")
                                    # time.sleep(3)  # 避免请求过于频繁
                                
                                # 分批移除用户
                                for i in range(0, len(users_to_remove), 3):
                                    batch = users_to_remove[i:i+3]
                                    wxids_str = ','.join(batch)
                                    result = self.bot.client.remove_member(self.bot.app_id, wxids_str, group_id)
                                    if result.get('ret') == 200:
                                        removed_count += len(batch)
                                        self.logger.info(f"已从群组 {group_id} 移除 {len(batch)} 个非VIP用户")
                                    else:
                                        self.logger.error(f"移除群成员失败: {result}")
                                    time.sleep(10)  # 避免请求过于频繁
                            else:
                                self.logger.info(f"群组 {group_id} 中所有用户都是有效VIP或管理员，无需移除")
                        else:
                            self.logger.error(f"获取群组 {group_id} 成员列表失败: {member_list_result}")
                    except Exception as e:
                        self.logger.error(f"清理群组 {group_id} 的非VIP用户失败: {str(e)}")
            except Exception as e:
                self.logger.error(f"从群组移除非VIP用户失败: {str(e)}")
            
            self.logger.info(f"VIP用户维护完成: {expired_count} 个用户已过期，{updated_count} 个用户已更新，{removed_count} 个非VIP用户已从监控群移除")
            return True
        except Exception as e:
            self.logger.error(f"维护VIP用户数据失败: {str(e)}\n{traceback.format_exc()}")
            return False

    def _handle_generate_card_command(self, from_user, sender_wxid, command):
        """处理生成卡密命令"""
        try:
            # 解析命令中的参数
            parts = command.split()
            if len(parts) < 2:
                self.bot.client.post_text(self.bot.app_id, from_user, "命令格式错误。使用'生成卡密 金额 [数量]'")
                return True
            
            try:
                amount = round(float(parts[1]), 2)
                if amount <= 0:
                    self.bot.client.post_text(self.bot.app_id, from_user, "金额必须大于0")
                    return True
            except ValueError:
                self.bot.client.post_text(self.bot.app_id, from_user, "无效的金额")
                return True
            
            count = 1  # 默认生成1个
            if len(parts) > 2:
                try:
                    count = int(parts[2])
                    if count < 1 or count > 100:  # 限制单次生成数量
                        self.bot.client.post_text(self.bot.app_id, from_user, "生成数量必须在1-100之间")
                        return True
                except ValueError:
                    self.bot.client.post_text(self.bot.app_id, from_user, "无效的数量参数")
                    return True
        
            # 生成卡密
            cards = []
            for _ in range(count):
                # 生成18位随机字符串（数字和小写字母）
                card_id = ''.join(random.choices(string.ascii_lowercase + string.digits, k=18))
                if self.db.add_vip_card(card_id, sender_wxid, amount):
                    cards.append(card_id)
        
            if cards:
                # 构建回复消息
                reply = f"成功生成 {len(cards)} 个充值卡密：\n\n"
                for i, card in enumerate(cards, 1):
                    reply += f"{card}\n"
                reply += f"\n卡密金额：{amount}元"
                
                self.bot.client.post_text(self.bot.app_id, from_user, reply)
            else:
                self.bot.client.post_text(self.bot.app_id, from_user, "生成卡密失败，请稍后重试")
        
            return True
        
        except Exception as e:
            self.logger.error(f"生成卡密失败: {str(e)}")
            self.bot.client.post_text(self.bot.app_id, from_user, "生成卡密失败，请稍后重试")
            return False

    def _handle_card_command(self, from_user, sender_wxid, card_id):
        """处理使用卡密命令"""
        try:
            # 检查卡密是否有效
            result = self.db.use_vip_card(card_id, sender_wxid)
            if not result['success']:
                self.bot.client.post_text(self.bot.app_id, from_user, result['message'])
                return True
            
            amount = result['amount']
            
            # 获取用户当前信息
            user_info = self.db.get_vip_user(sender_wxid)
            if user_info:
                new_balance = user_info.get('balance', 0)
            else:
                new_balance = amount
            
            # 获取VIP商品信息
            product = self.db.get_product("VIP")
            if not product:
                # 如果未找到VIP商品，则只显示充值成功信息
                reply = "充值成功！\n\n"
                reply += f"充值金额：{amount}元\n"
                reply += f"当前积分：{new_balance}\n\n"
                reply += "发送「VIP」查看详细信息"
                
                self.bot.client.post_text(self.bot.app_id, from_user, reply)
                return True
            
            # 检查余额是否足够购买VIP
            if new_balance < product['price']:
                reply = "充值成功！\n\n"
                reply += f"充值金额：{amount}元\n"
                reply += f"当前积分：{new_balance}\n\n"
                reply += f"当前积分不足以购买VIP（需要{product['price']}元），请继续充值或发送「购买VIP」查看详情"
                
                self.bot.client.post_text(self.bot.app_id, from_user, reply)
                return True
            
            # 自动执行购买VIP操作
            # 保存购买信息到数据库中
            self.db.set_pending_purchase(sender_wxid, product['product_id'])
            
            # 执行购买
            purchase_result = self.db.purchase_product(sender_wxid, product['product_id'])
            # 清除待购买状态
            self.db.clear_pending_purchase(sender_wxid)
            
            if purchase_result['success']:
                reply = "充值并自动购买VIP成功！\n\n"
                reply += f"充值金额：{amount}元\n"
                reply += f"VIP价格：{product['price']}元\n"
                reply += f"剩余积分：{purchase_result['balance']}\n"
                
                # 发送成功消息
                self.bot.client.post_text(self.bot.app_id, from_user, reply)
                
                # 自动发送VIP监控群邀请
                try:
                    # 发送VIP群邀请链接
                    chatroom_id = VIP监控群[1]
                    self.bot.client.invite_member(
                        self.bot.app_id, 
                        sender_wxid, 
                        chatroom_id, 
                        ""
                    )
                    
                    # 发送加入成功提示
                    time.sleep(1)  # 等待邀请处理
                    self.bot.client.post_text(
                        self.bot.app_id, 
                        from_user, 
                        "已邀请您加入VIP监控群，更多信息请发送「VIP」查看详情"
                    )
                except Exception as e:
                    self.logger.error(f"邀请用户 {sender_wxid} 加入VIP监控群失败: {str(e)}")
                    self.bot.client.post_text(
                        self.bot.app_id, 
                        from_user, 
                        "VIP已开通，但邀请您加入VIP监控群失败，请稍后发送「VIP监控」重试。"
                    )
            else:
                reply = f"充值成功但购买VIP失败：{purchase_result['message']}\n\n"
                reply += f"当前积分：{new_balance}\n"
                reply += "请稍后发送「购买VIP」手动购买"
                
                self.bot.client.post_text(self.bot.app_id, from_user, reply)
            
            return True
        
        except Exception as e:
            self.logger.error(f"使用卡密失败: {str(e)}")
            self.bot.client.post_text(self.bot.app_id, from_user, "使用卡密失败，请稍后重试")
            return False

    def _handle_purchase_command(self, from_user, sender_wxid, command):
        """处理购买命令"""
        try:
            # 解析商品名称
            product_name = command[2:].strip().upper()  # 移除"购买"二字
            if not product_name:
                self.bot.client.post_text(self.bot.app_id, from_user, "请指定要购买的商品")
                return True
            
            if product_name == "菜单":
                self.bot.client.post_text(self.bot.app_id, from_user, "建设中，当前只有\"购买VIP\"")
                return True

            # 获取商品信息
            product = self.db.get_product(product_name)
            if not product:
                self.bot.client.post_text(self.bot.app_id, from_user, "未找到该商品")
                return True
            
            # 获取用户信息
            user_info = self.db.get_vip_user(sender_wxid)
            current_balance = user_info.get('balance', 0) if user_info else 0
            
            # 构建确认消息
            reply = f"商品信息确认\n\n"
            reply += f"商品：{product['product_name']}\n"
            reply += f"描述：{product['description']}\n"
            reply += f"需要积分：{product['price']}\n"
            reply += f"当前积分：{current_balance}\n\n"
            
            if current_balance < product['price']:
                reply += "积分不足，请先充值"
            else:
                reply += "回复「确认购买」完成购买"
                # 保存购买信息到数据库中
                self.db.set_pending_purchase(sender_wxid, product['product_id'])
            
            self.bot.client.post_text(self.bot.app_id, from_user, reply)
            return True
        
        except Exception as e:
            self.logger.error(f"处理购买命令失败: {str(e)}")
            self.bot.client.post_text(self.bot.app_id, from_user, "处理购买请求失败，请稍后重试")
            return False

    def _handle_confirm_purchase(self, from_user, sender_wxid):
        """处理确认购买命令"""
        try:
            # 获取用户信息和待购买商品
            user_info = self.db.get_vip_user(sender_wxid)
            if not user_info or not user_info.get('pending_purchase'):
                self.bot.client.post_text(self.bot.app_id, from_user, "没有待确认的购买")
                return True
            
            # 获取商品信息
            product = self.db.get_product_by_id(user_info['pending_purchase'])
            if not product:
                self.db.clear_pending_purchase(sender_wxid)
                self.bot.client.post_text(self.bot.app_id, from_user, "商品信息已失效，请重新购买")
                return True
            
            # 检查余额
            current_balance = user_info.get('balance', 0)
            if current_balance < product['price']:
                self.db.clear_pending_purchase(sender_wxid)
                self.bot.client.post_text(self.bot.app_id, from_user, f"余额不足，当前余额：{current_balance}元")
                return True
            
            # 执行购买
            result = self.db.purchase_product(sender_wxid, user_info['pending_purchase'])
            # 清除待购买状态
            self.db.clear_pending_purchase(sender_wxid)
            
            if result['success']:
                reply = "购买成功！\n\n"
                reply += f"剩余余额：{result['balance']}元\n\n"
                
                # # 如果是自定义广告商品，更新机器人配置
                # if product['product_id'] == 'ad_custom' and product['action_type'] == 'show_ads':
                #     # 检查是否是副机器人
                #     if not self.bot.is_master:
                #         # 更新机器人信息
                #         if not hasattr(self.bot, 'bots_info'):
                #             self.bot.bots_info = self.bot.load_bots_info()
                        
                #         if self.bot.wxid and self.bot.wxid in self.bot.bots_info:
                #             self.bot.bots_info[self.bot.wxid]['show_ads'] = True
                #             self.bot.save_bots_info()
                #             reply += "已开启自定义广告位支持，发送'/bot 设置广告'可进行广告图片设置"
                #     else:
                #         reply += "主机器人不支持自定义广告设置"
                # else:
                reply += "发送「VIP」查看详细信息"
                
                self.bot.client.post_text(self.bot.app_id, from_user, reply)
            else:
                reply = f"购买失败：{result['message']}"
                self.bot.client.post_text(self.bot.app_id, from_user, reply)
            
            return True
        
        except Exception as e:
            self.logger.error(f"处理确认购买失败: {str(e)}")
            self.bot.client.post_text(self.bot.app_id, from_user, "处理购买请求失败，请稍后重试")
            return False

    def _handle_query_message_command(self, from_user, command):
        """
        处理查询原始消息的命令
        
        Args:
            from_user (str): 发送者的ID
            command (str): 命令内容，格式为"查询ID xx"，其中xx为消息ID
            
        Returns:
            bool: 处理成功返回True，否则返回False
        """
        try:
            # 解析命令中的消息ID
            parts = command.split()
            if len(parts) != 2:
                self.bot.client.post_text(self.bot.app_id, from_user, "命令格式错误，正确格式为：查文字 xx（其中xx为消息ID，范围1-99）")
                return True
                
            # 尝试将ID转换为整数
            try:
                msg_id = int(parts[1])
            except ValueError:
                self.bot.client.post_text(self.bot.app_id, from_user, f"无效的消息ID：{parts[1]}，ID应为1-99之间的数字")
                return True
                
            # 检查ID是否在合法范围内
            if msg_id < 1 or msg_id > self.max_message_id:
                self.bot.client.post_text(self.bot.app_id, from_user, f"消息ID超出范围，有效范围为1-{self.max_message_id}")
                return True
                
            # 查找消息内容
            if msg_id in self.original_message_cache:
                message_content = self.original_message_cache[msg_id]
                # 截断过长的消息
                if len(message_content) > 1000:
                    message_content = message_content[:997] + "..."
                response = f"消息ID {msg_id} 的内容：\n\n{message_content}"
                self.bot.client.post_text(self.bot.app_id, from_user, response)
            else:
                self.bot.client.post_text(self.bot.app_id, from_user, f"未找到ID为 {msg_id} 的消息，该消息可能已被覆盖")
                
            return True
            
        except Exception as e:
            self.logger.error(f"处理查询消息命令失败: {str(e)}")
            self.bot.client.post_text(self.bot.app_id, from_user, "处理查询消息命令时发生错误")
            return False

    def _handle_query_product_command(self, from_user, command):
        """
        处理查询商品的命令，并发送小程序卡片
        
        Args:
            from_user (str): 发送者的ID
            command (str): 命令内容，格式为"查商品 xx"，其中xx为商品ID
            
        Returns:
            bool: 处理成功返回True，否则返回False
        """
        try:
            # 解析命令中的商品ID
            parts = command.split()
            if len(parts) != 2:
                # self.bot.client.post_text(self.bot.app_id, from_user, "命令格式错误，正确格式为：查商品 xx（其中xx为商品ID）")
                return True
                
            # 获取商品ID
            product_id = parts[1]
            store_id = "1"
            
            # 直接组合小程序XML
            xml_content = f'''<msg>
    <appmsg appid="" sdkver="0">
        <title>商品详情</title>
        <des></des>
        <action>view</action>
        <type>33</type>
        <showtype>0</showtype>
        <content></content>
        <url></url>
        <dataurl></dataurl>
        <lowurl></lowurl>
        <lowdataurl></lowdataurl>
        <recorditem></recorditem>
        <thumburl></thumburl>
        <messageaction></messageaction>
        <md5></md5>
        <extinfo></extinfo>
        <sourceusername></sourceusername>
        <sourcedisplayname></sourcedisplayname>
        <commenturl></commenturl>
        <appattach>
            <totallen>0</totallen>
            <attachid></attachid>
            <emoticonmd5></emoticonmd5>
            <fileext></fileext>
            <cdnthumbaeskey></cdnthumbaeskey>
            <aeskey></aeskey>
            <cdnthumburl></cdnthumburl>
            <cdnthumblength>0</cdnthumblength>
            <cdnthumbheight>0</cdnthumbheight>
            <cdnthumbwidth>0</cdnthumbwidth>
        </appattach>
        <weappinfo>
            <username><![CDATA[gh_9d8815c84ea4@app]]></username>
            <appid><![CDATA[wx9627eb7f4b1c69d5]]></appid>
            <type>2</type>
            <version>0</version>
            <weappiconurl><![CDATA[http://mmbiz.qpic.cn/mmbiz_png/vVr3uFOB5gZ22FaJjuPVmvzkEm3w8CyIvKUcULhYnEJ4HG8TBH3jSPF5lXNAZeJGBts4SVTupRZmsJ6yrW6bnw/640?wx_fmt=png&wxfrom=200]]></weappiconurl>
            <pagepath><![CDATA[packages/storeGoGoodsDetail/pages/goodsDetail/goodsDetail.html?goodsSpuId={product_id}&storeID={store_id}&sharePhone=13726277667&shopType=1&moduleName=分享卡片&txsrShareInfoSdk=%7B%22mi%22%3A%221acbdc275a7a0b61f3769f92c2d0f0b6%22%2C%22d%22%3A1%2C%22o%22%3A%221acbdc275a7a0b61f3769f92c2d0f0b6%22%7D&sampshare=%7B%22i%22%3A%2225961693%22%2C%22p%22%3A%22packages%2FstoreGoGoodsDetail%2Fpages%2FgoodsDetail%2FgoodsDetail%22%2C%22d%22%3A0%2C%22m%22%3A%22%E8%BD%AC%E5%8F%91%E6%B6%88%E6%81%AF%E5%8D%A1%E7%89%87%22%7D]]></pagepath>
            <shareId><![CDATA[0_wx9627eb7f4b1c69d5_25984982468406939@openim_1744346579_0]]></shareId>
            <appservicetype>0</appservicetype>
            <pkginfo>
                <type>0</type>
            </pkginfo>
        </weappinfo>
        <appinfo>
            <version>1</version>
            <appname></appname>
        </appinfo>
        <fromusername>25984982468406939@openim</fromusername>
        <scene>0</scene>
        <commenturl></commenturl>
    </appmsg>
</msg>'''

            # 使用转发接口发送小程序
            self.bot.client.forward_mini_app(
                self.bot.app_id,
                from_user,
                xml_content,
                ''
            )
            
            self.logger.info(f"成功发送商品小程序: 商品ID={product_id}, 到用户={from_user}")
            return True
            
        except Exception as e:
            self.logger.error(f"处理查询商品命令失败: {str(e)}")
            self.bot.client.post_text(self.bot.app_id, from_user, "处理查询商品命令时发生错误")
            return False

    def _cleanup_message_cache(self, current_time):
        """清理过期的消息缓存"""
        try:
            # 使用list()创建items的副本进行遍历
            expired_keys = [
                k for k, v in list(self.message_cache.items())
                if current_time - v > self.message_cache_ttl
            ]
            
            # 批量删除过期的键
            for k in expired_keys:
                self.message_cache.pop(k, None)  # 使用pop替代del，避免KeyError
                
            if expired_keys:
                self.logger.debug(f"已清理 {len(expired_keys)} 个过期消息缓存")
                
        except Exception as e:
            self.logger.error(f"清理消息缓存失败: {str(e)}")
            
    def _process_ad_image(self, from_user, image_xml):
        """处理广告图片
        
        Args:
            from_user: 发送者ID
            image_xml: 图片XML内容
            
        Returns:
            bool: 处理成功返回True，否则返回False
        """
        try:
            # 优先尝试下载高清图片，然后依次尝试常规图片和缩略图
            image_url = None
            for img_type in [1, 2, 3]:  # 1:高清图片 2:常规图片 3:缩略图
                try:
                    # 使用client的download_image方法下载图片
                    response = self.bot.client.download_image(self.bot.app_id, image_xml, img_type)
                    
                    if response and response.get("ret") == 200 and response.get("data", {}).get("fileUrl"):
                        # 根据base_url构建图片URL
                        # base_url格式例如: "http://**************:2531/v2/api"
                        base_url = self.bot.base_url
                        
                        # 提取协议、IP和端口，忽略路径部分
                        if '://' in base_url:
                            protocol = base_url.split('://')[0]
                            rest = base_url.split('://')[1]
                            
                            # 提取IP和端口
                            if ':' in rest:
                                ip = rest.split(':')[0]
                                port_and_path = rest.split(':')[1]
                                
                                # 提取端口
                                if '/' in port_and_path:
                                    port = port_and_path.split('/')[0]
                                else:
                                    port = port_and_path
                                    
                                # 构建新URL，端口+1
                                new_port = int(port) + 1
                                image_url = f"{protocol}://{ip}:{new_port}/download/{response['data']['fileUrl']}"
                            else:
                                # 没有明确的端口，使用原始URL
                                image_url = f"{self.bot.base_url}{response['data']['fileUrl']}"
                        else:
                            # URL格式不符合预期，使用原始URL
                            image_url = f"{self.bot.base_url}{response['data']['fileUrl']}"
                        
                        self.logger.info(f"成功下载图片类型: {img_type}, URL: {image_url}")
                        break
                except Exception as e:
                    self.logger.error(f"下载图片类型 {img_type} 失败: {str(e)}")
                    continue
            
            if not image_url:
                self.bot.client.post_text(self.bot.app_id, from_user, "下载图片失败，请重试")
                return False
                
            # 下载图片内容
            img_response = requests.get(image_url)
            if img_response.status_code != 200:
                self.logger.error(f"获取图片内容失败，状态码: {img_response.status_code}, URL: {image_url}")
                self.bot.client.post_text(self.bot.app_id, from_user, "获取图片内容失败，请重试")
                return False
                
            # 保存为临时文件
            temp_path = f"product_qr_img/{self.bot.wxid}/AD_temp.png"
            os.makedirs(os.path.dirname(temp_path), exist_ok=True)
            with open(temp_path, 'wb') as f:
                f.write(img_response.content)

            # 生成预览图
            self._merge_images(temp_path, 'AD_preview', True)
                
            # 发送预览图和确认信息
            self.bot.client.post_text(self.bot.app_id, from_user, "下面是广告图片预览效果，回复「确认」完成设置，或发送新的图片重新设置")
            preview_url = f"{self.bot.ext_url}/product_qr_img/{self.bot.wxid}/AD_preview.jpeg"
            self.bot.client.post_image(self.bot.app_id, from_user, preview_url)
            
            return True
                
        except Exception as e:
            self.logger.error(f"处理广告图片失败: {str(e)}")
            self.bot.client.post_text(self.bot.app_id, from_user, "处理图片失败，请重试")
            return False
            
    def _confirm_ad_image(self, from_user):
        """确认使用当前的广告图片
        
        Args:
            from_user: 发送者ID
            
        Returns:
            bool: 确认成功返回True，否则返回False
        """
        try:
            # 检查发送者是否为要求设置广告的用户
            if from_user != self.ad_image_sender:
                return False
                
            # 获取临时广告图片路径
            temp_path = f"product_qr_img/{self.bot.wxid}/AD_temp.png"
            if not temp_path or not os.path.exists(temp_path):
                self.bot.client.post_text(self.bot.app_id, from_user, "没有待确认的广告图片")
                return False
                
            # 移动临时文件到正式位置
            final_path = f"product_qr_img/{self.bot.wxid}/AD.png"
            os.makedirs(os.path.dirname(final_path), exist_ok=True)
            os.rename(temp_path, final_path)

            self.bot.client.post_text(self.bot.app_id, from_user, "广告图片设置成功，开始清理缓存")

            # 设置正在等待接收广告图片的状态
            self.waiting_ad_image = False
            self.ad_image_sender = None

            # 清理缓存
            for files in os.listdir(f"product_qr_img/{self.bot.wxid}"):
                if files.endswith(".jpeg"):
                    os.remove(os.path.join(f"product_qr_img/{self.bot.wxid}",files))
            
            self.bot.client.post_text(self.bot.app_id, from_user, "缓存清理完成，新的图片将在机器人重启后生成\n\n由于生成图片需要占用大量服务器资源，请联系群主在服务器空闲时执行")
            
            return True
            
        except Exception as e:
            self.logger.error(f"确认广告图片失败: {str(e)}")
            self.bot.client.post_text(self.bot.app_id, from_user, "设置失败，请联系群主")
            self.waiting_ad_image = False
            self.ad_image_sender = None
            return False

    def _calculate_salt_num(self, salt):
        """计算盐值的数字表示（简单哈希）"""
        salt_num = 0
        for char in salt:
            salt_num += ord(char)
        return salt_num

    def _encode_shop_goods(self, goods_id, store_id):
        """编码商品ID和店铺ID为单一参数"""
        try:
            # 将ID转换为数字
            num_goods_id = int(goods_id)
            num_store_id = int(store_id)
            
            if num_store_id >= 10000:
                self.logger.warning(f"店铺ID '{store_id}' 必须小于10000，无法编码")
                return None
            
            # 计算盐的数字表示
            salt_num = self._calculate_salt_num('ppmtcnmybayyds')
            
            # 组合ID（商品ID*10000 + 店铺ID）并加盐
            encoded = (num_goods_id * 10000 + num_store_id) + salt_num
            return str(encoded)
        except (ValueError, TypeError):
            # 出错时返回空
            self.logger.warning(f"无法编码ID '{goods_id}' 和 '{store_id}'")
            return None
