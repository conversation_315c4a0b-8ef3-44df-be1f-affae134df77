#!/usr/bin/env python3
"""
VIP数据库迁移脚本
将vip_cards表的agent_id字段改为agent_group字段，简化查询逻辑
"""
import sys
import os
import logging
from datetime import datetime

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app.models import get_db_connection

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class VIPDatabaseMigration:
    """VIP数据库迁移器"""

    def __init__(self):
        self.connection = None

    def connect(self):
        """连接数据库"""
        try:
            self.connection = get_db_connection()
            logger.info("数据库连接成功")
            return True
        except Exception as e:
            logger.error(f"数据库连接失败: {e}")
            return False

    def close(self):
        """关闭数据库连接"""
        if self.connection:
            self.connection.close()
            logger.info("数据库连接已关闭")

    def backup_vip_cards_table(self):
        """备份vip_cards表"""
        try:
            with self.connection.cursor() as cursor:
                # 创建备份表
                backup_table_name = f"vip_cards_backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}"

                logger.info(f"创建备份表: {backup_table_name}")
                cursor.execute(f"""
                    CREATE TABLE {backup_table_name} AS
                    SELECT * FROM vip_cards
                """)

                # 检查备份数据
                cursor.execute(f"SELECT COUNT(*) FROM {backup_table_name}")
                backup_count = cursor.fetchone()[0]

                cursor.execute("SELECT COUNT(*) FROM vip_cards")
                original_count = cursor.fetchone()[0]

                if backup_count == original_count:
                    logger.info(f"备份成功: {backup_count} 条记录")
                    return backup_table_name
                else:
                    logger.error(f"备份失败: 原表 {original_count} 条，备份表 {backup_count} 条")
                    return None

        except Exception as e:
            logger.error(f"备份失败: {e}")
            return None

    def check_current_structure(self):
        """检查当前表结构"""
        try:
            with self.connection.cursor() as cursor:
                # 检查vip_cards表结构
                cursor.execute("DESCRIBE vip_cards")
                columns = cursor.fetchall()

                logger.info("当前vip_cards表结构:")
                for col in columns:
                    logger.info(f"  {col[0]} - {col[1]} - {col[2]} - {col[3]}")

                # 检查是否已有agent_group字段
                column_names = [col[0] for col in columns]
                has_agent_id = 'agent_id' in column_names
                has_agent_group = 'agent_group' in column_names

                logger.info(f"agent_id字段存在: {has_agent_id}")
                logger.info(f"agent_group字段存在: {has_agent_group}")

                return has_agent_id, has_agent_group

        except Exception as e:
            logger.error(f"检查表结构失败: {e}")
            return False, False

    def get_agent_mapping(self):
        """获取agent_id到agent_group的映射"""
        try:
            with self.connection.cursor() as cursor:
                cursor.execute("SELECT id, `group` FROM agents")
                agents = cursor.fetchall()

                mapping = {}
                for agent in agents:
                    agent_id, group = agent
                    mapping[agent_id] = group

                logger.info(f"获取到 {len(mapping)} 个代理商映射")
                for agent_id, group in mapping.items():
                    logger.info(f"  代理ID {agent_id} -> 组 {group}")

                return mapping

        except Exception as e:
            logger.error(f"获取代理商映射失败: {e}")
            return {}

    def add_agent_group_column(self):
        """添加agent_group字段"""
        try:
            with self.connection.cursor() as cursor:
                logger.info("添加agent_group字段...")
                cursor.execute("""
                    ALTER TABLE vip_cards
                    ADD COLUMN agent_group VARCHAR(50) NOT NULL DEFAULT ''
                    COMMENT '代理组名称'
                """)

                # 添加索引
                cursor.execute("""
                    CREATE INDEX idx_vip_cards_agent_group ON vip_cards(agent_group)
                """)

                logger.info("agent_group字段添加成功")
                return True

        except Exception as e:
            logger.error(f"添加agent_group字段失败: {e}")
            return False

    def migrate_data(self, agent_mapping):
        """迁移数据：将agent_id转换为agent_group"""
        try:
            with self.connection.cursor() as cursor:
                logger.info("开始数据迁移...")

                # 获取所有VIP卡记录
                cursor.execute("SELECT id, agent_id FROM vip_cards")
                vip_cards = cursor.fetchall()

                migrated_count = 0
                failed_count = 0

                for card_id, agent_id in vip_cards:
                    if agent_id in agent_mapping:
                        agent_group = agent_mapping[agent_id]

                        # 更新agent_group字段
                        cursor.execute("""
                            UPDATE vip_cards
                            SET agent_group = %s
                            WHERE id = %s
                        """, (agent_group, card_id))

                        migrated_count += 1
                    else:
                        logger.warning(f"VIP卡 {card_id} 的代理ID {agent_id} 未找到对应的组")
                        failed_count += 1

                logger.info(f"数据迁移完成: 成功 {migrated_count} 条，失败 {failed_count} 条")
                return migrated_count, failed_count

        except Exception as e:
            logger.error(f"数据迁移失败: {e}")
            return 0, 0

    def verify_migration(self, agent_mapping):
        """验证迁移结果"""
        try:
            with self.connection.cursor() as cursor:
                # 检查所有记录是否都有agent_group
                cursor.execute("SELECT COUNT(*) FROM vip_cards WHERE agent_group = '' OR agent_group IS NULL")
                empty_group_count = cursor.fetchone()[0]

                if empty_group_count > 0:
                    logger.warning(f"发现 {empty_group_count} 条记录的agent_group为空")
                    return False

                # 验证数据一致性
                cursor.execute("""
                    SELECT agent_id, agent_group, COUNT(*)
                    FROM vip_cards
                    GROUP BY agent_id, agent_group
                """)
                groups = cursor.fetchall()

                logger.info("迁移验证结果:")
                for agent_id, agent_group, count in groups:
                    expected_group = agent_mapping.get(agent_id, "UNKNOWN")
                    status = "OK" if agent_group == expected_group else "ERROR"
                    logger.info(f"  代理ID {agent_id} -> 组 {agent_group} ({count} 条记录) [{status}]")

                return True

        except Exception as e:
            logger.error(f"验证迁移失败: {e}")
            return False

    def run_migration(self, remove_agent_id=False):
        """执行完整迁移"""
        logger.info("=" * 60)
        logger.info("开始VIP数据库迁移")
        logger.info("=" * 60)

        try:
            # 1. 连接数据库
            if not self.connect():
                return False

            # 2. 检查当前结构
            has_agent_id, has_agent_group = self.check_current_structure()

            if not has_agent_id:
                logger.error("未找到agent_id字段，无需迁移")
                return False

            if has_agent_group:
                logger.warning("agent_group字段已存在，跳过添加步骤")

            # 3. 备份表
            backup_table = self.backup_vip_cards_table()
            if not backup_table:
                logger.error("备份失败，停止迁移")
                return False

            # 4. 获取代理商映射
            agent_mapping = self.get_agent_mapping()
            if not agent_mapping:
                logger.error("获取代理商映射失败，停止迁移")
                return False

            # 5. 添加agent_group字段
            if not has_agent_group:
                if not self.add_agent_group_column():
                    logger.error("添加agent_group字段失败，停止迁移")
                    return False

            # 6. 迁移数据
            migrated, failed = self.migrate_data(agent_mapping)
            if failed > 0:
                logger.warning(f"有 {failed} 条记录迁移失败")

            # 7. 验证迁移
            if not self.verify_migration(agent_mapping):
                logger.error("迁移验证失败")
                return False

            # 8. 提交事务
            self.connection.commit()
            logger.info("迁移事务已提交")

            logger.info("=" * 60)
            logger.info("VIP数据库迁移完成！")
            logger.info(f"备份表: {backup_table}")
            logger.info(f"迁移记录: {migrated} 条")
            logger.info("=" * 60)

            return True

        except Exception as e:
            logger.error(f"迁移过程中出现异常: {e}")
            if self.connection:
                self.connection.rollback()
                logger.info("已回滚事务")
            return False

        finally:
            self.close()


def main():
    """主函数"""
    migration = VIPDatabaseMigration()
    success = migration.run_migration()
    sys.exit(0 if success else 1)


if __name__ == '__main__':
    main()
    
    def verify_migration(self, agent_mapping):
        """验证迁移结果"""
        try:
            with self.connection.cursor() as cursor:
                # 检查所有记录是否都有agent_group
                cursor.execute("SELECT COUNT(*) FROM vip_cards WHERE agent_group = '' OR agent_group IS NULL")
                empty_group_count = cursor.fetchone()[0]
                
                if empty_group_count > 0:
                    logger.warning(f"发现 {empty_group_count} 条记录的agent_group为空")
                    return False
                
                # 验证数据一致性
                cursor.execute("""
                    SELECT agent_id, agent_group, COUNT(*) 
                    FROM vip_cards 
                    GROUP BY agent_id, agent_group
                """)
                groups = cursor.fetchall()
                
                logger.info("迁移验证结果:")
                for agent_id, agent_group, count in groups:
                    expected_group = agent_mapping.get(agent_id, "UNKNOWN")
                    status = "✓" if agent_group == expected_group else "✗"
                    logger.info(f"  代理ID {agent_id} -> 组 {agent_group} ({count} 条记录) {status}")
                
                return True
                
        except Exception as e:
            logger.error(f"验证迁移失败: {e}")
            return False
    
    def remove_agent_id_column(self):
        """移除agent_id字段（可选）"""
        try:
            with self.connection.cursor() as cursor:
                logger.info("移除agent_id字段...")
                
                # 先移除外键约束（如果存在）
                try:
                    cursor.execute("ALTER TABLE vip_cards DROP FOREIGN KEY vip_cards_ibfk_1")
                except:
                    pass  # 外键可能不存在
                
                # 移除索引
                try:
                    cursor.execute("DROP INDEX agent_id ON vip_cards")
                except:
                    pass  # 索引可能不存在
                
                # 移除字段
                cursor.execute("ALTER TABLE vip_cards DROP COLUMN agent_id")
                
                logger.info("agent_id字段移除成功")
                return True
                
        except Exception as e:
            logger.error(f"移除agent_id字段失败: {e}")
            return False
    
    def run_migration(self, remove_agent_id=False):
        """执行完整迁移"""
        logger.info("=" * 60)
        logger.info("开始VIP数据库迁移")
        logger.info("=" * 60)
        
        try:
            # 1. 连接数据库
            if not self.connect():
                return False
            
            # 2. 检查当前结构
            has_agent_id, has_agent_group = self.check_current_structure()
            
            if not has_agent_id:
                logger.error("未找到agent_id字段，无需迁移")
                return False
            
            if has_agent_group:
                logger.warning("agent_group字段已存在，跳过添加步骤")
            
            # 3. 备份表
            backup_table = self.backup_vip_cards_table()
            if not backup_table:
                logger.error("备份失败，停止迁移")
                return False
            
            # 4. 获取代理商映射
            agent_mapping = self.get_agent_mapping()
            if not agent_mapping:
                logger.error("获取代理商映射失败，停止迁移")
                return False
            
            # 5. 添加agent_group字段
            if not has_agent_group:
                if not self.add_agent_group_column():
                    logger.error("添加agent_group字段失败，停止迁移")
                    return False
            
            # 6. 迁移数据
            migrated, failed = self.migrate_data(agent_mapping)
            if failed > 0:
                logger.warning(f"有 {failed} 条记录迁移失败")
            
            # 7. 验证迁移
            if not self.verify_migration(agent_mapping):
                logger.error("迁移验证失败")
                return False
            
            # 8. 提交事务
            self.connection.commit()
            logger.info("迁移事务已提交")
            
            # 9. 可选：移除agent_id字段
            if remove_agent_id:
                if self.remove_agent_id_column():
                    self.connection.commit()
                    logger.info("agent_id字段已移除")
                else:
                    logger.warning("移除agent_id字段失败，但迁移已完成")
            
            logger.info("=" * 60)
            logger.info("VIP数据库迁移完成！")
            logger.info(f"备份表: {backup_table}")
            logger.info(f"迁移记录: {migrated} 条")
            logger.info("=" * 60)
            
            return True
            
        except Exception as e:
            logger.error(f"迁移过程中出现异常: {e}")
            if self.connection:
                self.connection.rollback()
                logger.info("已回滚事务")
            return False
        
        finally:
            self.close()


def main():
    """主函数"""
    import argparse
    
    parser = argparse.ArgumentParser(description='VIP数据库迁移工具')
    parser.add_argument('--remove-agent-id', action='store_true', 
                       help='迁移完成后移除agent_id字段')
    parser.add_argument('--dry-run', action='store_true',
                       help='只检查不执行迁移')
    
    args = parser.parse_args()
    
    migration = VIPDatabaseMigration()
    
    if args.dry_run:
        logger.info("执行干运行模式...")
        if migration.connect():
            migration.check_current_structure()
            migration.get_agent_mapping()
            migration.close()
    else:
        success = migration.run_migration(remove_agent_id=args.remove_agent_id)
        sys.exit(0 if success else 1)


if __name__ == '__main__':
    main()
