/**
 * 统一Web管理系统主要JavaScript文件
 */

$(document).ready(function() {
    // 初始化所有功能
    initSidebar();
    initTooltips();
    initLoadingOverlay();
    initFormValidation();
    initTableFeatures();
    initAnimations();
});

/**
 * 侧边栏功能
 */
function initSidebar() {
    const sidebar = $('#sidebar');
    const sidebarToggle = $('#sidebarToggle');
    const mainContent = $('.main-content');
    
    // 侧边栏切换
    sidebarToggle.on('click', function() {
        sidebar.toggleClass('collapsed');
        
        // 保存状态到localStorage
        const isCollapsed = sidebar.hasClass('collapsed');
        localStorage.setItem('sidebarCollapsed', isCollapsed);
    });
    
    // 恢复侧边栏状态
    const savedState = localStorage.getItem('sidebarCollapsed');
    if (savedState === 'true') {
        sidebar.addClass('collapsed');
    }
    
    // 移动端侧边栏
    if (window.innerWidth <= 768) {
        sidebar.addClass('collapsed');
        
        sidebarToggle.on('click', function() {
            sidebar.toggleClass('show');
        });
        
        // 点击主内容区域时隐藏侧边栏
        mainContent.on('click', function() {
            if (sidebar.hasClass('show')) {
                sidebar.removeClass('show');
            }
        });
    }
}

/**
 * 初始化工具提示
 */
function initTooltips() {
    const tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
    tooltipTriggerList.map(function (tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });
}

/**
 * 加载指示器
 */
function initLoadingOverlay() {
    window.showLoading = function(text = '加载中...') {
        $('#loadingOverlay .loading-text').text(text);
        $('#loadingOverlay').fadeIn(200);
    };
    
    window.hideLoading = function() {
        $('#loadingOverlay').fadeOut(200);
    };
    
    // 表单提交时显示加载指示器
    $('form').on('submit', function() {
        const submitBtn = $(this).find('button[type="submit"]');
        const originalText = submitBtn.text();
        
        submitBtn.prop('disabled', true).html('<i class="bi bi-hourglass-split me-2"></i>处理中...');
        showLoading('正在处理请求...');
        
        // 如果5秒后还没有响应，恢复按钮状态
        setTimeout(function() {
            submitBtn.prop('disabled', false).text(originalText);
            hideLoading();
        }, 5000);
    });
}

/**
 * 表单验证
 */
function initFormValidation() {
    // Bootstrap表单验证
    const forms = document.querySelectorAll('.needs-validation');
    
    Array.prototype.slice.call(forms).forEach(function(form) {
        form.addEventListener('submit', function(event) {
            if (!form.checkValidity()) {
                event.preventDefault();
                event.stopPropagation();
            }
            form.classList.add('was-validated');
        }, false);
    });
    
    // 实时验证
    $('.form-control').on('input', function() {
        const input = $(this);
        const isValid = input[0].checkValidity();
        
        input.removeClass('is-valid is-invalid');
        if (input.val().length > 0) {
            input.addClass(isValid ? 'is-valid' : 'is-invalid');
        }
    });
}

/**
 * 表格功能
 */
function initTableFeatures() {
    // 表格行点击效果
    $('.table tbody tr').on('click', function() {
        $(this).addClass('table-active').siblings().removeClass('table-active');
    });
    
    // 全选功能
    $('.select-all').on('change', function() {
        const isChecked = $(this).prop('checked');
        $('.select-item').prop('checked', isChecked);
        updateBatchActions();
    });
    
    $('.select-item').on('change', function() {
        updateBatchActions();
        
        // 更新全选状态
        const totalItems = $('.select-item').length;
        const checkedItems = $('.select-item:checked').length;
        $('.select-all').prop('checked', totalItems === checkedItems);
    });
    
    function updateBatchActions() {
        const checkedItems = $('.select-item:checked').length;
        $('.batch-actions').toggle(checkedItems > 0);
        $('.selected-count').text(checkedItems);
    }
}

/**
 * 动画效果
 */
function initAnimations() {
    // 页面加载动画
    $('.card, .alert').each(function(index) {
        $(this).css({
            'opacity': '0',
            'transform': 'translateY(20px)'
        }).delay(index * 100).animate({
            'opacity': '1'
        }, 500).css('transform', 'translateY(0)');
    });
    
    // 按钮悬停效果
    $('.btn').on('mouseenter', function() {
        $(this).addClass('shadow-lg');
    }).on('mouseleave', function() {
        $(this).removeClass('shadow-lg');
    });
    
    // 卡片悬停效果
    $('.card').on('mouseenter', function() {
        $(this).addClass('shadow-lg');
    }).on('mouseleave', function() {
        $(this).removeClass('shadow-lg');
    });
}

/**
 * 通用工具函数
 */

// 显示确认对话框
function showConfirm(message, callback) {
    if (confirm(message)) {
        callback();
    }
}

// 显示成功消息
function showSuccess(message) {
    showAlert(message, 'success');
}

// 显示错误消息
function showError(message) {
    showAlert(message, 'danger');
}

// 显示警告消息
function showWarning(message) {
    showAlert(message, 'warning');
}

// 显示信息消息
function showInfo(message) {
    showAlert(message, 'info');
}

// 通用消息显示函数
function showAlert(message, type = 'info') {
    const alertHtml = `
        <div class="alert alert-${type} alert-dismissible fade show" role="alert">
            <i class="bi bi-${getAlertIcon(type)} me-2"></i>
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    `;
    
    $('.alerts-container').prepend(alertHtml);
    
    // 自动隐藏
    setTimeout(function() {
        $('.alert').first().alert('close');
    }, 5000);
}

// 获取消息图标
function getAlertIcon(type) {
    const icons = {
        'success': 'check-circle',
        'danger': 'exclamation-triangle',
        'warning': 'exclamation-circle',
        'info': 'info-circle'
    };
    return icons[type] || 'info-circle';
}

// 格式化日期
function formatDate(dateString) {
    const date = new Date(dateString);
    return date.toLocaleString('zh-CN');
}

// 复制到剪贴板
function copyToClipboard(text) {
    navigator.clipboard.writeText(text).then(function() {
        showSuccess('已复制到剪贴板');
    }).catch(function() {
        showError('复制失败');
    });
}

// AJAX请求封装
function apiRequest(url, options = {}) {
    const defaultOptions = {
        method: 'GET',
        headers: {
            'Content-Type': 'application/json',
        }
    };
    
    const finalOptions = { ...defaultOptions, ...options };
    
    showLoading();
    
    return fetch(url, finalOptions)
        .then(response => {
            hideLoading();
            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }
            return response.json();
        })
        .catch(error => {
            hideLoading();
            showError('请求失败: ' + error.message);
            throw error;
        });
}

/**
 * 高级UI组件和交互效果
 */

// 数据表格增强
function initDataTable() {
    // 表格行选择
    $('.data-table tbody tr').on('click', function(e) {
        if (!$(e.target).closest('.action-buttons').length) {
            $(this).toggleClass('selected').siblings().removeClass('selected');
        }
    });

    // 表格排序
    $('.data-table thead th[data-sort]').on('click', function() {
        const column = $(this).data('sort');
        const direction = $(this).hasClass('sort-asc') ? 'desc' : 'asc';

        // 更新排序图标
        $('.data-table thead th').removeClass('sort-asc sort-desc');
        $(this).addClass(`sort-${direction}`);

        // 触发排序事件
        $(document).trigger('table:sort', { column, direction });
    });
}

// 搜索框增强
function initSearchBox() {
    let searchTimeout;

    $('.search-box input').on('input', function() {
        const query = $(this).val();
        const $this = $(this);

        clearTimeout(searchTimeout);
        searchTimeout = setTimeout(function() {
            // 触发搜索事件
            $(document).trigger('search:query', { query, element: $this });
        }, 300);
    });

    // 搜索框清除按钮
    $('.search-box').each(function() {
        const $input = $(this).find('input');
        const $clearBtn = $('<button type="button" class="search-clear"><i class="bi bi-x"></i></button>');

        $clearBtn.on('click', function() {
            $input.val('').trigger('input').focus();
        });

        $(this).append($clearBtn);

        $input.on('input', function() {
            $clearBtn.toggle($(this).val().length > 0);
        });
    });
}

// 状态徽章动画
function initStatusBadges() {
    $('.status-badge').each(function() {
        const $badge = $(this);
        const status = $badge.text().toLowerCase();

        // 添加状态图标
        let icon = '';
        if (status.includes('激活') || status.includes('在线') || status.includes('成功')) {
            icon = '<i class="bi bi-check-circle"></i>';
            $badge.addClass('status-active');
        } else if (status.includes('停用') || status.includes('离线') || status.includes('失败')) {
            icon = '<i class="bi bi-x-circle"></i>';
            $badge.addClass('status-inactive');
        } else if (status.includes('警告') || status.includes('待处理')) {
            icon = '<i class="bi bi-exclamation-triangle"></i>';
            $badge.addClass('status-warning');
        } else if (status.includes('错误') || status.includes('异常')) {
            icon = '<i class="bi bi-exclamation-circle"></i>';
            $badge.addClass('status-error');
        }

        if (icon) {
            $badge.prepend(icon);
        }
    });
}

// 操作按钮增强
function initActionButtons() {
    // 编辑按钮
    $(document).on('click', '.action-btn.btn-edit', function(e) {
        e.preventDefault();
        const id = $(this).data('id');
        const url = $(this).data('url') || $(this).attr('href');

        if (url) {
            window.location.href = url;
        } else {
            $(document).trigger('action:edit', { id, element: $(this) });
        }
    });

    // 删除按钮
    $(document).on('click', '.action-btn.btn-delete', function(e) {
        e.preventDefault();
        const id = $(this).data('id');
        const name = $(this).data('name') || '该项目';
        const url = $(this).data('url') || $(this).attr('href');

        showConfirm(`确定要删除 ${name} 吗？此操作不可恢复。`, function() {
            if (url) {
                // 发送删除请求
                apiRequest(url, { method: 'POST' })
                    .then(response => {
                        if (response.success) {
                            showSuccess(response.message || '删除成功');
                            // 刷新页面或移除行
                            setTimeout(() => window.location.reload(), 1000);
                        } else {
                            showError(response.message || '删除失败');
                        }
                    })
                    .catch(error => {
                        showError('删除失败: ' + error.message);
                    });
            } else {
                $(document).trigger('action:delete', { id, element: $(this) });
            }
        });
    });

    // 查看按钮
    $(document).on('click', '.action-btn.btn-view', function(e) {
        e.preventDefault();
        const id = $(this).data('id');
        const url = $(this).data('url') || $(this).attr('href');

        if (url) {
            window.location.href = url;
        } else {
            $(document).trigger('action:view', { id, element: $(this) });
        }
    });
}

// 进度条动画
function animateProgressBar(selector, targetPercent, duration = 1000) {
    const $progressBar = $(selector);
    const $fill = $progressBar.find('.progress-fill');

    $({ percent: 0 }).animate({ percent: targetPercent }, {
        duration: duration,
        easing: 'easeOutCubic',
        step: function(now) {
            $fill.css('width', now + '%');
        },
        complete: function() {
            $fill.css('width', targetPercent + '%');
        }
    });
}

// 模态框增强
function initModalEnhancements() {
    // 模态框打开动画
    $('.modal').on('show.bs.modal', function() {
        $(this).find('.modal-dialog').css({
            'transform': 'scale(0.8) translateY(-50px)',
            'opacity': '0'
        });
    });

    $('.modal').on('shown.bs.modal', function() {
        $(this).find('.modal-dialog').animate({
            'transform': 'scale(1) translateY(0)',
            'opacity': '1'
        }, 300);
    });

    // 模态框关闭动画
    $('.modal').on('hide.bs.modal', function() {
        $(this).find('.modal-dialog').animate({
            'transform': 'scale(0.8) translateY(-50px)',
            'opacity': '0'
        }, 200);
    });
}

// 工具提示增强
function initTooltipEnhancements() {
    // 为带有data-tooltip属性的元素添加工具提示类
    $('[data-tooltip]').addClass('tooltip-custom');

    // 动态工具提示
    $('.tooltip-custom').on('mouseenter', function() {
        const $this = $(this);
        const tooltip = $this.attr('data-tooltip');

        if (tooltip) {
            $this.attr('data-original-tooltip', tooltip);
        }
    });
}

// 卡片悬停效果增强
function initCardEffects() {
    $('.card, .stat-card, .action-card').on('mouseenter', function() {
        $(this).addClass('card-hover');
    }).on('mouseleave', function() {
        $(this).removeClass('card-hover');
    });
}

// 平滑滚动
function initSmoothScroll() {
    $('a[href^="#"]').on('click', function(e) {
        e.preventDefault();
        const target = $($(this).attr('href'));

        if (target.length) {
            $('html, body').animate({
                scrollTop: target.offset().top - 100
            }, 500);
        }
    });
}

// 懒加载图片
function initLazyLoading() {
    const imageObserver = new IntersectionObserver((entries, observer) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                const img = entry.target;
                img.src = img.dataset.src;
                img.classList.remove('lazy');
                observer.unobserve(img);
            }
        });
    });

    document.querySelectorAll('img[data-src]').forEach(img => {
        img.classList.add('lazy');
        imageObserver.observe(img);
    });
}

// 初始化所有增强功能
function initUIEnhancements() {
    initDataTable();
    initSearchBox();
    initStatusBadges();
    initActionButtons();
    initModalEnhancements();
    initTooltipEnhancements();
    initCardEffects();
    initSmoothScroll();
    initLazyLoading();
}

// 在文档就绪时初始化增强功能
$(document).ready(function() {
    initUIEnhancements();
});

// 导出为全局函数
window.showConfirm = showConfirm;
window.showSuccess = showSuccess;
window.showError = showError;
window.showWarning = showWarning;
window.showInfo = showInfo;
window.formatDate = formatDate;
window.copyToClipboard = copyToClipboard;
window.apiRequest = apiRequest;
window.animateProgressBar = animateProgressBar;
window.initUIEnhancements = initUIEnhancements;
