# YBA监控管理系统使用指南

## 快速开始

### 1. 环境配置

```bash
# 1. 检查Python版本（需要3.7+）
python --version

# 2. 安装依赖
pip install -r requirements.txt

# 3. 配置环境变量
python check_env.py  # 这会自动创建.env.example模板

# 4. 复制并编辑环境变量文件
cp .env.example .env
# 编辑.env文件，设置数据库连接信息
```

### 2. 环境变量配置

编辑 `.env` 文件，设置以下必需参数：

```env
# 数据库配置（必需）
MYSQL_HOST=localhost
MYSQL_PORT=3306
MYSQL_USER=your_username
MYSQL_PASSWORD=your_password
MYSQL_DB=your_database

# 安全配置（必需）
SECRET_KEY=your-secret-key-here
ADMIN_TOKEN=your-admin-token

# 应用配置（可选）
FLASK_HOST=0.0.0.0
FLASK_PORT=5000
FLASK_DEBUG=false
FLASK_ENV=production
```

### 3. 验证配置

```bash
# 检查环境变量配置
python check_env.py

# 运行系统测试
python test_fixes.py
```

### 4. 启动系统

```bash
# 推荐：使用快速启动脚本
python quick_start.py

# 或者直接启动
python start.py
```

## 功能使用

### 登录系统

1. **管理员登录**
   - 使用 `.env` 文件中设置的 `ADMIN_TOKEN`
   - 拥有所有功能权限

2. **代理商登录**
   - 使用代理商专用Token
   - 权限受限，只能管理自己的数据

### 微信登录管理

1. **查看登录状态**
   - 进入"微信登录管理"页面
   - 查看当前微信登录状态和用户信息

2. **扫码登录**
   - 点击"获取二维码"
   - 使用微信扫描二维码
   - 在手机上确认登录

3. **退出登录**
   - 点击"退出登录"按钮
   - 确认退出微信登录

### VIP会员管理

1. **查看VIP卡统计**
   - 总卡数、已绑定卡数、积分统计
   - 今日新增卡片数量

2. **创建VIP卡**
   - 单张创建或批量创建
   - 可选择创建时直接绑定用户

3. **管理VIP卡**
   - 查看VIP卡列表
   - 绑定/解绑用户
   - 更新积分
   - 设置有效期

### 用户管理（管理员功能）

1. **代理商管理**
   - 创建新代理商账户
   - 编辑代理商信息
   - 激活/停用功能模块

2. **权限控制**
   - VIP功能开关
   - 微信功能开关
   - Token重置

## 环境变量详解

### 自动加载机制

系统会自动按以下顺序加载环境变量：

1. 系统环境变量（优先级最高）
2. `.env` 文件中的配置
3. 代码中的默认值

### 类型转换

环境变量支持自动类型转换：

```python
# 字符串（默认）
MYSQL_HOST=localhost

# 整数
MYSQL_PORT=3306

# 布尔值（true/false, 1/0, yes/no, on/off）
FLASK_DEBUG=true

# 列表（逗号分隔）
ALLOWED_HOSTS=localhost,127.0.0.1,example.com
```

### 验证功能

使用内置验证工具检查配置：

```bash
# 完整的环境检查
python check_env.py

# 输出示例：
# ✓ .env文件存在
# ✓ 所有必需的环境变量都已设置
# ✓ 数据库配置正常
# ✓ Flask配置正常
# ✓ 管理员配置正常
# ✓ 数据库连接成功
```

## 故障排除

### 常见错误及解决方案

1. **环境变量未设置**
   ```bash
   # 错误：ValueError: 必需的环境变量 SECRET_KEY 未设置
   # 解决：检查.env文件是否存在并包含所需变量
   python check_env.py
   ```

2. **数据库连接失败**
   ```bash
   # 错误：pymysql.err.OperationalError
   # 解决：检查数据库配置和服务状态
   python check_env.py  # 查看数据库配置
   ```

3. **Token认证失败**
   ```bash
   # 错误：登录失败
   # 解决：检查Token是否正确设置
   # 管理员Token在.env文件的ADMIN_TOKEN中
   ```

4. **端口被占用**
   ```bash
   # 错误：Address already in use
   # 解决：更改端口或停止占用进程
   # 在.env中设置：FLASK_PORT=5001
   ```

### 日志查看

```bash
# 查看应用日志
tail -f app.log

# 查看启动日志
python start.py  # 日志会输出到控制台
```

### 重置配置

```bash
# 重新创建环境变量模板
python -c "from app.utils.env_loader import EnvLoader; EnvLoader.create_env_example()"

# 删除现有.env文件重新配置
rm .env
python check_env.py  # 会提示创建新的配置
```

## 开发模式

### 启用调试模式

在 `.env` 文件中设置：

```env
FLASK_DEBUG=true
FLASK_ENV=development
```

### 查看详细日志

```python
# 在代码中启用详细日志
import logging
logging.basicConfig(level=logging.DEBUG)
```

## 生产部署

### 安全配置

1. **设置强密码**
   ```env
   SECRET_KEY=your-very-long-and-random-secret-key
   ADMIN_TOKEN=your-secure-admin-token-at-least-32-chars
   ```

2. **关闭调试模式**
   ```env
   FLASK_DEBUG=false
   FLASK_ENV=production
   ```

3. **数据库安全**
   - 使用专用数据库用户
   - 限制数据库访问权限
   - 定期备份数据

### 性能优化

1. **使用生产服务器**
   ```bash
   # 安装gunicorn
   pip install gunicorn
   
   # 启动生产服务器
   gunicorn -w 4 -b 0.0.0.0:5000 "app:create_app()"
   ```

2. **配置反向代理**
   - 使用Nginx作为反向代理
   - 配置SSL证书
   - 启用gzip压缩

## 联系支持

如遇到问题，请提供以下信息：

1. 运行 `python check_env.py` 的输出
2. 运行 `python test_fixes.py` 的输出
3. 应用日志文件 `app.log` 的相关内容
4. 错误的详细描述和重现步骤
