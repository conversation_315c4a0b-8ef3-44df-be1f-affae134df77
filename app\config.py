"""
YBA监控管理系统配置文件
整合两个系统的配置信息
"""
import os
from app.utils.env_loader import EnvLoader

# 加载环境变量
EnvLoader.load_env_file()


class Config:
    """统一配置类"""
    
    # Flask基础配置
    SECRET_KEY = os.environ.get('SECRET_KEY') or 'unified-admin-system-secret-key'
    
    # 数据库配置（使用现有数据库）
    MYSQL_HOST = os.environ.get('MYSQL_HOST') or 'rm-uf6psdjix97ed81r8oo.mysql.rds.aliyuncs.com'
    MYSQL_PORT = int(os.environ.get('MYSQL_PORT') or 3306)
    MYSQL_USER = os.environ.get('MYSQL_USER') or 'bot'
    MYSQL_PASSWORD = os.environ.get('MYSQL_PASSWORD') or 'yaoboan19990312!'
    MYSQL_DB = os.environ.get('MYSQL_DB') or 'yba_ppmt'
    
    # 管理员配置
    ADMIN_TOKEN = os.environ.get('ADMIN_TOKEN') or 'YBA990312'
    
    # 微信API配置
    WECHAT_API_TIMEOUT = 30
    
    # VIP系统配置
    VIP_CARD_PREFIX_LENGTH = 3
    VIP_CARD_NUMBER_LENGTH = 18
    
    @classmethod
    def get_db_url(cls):
        """获取数据库连接URL"""
        return f"mysql+pymysql://{cls.MYSQL_USER}:{cls.MYSQL_PASSWORD}@{cls.MYSQL_HOST}:{cls.MYSQL_PORT}/{cls.MYSQL_DB}"
    
    @classmethod
    def get_db_config(cls):
        """获取数据库配置字典"""
        return {
            'host': cls.MYSQL_HOST,
            'port': cls.MYSQL_PORT,
            'user': cls.MYSQL_USER,
            'password': cls.MYSQL_PASSWORD,
            'database': cls.MYSQL_DB,
            'charset': 'utf8mb4',
            'autocommit': True
        }
