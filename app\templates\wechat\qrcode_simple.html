{% extends "base.html" %}

{% block title %}微信扫码登录{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row justify-content-center">
        <div class="col-md-6 col-lg-4">
            <!-- 简洁的扫码卡片 -->
            <div class="qrcode-card-simple">
                <!-- 标题区域 -->
                <div class="card-header-simple">
                    <h4 class="title">
                        <i class="bi bi-qr-code me-2"></i>
                        微信扫码登录
                    </h4>
                    <p class="subtitle">请使用微信扫描下方二维码</p>
                </div>

                <!-- 二维码显示区 -->
                <div class="qrcode-display-simple">
                    {% if qrcode_data %}
                    <div class="qrcode-wrapper-simple">
                        <img src="{{ qrcode_data }}" alt="微信登录二维码" class="qrcode-image-simple" id="qrcodeImage">
                        <div class="qrcode-overlay-simple" id="qrcodeOverlay" style="display: none;">
                            <div class="overlay-content-simple">
                                <i class="bi bi-clock-history"></i>
                                <p>二维码已过期</p>
                                <button class="btn btn-primary" onclick="refreshQrcode()">
                                    <i class="bi bi-arrow-clockwise me-1"></i>刷新二维码
                                </button>
                            </div>
                        </div>
                    </div>
                    {% else %}
                    <div class="qrcode-error-simple">
                        <i class="bi bi-exclamation-triangle"></i>
                        <p>{{ error_message or '获取二维码失败' }}</p>
                        <button class="btn btn-primary" onclick="refreshQrcode()">
                            <i class="bi bi-arrow-clockwise me-2"></i>重新获取
                        </button>
                    </div>
                    {% endif %}
                </div>



                <!-- 操作按钮 -->
                <div class="actions-simple">
                    <button class="btn btn-outline-primary btn-sm" onclick="refreshQrcode()">
                        <i class="bi bi-arrow-clockwise me-1"></i>刷新
                    </button>
                    <a href="{{ url_for('wechat.index') }}" class="btn btn-outline-secondary btn-sm">
                        <i class="bi bi-arrow-left me-1"></i>返回
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
/* 简洁的扫码界面样式 */
.qrcode-card-simple {
    background: #ffffff;
    border-radius: 16px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
    padding: 2rem;
    text-align: center;
    max-width: 400px;
    margin: 2rem auto;
}

.card-header-simple {
    margin-bottom: 2rem;
}

.card-header-simple .title {
    color: #2c3e50;
    font-weight: 600;
    margin-bottom: 0.5rem;
}

.card-header-simple .subtitle {
    color: #7f8c8d;
    font-size: 0.9rem;
    margin: 0;
}

/* 二维码显示 */
.qrcode-display-simple {
    margin-bottom: 2rem;
    position: relative;
}

.qrcode-wrapper-simple {
    position: relative;
    display: inline-block;
}

.qrcode-image-simple {
    width: 200px;
    height: 200px;
    border-radius: 12px;
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
    transition: transform 0.3s ease;
}

.qrcode-image-simple:hover {
    transform: scale(1.05);
}

.qrcode-overlay-simple {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(255, 255, 255, 0.95);
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.overlay-content-simple {
    text-align: center;
}

.overlay-content-simple i {
    font-size: 2rem;
    color: #e74c3c;
    margin-bottom: 1rem;
}

.overlay-content-simple p {
    margin-bottom: 1rem;
    color: #2c3e50;
}

.qrcode-error-simple {
    padding: 2rem;
    color: #7f8c8d;
}

.qrcode-error-simple i {
    font-size: 3rem;
    color: #f39c12;
    margin-bottom: 1rem;
}



/* 操作按钮 */
.actions-simple {
    display: flex;
    gap: 1rem;
    justify-content: center;
}

.actions-simple .btn {
    border-radius: 8px;
    font-weight: 500;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .qrcode-card-simple {
        margin: 1rem;
        padding: 1.5rem;
    }
    
    .qrcode-image-simple {
        width: 160px;
        height: 160px;
    }
    
    .time-value-large {
        font-size: 2.5rem;
    }
}
</style>

<script>
// 刷新二维码
function refreshQrcode() {
    window.location.reload();
}




</script>
{% endblock %}
