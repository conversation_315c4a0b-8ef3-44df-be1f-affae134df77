{% extends "base.html" %}

{% block title %}V3 API登录管理 - YBA监控管理系统{% endblock %}

{% block page_title %}
V3 API登录管理
<span class="badge bg-warning ms-2">GeWeChat API</span>
{% endblock %}

{% block content %}
<div class="wechat-container">
    <div class="row g-4">
        <!-- V3 API功能卡片 -->
        <div class="col-xl-8">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="bi bi-gear me-2"></i>V3 API管理
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row g-3">
                        <!-- 获取Token -->
                        <div class="col-md-6">
                            <div class="d-grid">
                                <button type="button" class="btn btn-primary" onclick="getV3Token()">
                                    <i class="bi bi-key me-2"></i>获取Token
                                </button>
                            </div>
                        </div>
                        
                        <!-- 设置回调地址 -->
                        <div class="col-md-6">
                            <div class="input-group">
                                <input type="text" class="form-control" id="callbackUrl" 
                                       placeholder="回调地址" value="http://your-server.com/callback">
                                <button class="btn btn-outline-secondary" type="button" onclick="setV3Callback()">
                                    <i class="bi bi-link me-1"></i>设置回调
                                </button>
                            </div>
                        </div>
                    </div>
                    
                    <!-- 结果显示区域 -->
                    <div id="v3-result" class="mt-3" style="display: none;">
                        <div class="alert alert-info">
                            <h6>操作结果：</h6>
                            <pre id="v3-result-content" class="mb-0"></pre>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- 配置信息卡片 -->
        <div class="col-xl-4">
            <div class="card">
                <div class="card-header">
                    <h6 class="mb-0">
                        <i class="bi bi-info-circle me-2"></i>配置信息
                    </h6>
                </div>
                <div class="card-body">
                    <div class="config-info">
                        <div class="config-item">
                            <strong>用户ID:</strong> {{ debug_info.user_id }}
                        </div>
                        <div class="config-item">
                            <strong>用户名:</strong> {{ debug_info.name }}
                        </div>
                        <div class="config-item">
                            <strong>API版本:</strong> 
                            <span class="badge bg-warning">{{ debug_info.api_version }}</span>
                        </div>
                        <div class="config-item">
                            <strong>基础URL:</strong> {{ debug_info.base_url }}
                        </div>
                        <div class="config-item">
                            <strong>Token:</strong> 
                            <span class="badge {% if debug_info.token == 'Yes' %}bg-success{% else %}bg-danger{% endif %}">
                                {{ debug_info.token }}
                            </span>
                        </div>
                        <div class="config-item">
                            <strong>App ID:</strong> {{ debug_info.app_id or '未设置' }}
                        </div>
                        <div class="config-item">
                            <strong>代理:</strong> {{ debug_info.proxy or '无' }}
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- 返回按钮 -->
    <div class="row mt-4">
        <div class="col-12">
            <a href="{{ url_for('wechat.index') }}" class="btn btn-secondary">
                <i class="bi bi-arrow-left me-2"></i>返回主页
            </a>
        </div>
    </div>
</div>

<style>
.config-info .config-item {
    margin-bottom: 0.5rem;
    padding: 0.25rem 0;
    border-bottom: 1px solid #eee;
}

.config-info .config-item:last-child {
    border-bottom: none;
}

#v3-result-content {
    font-size: 0.875rem;
    max-height: 300px;
    overflow-y: auto;
}
</style>

<script>
function getV3Token() {
    const btn = event.target;
    const originalText = btn.innerHTML;
    
    // 显示加载状态
    btn.innerHTML = '<i class="spinner-border spinner-border-sm me-2"></i>获取中...';
    btn.disabled = true;
    
    fetch('{{ url_for("wechat.v3_get_token") }}')
        .then(response => response.json())
        .then(data => {
            showV3Result(data);
        })
        .catch(error => {
            showV3Result({
                ret: 500,
                msg: '请求失败: ' + error.message,
                data: null
            });
        })
        .finally(() => {
            // 恢复按钮状态
            btn.innerHTML = originalText;
            btn.disabled = false;
        });
}

function setV3Callback() {
    const callbackUrl = document.getElementById('callbackUrl').value;
    const btn = event.target;
    const originalText = btn.innerHTML;
    
    if (!callbackUrl.trim()) {
        alert('请输入回调地址');
        return;
    }
    
    // 显示加载状态
    btn.innerHTML = '<i class="spinner-border spinner-border-sm me-1"></i>设置中...';
    btn.disabled = true;
    
    const url = '{{ url_for("wechat.v3_set_callback") }}?callback_url=' + encodeURIComponent(callbackUrl);
    
    fetch(url)
        .then(response => response.json())
        .then(data => {
            showV3Result(data);
        })
        .catch(error => {
            showV3Result({
                ret: 500,
                msg: '请求失败: ' + error.message,
                data: null
            });
        })
        .finally(() => {
            // 恢复按钮状态
            btn.innerHTML = originalText;
            btn.disabled = false;
        });
}

function showV3Result(data) {
    const resultDiv = document.getElementById('v3-result');
    const contentPre = document.getElementById('v3-result-content');
    
    contentPre.textContent = JSON.stringify(data, null, 2);
    resultDiv.style.display = 'block';
    
    // 根据结果设置样式
    const alertDiv = resultDiv.querySelector('.alert');
    alertDiv.className = 'alert ' + (data.ret === 200 ? 'alert-success' : 'alert-danger');
}
</script>
{% endblock %}
