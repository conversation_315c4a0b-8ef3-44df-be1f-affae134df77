{% extends "base.html" %}

{% block title %}创建VIP卡 - 统一Web管理系统{% endblock %}

{% block page_title %}创建VIP卡{% endblock %}

{% block content %}
<div class="create-card-container">
    <div class="row justify-content-center">
        <div class="col-lg-8">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="bi bi-plus-square me-2"></i>创建VIP会员卡
                    </h5>
                </div>
                
                <div class="card-body">
                    <form method="POST" class="needs-validation" novalidate>
                        <div class="row g-3">
                            <div class="col-md-6">
                                <label for="count" class="form-label">创建数量</label>
                                <input type="number" class="form-control" id="count" name="count" 
                                       value="1" min="1" max="100" required>
                                <div class="form-text">单次最多创建100张VIP卡</div>
                                <div class="invalid-feedback">请输入有效的创建数量（1-100）</div>
                            </div>
                            
                            <div class="col-md-6">
                                <label for="user_id" class="form-label">绑定用户ID（可选）</label>
                                <input type="text" class="form-control" id="user_id" name="user_id" 
                                       placeholder="留空则创建未绑定的VIP卡">
                                <div class="form-text">创建时直接绑定到指定用户</div>
                            </div>
                        </div>
                        
                        <div class="mt-4">
                            <h6>创建说明</h6>
                            <ul class="text-muted">
                                <li>VIP卡号格式：代理前缀-18位随机字符</li>
                                <li>创建后的VIP卡默认为激活状态</li>
                                <li>可以在创建时直接绑定用户，也可以后续绑定</li>
                                <li>批量创建时，所有VIP卡都将是未绑定状态</li>
                            </ul>
                        </div>
                        
                        <div class="d-flex gap-3 mt-4">
                            <button type="submit" class="btn btn-primary">
                                <i class="bi bi-plus-circle me-2"></i>创建VIP卡
                            </button>
                            <a href="{{ url_for('vip.index') }}" class="btn btn-outline-secondary">
                                <i class="bi bi-arrow-left me-2"></i>返回
                            </a>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.create-card-container {
    animation: fadeInUp 0.6s ease-out;
}

.form-text {
    font-size: 0.875rem;
    color: var(--text-secondary);
}

.needs-validation .form-control:invalid {
    border-color: var(--danger-color);
}

.needs-validation .form-control:valid {
    border-color: var(--success-color);
}
</style>

<script>
// 表单验证
(function() {
    'use strict';
    
    const form = document.querySelector('.needs-validation');
    
    form.addEventListener('submit', function(event) {
        if (!form.checkValidity()) {
            event.preventDefault();
            event.stopPropagation();
        }
        
        form.classList.add('was-validated');
    }, false);
    
    // 数量输入验证
    const countInput = document.getElementById('count');
    countInput.addEventListener('input', function() {
        const value = parseInt(this.value);
        if (value < 1 || value > 100) {
            this.setCustomValidity('创建数量必须在1-100之间');
        } else {
            this.setCustomValidity('');
        }
    });
})();
</script>
{% endblock %}
