"""
V3 API客户端
基于gewechat_client实现，提供与V1、V2客户端统一的接口
"""
import logging
from typing import Dict, Any, Optional
from .gewechat_client import GewechatClient

logger = logging.getLogger(__name__)


class WeChatClientV3:
    """V3 API客户端，基于gewechat_client"""
    
    def __init__(self, base_url: str, token: str = None, app_id: str = None):
        """
        初始化V3客户端
        
        Args:
            base_url: API基础URL
            token: API令牌
            app_id: 应用ID
        """
        self.base_url = base_url
        self.token = token
        self.app_id = app_id
        
        # 创建gewechat客户端
        self.client = GewechatClient(base_url, token)
        
        logger.info(f"初始化V3客户端: {base_url}")
    
    def set_app_id(self, app_id: str):
        """设置应用ID"""
        self.app_id = app_id
        logger.info(f"设置app_id: {app_id}")
    
    def get_login_status(self) -> Dict[str, Any]:
        """获取登录状态（统一接口）"""
        try:
            if not self.app_id:
                return {
                    'Code': 500,
                    'Text': '未设置app_id',
                    'Data': {
                        'loginState': 0,
                        'status': 'error',
                        'message': '未设置app_id'
                    }
                }
            
            # 使用check_online检查登录状态
            result = self.client.check_online(self.app_id)
            
            if result.get('ret') == 200:
                is_online = result.get('data', False)
                
                if is_online:
                    # 获取用户信息
                    user_info = self.get_user_info()
                    
                    return {
                        'Code': 200,
                        'Text': '在线',
                        'Data': {
                            'loginState': 1,
                            'status': 'online',
                            'message': '账号在线',
                            'user_info': user_info.get('Data') if user_info.get('Code') == 200 else None
                        }
                    }
                else:
                    return {
                        'Code': 200,
                        'Text': '离线',
                        'Data': {
                            'loginState': 0,
                            'status': 'offline',
                            'message': '账号离线'
                        }
                    }
            else:
                return {
                    'Code': result.get('ret', 500),
                    'Text': result.get('msg', '检查登录状态失败'),
                    'Data': {
                        'loginState': 0,
                        'status': 'error',
                        'message': result.get('msg', '检查登录状态失败')
                    }
                }
                
        except Exception as e:
            logger.error(f"获取登录状态失败: {str(e)}")
            return {
                'Code': 500,
                'Text': f'获取登录状态异常: {str(e)}',
                'Data': {
                    'loginState': 0,
                    'status': 'error',
                    'message': str(e)
                }
            }
    
    def get_user_info(self) -> Dict[str, Any]:
        """获取用户信息（统一接口）"""
        try:
            if not self.app_id:
                return {
                    'Code': 500,
                    'Text': '未设置app_id',
                    'Data': None
                }
            
            result = self.client.get_profile(self.app_id)
            
            if result.get('ret') == 200:
                profile_data = result.get('data', {})
                
                # 标准化用户信息格式
                standardized_data = {
                    'wxid': profile_data.get('wxid', ''),
                    'nick_name': profile_data.get('nickName', ''),
                    'head_img': profile_data.get('headImgUrl', ''),
                    'mobile': profile_data.get('mobile', ''),
                    'signature': profile_data.get('signature', ''),
                    'sex': profile_data.get('sex', 0),
                    'country': profile_data.get('country', ''),
                    'province': profile_data.get('province', ''),
                    'city': profile_data.get('city', '')
                }
                
                return {
                    'Code': 200,
                    'Text': '获取用户信息成功',
                    'Data': standardized_data
                }
            else:
                return {
                    'Code': result.get('ret', 500),
                    'Text': result.get('msg', '获取用户信息失败'),
                    'Data': None
                }
                
        except Exception as e:
            logger.error(f"获取用户信息失败: {str(e)}")
            return {
                'Code': 500,
                'Text': f'获取用户信息异常: {str(e)}',
                'Data': None
            }
    
    def logout(self) -> Dict[str, Any]:
        """退出登录（统一接口）"""
        try:
            if not self.app_id:
                return {
                    'Code': 500,
                    'Text': '未设置app_id',
                    'Data': None
                }
            
            result = self.client.logout(self.app_id)
            
            if result.get('ret') == 200:
                return {
                    'Code': 200,
                    'Text': '退出登录成功',
                    'Data': result.get('data')
                }
            else:
                return {
                    'Code': result.get('ret', 500),
                    'Text': result.get('msg', '退出登录失败'),
                    'Data': None
                }
                
        except Exception as e:
            logger.error(f"退出登录失败: {str(e)}")
            return {
                'Code': 500,
                'Text': f'退出登录异常: {str(e)}',
                'Data': None
            }
    
    def get_qr_code(self, **kwargs) -> Dict[str, Any]:
        """获取二维码（统一接口）"""
        try:
            # V3 API的二维码获取
            result = self.client.get_qr(self.app_id or "")
            
            if result.get('ret') == 200:
                qr_data = result.get('data', {})
                
                # 更新app_id（如果返回了新的）
                if qr_data.get('appId') and not self.app_id:
                    self.app_id = qr_data.get('appId')
                    logger.info(f"自动设置app_id: {self.app_id}")
                
                # 标准化二维码数据格式
                standardized_data = {
                    'qrcode': qr_data.get('qrImgBase64', ''),
                    'QrBase64': qr_data.get('qrImgBase64', ''),
                    'qrCodeBase64': qr_data.get('qrImgBase64', ''),
                    'uuid': qr_data.get('uuid', ''),
                    'Uuid': qr_data.get('uuid', ''),
                    'UUID': qr_data.get('uuid', ''),
                    'expiredTime': qr_data.get('expiredTime', 120),
                    'expired_time': qr_data.get('expiredTime', 120),
                    'appId': qr_data.get('appId', self.app_id),
                    'app_id': qr_data.get('appId', self.app_id)
                }
                
                return {
                    'Code': 200,
                    'Text': '获取二维码成功',
                    'Data': standardized_data
                }
            else:
                return {
                    'Code': result.get('ret', 500),
                    'Text': result.get('msg', '获取二维码失败'),
                    'Data': None
                }
                
        except Exception as e:
            logger.error(f"获取二维码失败: {str(e)}")
            return {
                'Code': 500,
                'Text': f'获取二维码异常: {str(e)}',
                'Data': None
            }
    
    def check_qr_status(self, uuid: str, captcha: str = "") -> Dict[str, Any]:
        """检查二维码状态（统一接口）"""
        try:
            if not self.app_id:
                return {
                    'Code': 500,
                    'Text': '未设置app_id',
                    'Data': None
                }
            
            result = self.client.check_qr(self.app_id, uuid, captcha)
            
            if result.get('ret') == 200:
                qr_data = result.get('data', {})
                status = qr_data.get('status', 0)
                
                # 标准化状态数据格式
                standardized_data = {
                    'status': status,
                    'state': status,
                    'uuid': uuid,
                    'expiredTime': qr_data.get('expiredTime', 0),
                    'expired_time': qr_data.get('expiredTime', 0),
                    'nickName': qr_data.get('nickName', ''),
                    'nick_name': qr_data.get('nickName', ''),
                    'message': qr_data.get('msg', '')
                }
                
                # 根据状态设置消息
                if status == 2:  # 登录成功
                    standardized_data['loginState'] = 1
                    standardized_data['message'] = '登录成功'
                elif status == 1:  # 已扫码，等待确认
                    standardized_data['message'] = '已扫码，等待确认'
                elif status == 0:  # 等待扫码
                    standardized_data['message'] = '等待扫码'
                else:
                    standardized_data['message'] = qr_data.get('msg', '未知状态')
                
                return {
                    'Code': 200,
                    'Text': standardized_data['message'],
                    'Data': standardized_data
                }
            else:
                return {
                    'Code': result.get('ret', 500),
                    'Text': result.get('msg', '检查二维码状态失败'),
                    'Data': None
                }
                
        except Exception as e:
            logger.error(f"检查二维码状态失败: {str(e)}")
            return {
                'Code': 500,
                'Text': f'检查二维码状态异常: {str(e)}',
                'Data': None
            }
    
    # 获取token的方法
    def get_token(self) -> Dict[str, Any]:
        """获取token"""
        try:
            result = self.client.get_token()
            return result
        except Exception as e:
            logger.error(f"获取token失败: {str(e)}")
            return {
                'ret': 500,
                'msg': f'获取token异常: {str(e)}',
                'data': None
            }
    
    # 设置回调地址的方法
    def set_callback(self, callback_url: str) -> Dict[str, Any]:
        """设置回调地址"""
        try:
            if not self.token:
                return {
                    'ret': 500,
                    'msg': '未设置token',
                    'data': None
                }
            
            result = self.client.set_callback(self.token, callback_url)
            return result
        except Exception as e:
            logger.error(f"设置回调地址失败: {str(e)}")
            return {
                'ret': 500,
                'msg': f'设置回调地址异常: {str(e)}',
                'data': None
            }
