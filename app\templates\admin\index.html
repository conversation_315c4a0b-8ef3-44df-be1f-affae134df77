{% extends "base.html" %}

{% block title %}用户管理 - YBA监控管理系统{% endblock %}

{% block page_title %}用户管理{% endblock %}

{% block content %}
<div class="admin-container">
    <!-- 统计卡片 -->
    <div class="stats-section mb-5">
        <div class="row g-4">
            <div class="col-xl-3 col-md-6">
                <div class="stat-card">
                    <div class="stat-icon bg-primary">
                        <i class="bi bi-people"></i>
                    </div>
                    <div class="stat-content">
                        <h3 class="stat-number">{{ stats.total_agents or 0 }}</h3>
                        <p class="stat-label">代理商总数</p>
                    </div>
                </div>
            </div>
            
            <div class="col-xl-3 col-md-6">
                <div class="stat-card">
                    <div class="stat-icon bg-success">
                        <i class="bi bi-check-circle"></i>
                    </div>
                    <div class="stat-content">
                        <h3 class="stat-number">{{ stats.active_agents or 0 }}</h3>
                        <p class="stat-label">激活代理商</p>
                    </div>
                </div>
            </div>
            
            <div class="col-xl-3 col-md-6">
                <div class="stat-card">
                    <div class="stat-icon bg-warning">
                        <i class="bi bi-credit-card-2-front"></i>
                    </div>
                    <div class="stat-content">
                        <h3 class="stat-number">{{ stats.vip_active_agents or 0 }}</h3>
                        <p class="stat-label">VIP功能激活</p>
                    </div>
                </div>
            </div>
            
            <div class="col-xl-3 col-md-6">
                <div class="stat-card">
                    <div class="stat-icon bg-info">
                        <i class="bi bi-wechat"></i>
                    </div>
                    <div class="stat-content">
                        <h3 class="stat-number">{{ stats.wechat_active_agents or 0 }}</h3>
                        <p class="stat-label">微信功能激活</p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 管理功能 -->
    <div class="row g-4">
        <!-- 主要操作 -->
        <div class="col-xl-8">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="bi bi-gear me-2"></i>管理功能
                    </h5>
                </div>
                <div class="card-body">
                    <div class="admin-actions-grid">
                        <a href="{{ url_for('admin.create_agent') }}" class="action-card">
                            <div class="action-icon bg-primary">
                                <i class="bi bi-person-plus"></i>
                            </div>
                            <div class="action-content">
                                <h6>创建代理商</h6>
                                <p>添加新的代理商账户</p>
                            </div>
                        </a>
                        
                        <a href="{{ url_for('admin.agents') }}" class="action-card">
                            <div class="action-icon bg-success">
                                <i class="bi bi-list-ul"></i>
                            </div>
                            <div class="action-content">
                                <h6>代理商列表</h6>
                                <p>查看所有代理商</p>
                            </div>
                        </a>
                        
                        <a href="{{ url_for('admin.agents', status='vip_active') }}" class="action-card">
                            <div class="action-icon bg-warning">
                                <i class="bi bi-credit-card-2-front"></i>
                            </div>
                            <div class="action-content">
                                <h6>VIP代理商</h6>
                                <p>VIP功能激活的代理商</p>
                            </div>
                        </a>
                        
                        <a href="{{ url_for('admin.agents', status='wechat_active') }}" class="action-card">
                            <div class="action-icon bg-info">
                                <i class="bi bi-wechat"></i>
                            </div>
                            <div class="action-content">
                                <h6>微信代理商</h6>
                                <p>微信功能激活的代理商</p>
                            </div>
                        </a>
                        
                        <a href="{{ url_for('vip.index') }}" class="action-card">
                            <div class="action-icon bg-purple">
                                <i class="bi bi-gem"></i>
                            </div>
                            <div class="action-content">
                                <h6>VIP管理</h6>
                                <p>管理VIP会员卡</p>
                            </div>
                        </a>
                        
                        <a href="{{ url_for('wechat.index') }}" class="action-card">
                            <div class="action-icon bg-success">
                                <i class="bi bi-chat-dots"></i>
                            </div>
                            <div class="action-content">
                                <h6>微信管理</h6>
                                <p>管理微信登录</p>
                            </div>
                        </a>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- 侧边信息 -->
        <div class="col-xl-4">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="bi bi-info-circle me-2"></i>系统状态
                    </h5>
                </div>
                <div class="card-body">
                    <div class="system-status">
                        <div class="status-item">
                            <div class="status-indicator bg-success"></div>
                            <div class="status-content">
                                <h6>系统运行正常</h6>
                                <p>所有功能模块正常运行</p>
                            </div>
                        </div>
                        
                        <div class="status-item">
                            <div class="status-indicator bg-primary"></div>
                            <div class="status-content">
                                <h6>数据库连接正常</h6>
                                <p>数据库响应正常</p>
                            </div>
                        </div>
                        
                        <div class="status-item">
                            <div class="status-indicator bg-warning"></div>
                            <div class="status-content">
                                <h6>今日新增 {{ stats.today_agents or 0 }} 个代理商</h6>
                                <p>系统活跃度良好</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- 快捷统计 -->
            <div class="card mt-4">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="bi bi-bar-chart me-2"></i>快捷统计
                    </h5>
                </div>
                <div class="card-body">
                    <div class="quick-stats">
                        <div class="stat-row">
                            <span class="label">激活率</span>
                            <span class="value">
                                {% if stats.total_agents > 0 %}
                                {{ "%.1f"|format((stats.active_agents / stats.total_agents * 100)) }}%
                                {% else %}
                                0%
                                {% endif %}
                            </span>
                        </div>
                        
                        <div class="stat-row">
                            <span class="label">VIP功能使用率</span>
                            <span class="value">
                                {% if stats.total_agents > 0 %}
                                {{ "%.1f"|format((stats.vip_active_agents / stats.total_agents * 100)) }}%
                                {% else %}
                                0%
                                {% endif %}
                            </span>
                        </div>
                        
                        <div class="stat-row">
                            <span class="label">微信功能使用率</span>
                            <span class="value">
                                {% if stats.total_agents > 0 %}
                                {{ "%.1f"|format((stats.wechat_active_agents / stats.total_agents * 100)) }}%
                                {% else %}
                                0%
                                {% endif %}
                            </span>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- 管理员工具 -->
            <div class="card mt-4">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="bi bi-tools me-2"></i>管理员工具
                    </h5>
                </div>
                <div class="card-body">
                    <div class="admin-tools">
                        <button class="btn btn-outline-primary btn-sm w-100 mb-2" onclick="refreshStats()">
                            <i class="bi bi-arrow-clockwise me-2"></i>刷新统计
                        </button>
                        
                        <button class="btn btn-outline-info btn-sm w-100 mb-2" onclick="exportData()">
                            <i class="bi bi-download me-2"></i>导出数据
                        </button>
                        
                        <button class="btn btn-outline-warning btn-sm w-100" onclick="showSystemInfo()">
                            <i class="bi bi-info-circle me-2"></i>系统信息
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.admin-container {
    animation: fadeInUp 0.6s ease-out;
}

.stat-card {
    background: var(--bg-secondary);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    padding: 1.5rem;
    display: flex;
    align-items: center;
    gap: 1rem;
    transition: var(--transition);
    box-shadow: var(--shadow-sm);
}

.stat-card:hover {
    transform: translateY(-4px);
    box-shadow: var(--shadow-lg);
}

.stat-icon {
    width: 60px;
    height: 60px;
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    color: white;
    flex-shrink: 0;
}

.stat-number {
    font-size: 2rem;
    font-weight: 700;
    margin: 0;
    color: var(--text-primary);
}

.stat-label {
    margin: 0;
    color: var(--text-secondary);
    font-size: 0.9rem;
}

.admin-actions-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1rem;
}

.action-card {
    display: flex;
    align-items: center;
    gap: 1rem;
    padding: 1.5rem;
    background: var(--bg-tertiary);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    text-decoration: none;
    color: inherit;
    transition: var(--transition);
}

.action-card:hover {
    background: rgba(99, 102, 241, 0.1);
    border-color: var(--primary-color);
    color: inherit;
    text-decoration: none;
    transform: translateY(-2px);
    box-shadow: var(--shadow-md);
}

.action-icon {
    width: 50px;
    height: 50px;
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.25rem;
    color: white;
    flex-shrink: 0;
}

.bg-purple {
    background: linear-gradient(135deg, #8b5cf6, #a855f7);
}

.action-content h6 {
    margin: 0 0 0.25rem;
    font-weight: 600;
    color: var(--text-primary);
}

.action-content p {
    margin: 0;
    font-size: 0.85rem;
    color: var(--text-secondary);
}

.system-status {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.status-item {
    display: flex;
    align-items: flex-start;
    gap: 1rem;
}

.status-indicator {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    margin-top: 0.25rem;
    flex-shrink: 0;
}

.status-content h6 {
    margin: 0 0 0.25rem;
    font-weight: 600;
    color: var(--text-primary);
    font-size: 0.9rem;
}

.status-content p {
    margin: 0;
    font-size: 0.8rem;
    color: var(--text-secondary);
}

.quick-stats {
    display: flex;
    flex-direction: column;
    gap: 0.75rem;
}

.stat-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.5rem 0;
    border-bottom: 1px solid var(--border-color);
}

.stat-row:last-child {
    border-bottom: none;
}

.stat-row .label {
    font-size: 0.9rem;
    color: var(--text-secondary);
    font-weight: 500;
}

.stat-row .value {
    font-size: 0.9rem;
    color: var(--text-primary);
    font-weight: 600;
}

.admin-tools {
    display: flex;
    flex-direction: column;
}
</style>

<script>
function refreshStats() {
    showLoading('正在刷新统计数据...');
    window.location.reload();
}

function exportData() {
    showInfo('数据导出功能开发中...');
}

function showSystemInfo() {
    showInfo('系统信息功能开发中...');
}

// 统计数字动画
$(document).ready(function() {
    $('.stat-number').each(function() {
        const $this = $(this);
        const countTo = parseInt($this.text()) || 0;
        
        if (countTo > 0) {
            $({ countNum: 0 }).animate({
                countNum: countTo
            }, {
                duration: 2000,
                easing: 'swing',
                step: function() {
                    $this.text(Math.floor(this.countNum));
                },
                complete: function() {
                    $this.text(countTo);
                }
            });
        }
    });
});
</script>
{% endblock %}
