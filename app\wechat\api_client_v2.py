"""
微信API客户端 v2.0
基于端点发现结果的优化版本，支持动态端点检测和自适应API调用
"""
import json
import time
import logging
import requests
from typing import Dict, List, Optional, Tuple

logger = logging.getLogger(__name__)


class WeChatAPIClientV2:
    """微信API客户端 v2.0 - 支持动态端点发现"""
    
    def __init__(self, base_url: str, api_key: str, proxy: Optional[str] = None):
        """
        初始化微信API客户端
        :param base_url: API基础URL
        :param api_key: API密钥
        :param proxy: 代理服务器地址（可选）
        """
        # 规范化base_url
        if base_url and not (base_url.startswith('http://') or base_url.startswith('https://')):
            base_url = 'http://' + base_url
        
        if not api_key:
            raise ValueError("API密钥不能为空")
        
        self.base_url = base_url.rstrip('/')
        self.api_key = api_key
        self.proxy = proxy
        self.timeout = 30
        
        # 端点缓存
        self._endpoint_cache = {}
        self._cache_timestamp = 0
        self._cache_ttl = 300  # 5分钟缓存
        
        # 预定义的端点映射
        self.endpoint_patterns = {
            'login_status': [
                '/login/GetLoginStatus',
                '/api/login/GetLoginStatus',
                '/wechat/login/GetLoginStatus',
                '/v1/login/GetLoginStatus',
                '/login/status',
                '/api/login/status',
                '/api/v1/login/status',
                '/status'
            ],
            'qrcode': [
                '/login/GetLoginQrCodeNewX',
                '/login/GetLoginQrCode',
                '/api/login/qrcode',
                '/login/qrcode',
                '/qrcode',
                '/login/ShowQrCode',
                '/api/qrcode'
            ],
            'check_login': [
                '/login/CheckLoginStatus',
                '/api/login/check',
                '/login/check',
                '/check',
                '/api/check'
            ],
            'logout': [
                '/login/LogOut',
                '/login/logout',
                '/api/login/logout',
                '/logout',
                '/api/logout'
            ]
        }
        
        logger.info(f"初始化WeChatAPIClientV2: base_url={base_url}")
    
    def _make_request(self, method: str, endpoint: str, data: Optional[Dict] = None) -> Dict:
        """发送HTTP请求"""
        url = f"{self.base_url}{endpoint}"
        headers = {
            'Authorization': f'Bearer {self.api_key}',
            'Content-Type': 'application/json',
            'User-Agent': 'WeChatAPI-Client-v2/1.0'
        }
        
        logger.debug(f"发送请求: {method} {url}")
        
        try:
            if method.upper() == 'GET':
                response = requests.get(url, headers=headers, timeout=self.timeout)
            else:
                response = requests.post(url, json=data, headers=headers, timeout=self.timeout)
            
            logger.debug(f"响应状态码: {response.status_code}")
            
            # 处理不同的HTTP状态码
            if response.status_code == 404:
                return {
                    'Code': -2,
                    'Text': '该链接不存在！',
                    'Data': None,
                    '_meta': {'http_status': 404, 'endpoint': endpoint}
                }
            elif response.status_code == 401:
                return {
                    'Code': -3,
                    'Text': '认证失败，请检查API密钥',
                    'Data': None,
                    '_meta': {'http_status': 401, 'endpoint': endpoint}
                }
            elif response.status_code == 403:
                return {
                    'Code': -4,
                    'Text': '权限不足',
                    'Data': None,
                    '_meta': {'http_status': 403, 'endpoint': endpoint}
                }
            
            response.raise_for_status()
            
            # 尝试解析JSON响应
            try:
                json_response = response.json()
                json_response['_meta'] = {
                    'http_status': response.status_code,
                    'endpoint': endpoint,
                    'success': True
                }
                logger.debug(f"API响应: {json_response}")
                return json_response
            except ValueError:
                logger.warning(f"非JSON响应: {response.text[:200]}")
                return {
                    'Code': 500,
                    'Text': f'非JSON响应: {response.text[:100]}',
                    'Data': None,
                    '_meta': {'http_status': response.status_code, 'endpoint': endpoint}
                }
                
        except requests.exceptions.ConnectionError as e:
            logger.error(f"连接失败: {str(e)}")
            return {
                'Code': -1,
                'Text': f'连接失败: 无法连接到API服务器',
                'Data': None,
                '_meta': {'error': 'connection_error', 'endpoint': endpoint}
            }
        except requests.exceptions.Timeout as e:
            logger.error(f"请求超时: {str(e)}")
            return {
                'Code': -1,
                'Text': f'请求超时: API服务器响应超时',
                'Data': None,
                '_meta': {'error': 'timeout', 'endpoint': endpoint}
            }
        except Exception as e:
            logger.error(f"请求异常: {str(e)}")
            return {
                'Code': 500,
                'Text': f'请求异常: {str(e)}',
                'Data': None,
                '_meta': {'error': 'unknown', 'endpoint': endpoint}
            }
    
    def _find_working_endpoint(self, category: str, method: str = 'GET', data: Optional[Dict] = None) -> Optional[str]:
        """查找可用的端点"""
        # 检查缓存
        cache_key = f"{category}_{method}"
        current_time = time.time()
        
        if (cache_key in self._endpoint_cache and 
            current_time - self._cache_timestamp < self._cache_ttl):
            cached_endpoint = self._endpoint_cache[cache_key]
            logger.debug(f"使用缓存的端点: {cached_endpoint}")
            return cached_endpoint
        
        # 测试端点
        endpoints = self.endpoint_patterns.get(category, [])
        for endpoint in endpoints:
            logger.debug(f"测试端点: {endpoint}")
            result = self._make_request(method, endpoint, data)
            
            # 如果不是404错误，认为端点可用
            if result.get('Code') != -2:
                logger.info(f"找到可用端点: {endpoint} (类别: {category})")
                self._endpoint_cache[cache_key] = endpoint
                self._cache_timestamp = current_time
                return endpoint
        
        logger.warning(f"未找到可用的 {category} 端点")
        return None
    
    def _call_api_with_fallback(self, category: str, method: str = 'GET', data: Optional[Dict] = None) -> Dict:
        """使用备用端点调用API"""
        endpoint = self._find_working_endpoint(category, method, data)
        
        if endpoint:
            return self._make_request(method, endpoint, data)
        else:
            return {
                'Code': -2,
                'Text': f'所有 {category} 端点都不可用',
                'Data': None,
                '_meta': {'error': 'no_working_endpoint', 'category': category}
            }
    
    def get_login_status(self) -> Dict:
        """获取登录状态"""
        logger.info("获取登录状态")
        
        result = self._call_api_with_fallback('login_status', 'GET')
        
        # 如果所有端点都不可用，返回模拟的离线状态
        if result.get('Code') == -2 and 'endpoint' not in result.get('_meta', {}):
            logger.warning("所有登录状态端点都不可用，返回模拟离线状态")
            return {
                'Code': 200,
                'Text': 'API服务不可用，显示离线状态',
                'Data': {
                    'loginState': 0,
                    'status': 'offline',
                    'message': 'API服务不可用'
                },
                '_meta': {'simulated': True}
            }
        
        return result
    
    def get_login_qrcode(self) -> Dict:
        """获取登录二维码"""
        logger.info("获取登录二维码")
        
        # 准备请求数据
        data = {
            "Proxy": self.proxy or "",
            "Check": True
        }
        
        result = self._call_api_with_fallback('qrcode', 'POST', data)
        
        # 处理特殊响应
        if result.get('Code') == 300 and "loginState == MMLoginStateNoLogin" in result.get('Text', ''):
            logger.info("检测到未登录状态，重新获取二维码")
            data['Check'] = False
            result = self._call_api_with_fallback('qrcode', 'POST', data)
        
        # 处理成功响应
        if result.get('Code') == 200 and result.get('Data'):
            data_obj = result.get('Data', {})
            qrcode_base64 = data_obj.get('qrcode', '')
            key = data_obj.get('uuid', data_obj.get('key', ''))
            expired_time = data_obj.get('expired_time', data_obj.get('expiredTime', 120))
            
            if qrcode_base64:
                # 处理base64数据
                if qrcode_base64.startswith('data:'):
                    qrcode_base64 = qrcode_base64.split(',', 1)[1]
                
                return {
                    'Code': 200,
                    'Text': '获取二维码成功',
                    'Data': {
                        'qrcode': qrcode_base64,
                        'uuid': key,
                        'expired_time': expired_time
                    },
                    '_meta': result.get('_meta', {})
                }
        
        return result
    
    def check_login_status(self) -> Dict:
        """检查登录状态（扫码状态）"""
        logger.info("检查扫码登录状态")
        
        result = self._call_api_with_fallback('check_login', 'GET')
        
        # 处理特殊的未登录响应
        if result.get('Code') == -2 and 'endpoint' in result.get('_meta', {}):
            logger.info("检测到特殊未登录响应，返回等待扫描状态")
            return {
                'Code': 201,
                'Text': '等待扫描',
                'Data': {
                    'status': 'waiting',
                    'message': '请使用微信扫描二维码'
                },
                '_meta': result.get('_meta', {})
            }
        
        return result
    
    def logout(self) -> Dict:
        """退出登录"""
        logger.info("退出微信登录")
        return self._call_api_with_fallback('logout', 'GET')
    
    def get_api_info(self) -> Dict:
        """获取API信息和可用端点"""
        info = {
            'base_url': self.base_url,
            'cached_endpoints': self._endpoint_cache.copy(),
            'cache_timestamp': self._cache_timestamp,
            'available_categories': list(self.endpoint_patterns.keys())
        }
        
        # 测试所有类别的端点可用性
        availability = {}
        for category in self.endpoint_patterns.keys():
            endpoint = self._find_working_endpoint(category)
            availability[category] = {
                'available': endpoint is not None,
                'endpoint': endpoint
            }
        
        info['endpoint_availability'] = availability
        return info
