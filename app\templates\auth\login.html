{% extends "base.html" %}

{% block title %}登录 - YBA监控管理系统{% endblock %}

{% block login_content %}
<div class="login-container">
    <div class="login-card">
        <div class="login-header">
            <div class="login-logo">
                <i class="bi bi-grid-3x3-gap-fill"></i>
            </div>
            <h2 class="login-title">YBA监控管理系统</h2>
            <p class="login-subtitle">选择登录方式</p>
        </div>
        
        <div class="login-body">
            {% if error %}
            <div class="alert alert-danger" role="alert">
                <i class="bi bi-exclamation-triangle me-2"></i>
                {{ error }}
            </div>
            {% endif %}

            <!-- 登录方式选择 -->
            <div class="login-method-selector">
                <div class="method-tabs">
                    <button type="button" class="method-tab active" data-method="v1">
                        <i class="bi bi-key me-2"></i>
                        <span>V1登录</span>
                        <small>Token认证</small>
                    </button>
                    <button type="button" class="method-tab" data-method="v2">
                        <i class="bi bi-qr-code me-2"></i>
                        <span>V2登录</span>
                        <small>WXID认证</small>
                    </button>
                    <button type="button" class="method-tab" data-method="v3">
                        <i class="bi bi-gear me-2"></i>
                        <span>V3登录</span>
                        <small>GeWeChat</small>
                    </button>
                </div>
            </div>

            <!-- V1登录表单 -->
            <div id="v1-login" class="login-method-form active">
                <form method="POST" class="login-form needs-validation" novalidate>
                    {{ form.hidden_tag() }}
                    <input type="hidden" name="login_method" value="v1">

                    <div class="form-group">
                        <label for="token" class="form-label">
                            <i class="bi bi-key me-2"></i>Token
                        </label>
                        <div class="input-group">
                            <span class="input-group-text">
                                <i class="bi bi-shield-lock"></i>
                            </span>
                            {{ form.token(class="form-control", placeholder="请输入您的Token", required=true) }}
                            <div class="invalid-feedback">
                                请输入有效的Token
                            </div>
                        </div>
                        <small class="form-text text-muted">
                            <i class="bi bi-info-circle me-1"></i>
                            使用第一套API系统，基于Token认证
                        </small>
                    </div>

                    <div class="form-group">
                        {{ form.submit(class="btn btn-primary btn-login") }}
                    </div>
                </form>
            </div>

            <!-- V2登录表单 -->
            <div id="v2-login" class="login-method-form">
                <form method="POST" class="login-form needs-validation" novalidate>
                    {{ form.hidden_tag() }}
                    <input type="hidden" name="login_method" value="v2">

                    <div class="form-group">
                        <label for="wxid" class="form-label">
                            <i class="bi bi-person-badge me-2"></i>微信ID
                        </label>
                        <div class="input-group">
                            <span class="input-group-text">
                                <i class="bi bi-wechat"></i>
                            </span>
                            <input type="text" name="wxid" class="form-control" placeholder="请输入微信ID" required>
                            <div class="invalid-feedback">
                                请输入有效的微信ID
                            </div>
                        </div>
                        <small class="form-text text-muted">
                            <i class="bi bi-info-circle me-1"></i>
                            使用第二套API系统，基于WXID认证
                        </small>
                    </div>

                    <div class="form-group">
                        <button type="submit" class="btn btn-success btn-login">
                            <i class="bi bi-box-arrow-in-right me-2"></i>V2登录
                        </button>
                    </div>
                </form>
            </div>

            <!-- V3登录表单 -->
            <div id="v3-login" class="login-method-form">
                <form method="POST" class="login-form needs-validation" novalidate>
                    {{ form.hidden_tag() }}
                    <input type="hidden" name="login_method" value="v3">

                    <div class="form-group">
                        <label for="token" class="form-label">
                            <i class="bi bi-key me-2"></i>Token
                        </label>
                        <div class="input-group">
                            <span class="input-group-text">
                                <i class="bi bi-shield-lock"></i>
                            </span>
                            {{ form.token(class="form-control", placeholder="请输入您的Token", required=true) }}
                            <div class="invalid-feedback">
                                请输入有效的Token
                            </div>
                        </div>
                        <small class="form-text text-muted">
                            <i class="bi bi-info-circle me-1"></i>
                            V3 API需要Token认证
                        </small>
                    </div>

                    <div class="form-group">
                        <label for="app_id" class="form-label">
                            <i class="bi bi-app-indicator me-2"></i>应用ID（可选）
                        </label>
                        <div class="input-group">
                            <span class="input-group-text">
                                <i class="bi bi-gear"></i>
                            </span>
                            <input type="text" name="app_id" class="form-control" placeholder="请输入应用ID（可选）">
                            <div class="invalid-feedback">
                                请输入有效的应用ID
                            </div>
                        </div>
                        <small class="form-text text-muted">
                            <i class="bi bi-info-circle me-1"></i>
                            应用ID可选，如果不填写，登录时会自动获取
                        </small>
                    </div>

                    <div class="form-group">
                        <button type="submit" class="btn btn-warning btn-login">
                            <i class="bi bi-box-arrow-in-right me-2"></i>V3登录
                        </button>
                    </div>
                </form>
            </div>
        </div>
        
        <div class="login-footer">
            <div class="copyright">
                <small class="text-muted">© 2025 YBA监控管理系统</small>
            </div>
        </div>
    </div>
</div>

<style>
.login-container {
    min-height: 100vh;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 2rem;
    background: linear-gradient(135deg, #0f172a 0%, #1e293b 50%, #334155 100%);
    position: relative;
    overflow: hidden;
}

.login-container::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grid" width="10" height="10" patternUnits="userSpaceOnUse"><path d="M 10 0 L 0 0 0 10" fill="none" stroke="%23475569" stroke-width="0.5" opacity="0.3"/></pattern></defs><rect width="100" height="100" fill="url(%23grid)"/></svg>');
    opacity: 0.5;
}

.login-card {
    background: rgba(30, 41, 59, 0.95);
    border: 1px solid rgba(71, 85, 105, 0.3);
    border-radius: 20px;
    padding: 0;
    width: 100%;
    max-width: 420px;
    box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.5);
    backdrop-filter: blur(20px);
    position: relative;
    z-index: 1;
    overflow: hidden;
    animation: slideInUp 0.6s ease-out;
}

@keyframes slideInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.login-header {
    text-align: center;
    padding: 2.5rem 2rem 1.5rem;
    background: linear-gradient(135deg, rgba(99, 102, 241, 0.1) 0%, rgba(139, 92, 246, 0.1) 100%);
    border-bottom: 1px solid rgba(71, 85, 105, 0.3);
}

.login-logo {
    width: 80px;
    height: 80px;
    background: linear-gradient(135deg, #6366f1, #8b5cf6);
    border-radius: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 1.5rem;
    box-shadow: 0 10px 25px rgba(99, 102, 241, 0.3);
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0%, 100% {
        transform: scale(1);
    }
    50% {
        transform: scale(1.05);
    }
}

.login-logo i {
    font-size: 2rem;
    color: white;
}

.login-title {
    color: #f8fafc;
    font-size: 1.75rem;
    font-weight: 700;
    margin: 0 0 0.5rem;
    background: linear-gradient(135deg, #f8fafc, #cbd5e1);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.login-subtitle {
    color: #94a3b8;
    margin: 0;
    font-size: 0.95rem;
}

.login-body {
    padding: 2rem;
}

/* 登录方式选择器 */
.login-method-selector {
    margin-bottom: 2rem;
}

.method-tabs {
    display: flex;
    background: rgba(15, 23, 42, 0.8);
    border-radius: 12px;
    padding: 0.25rem;
    gap: 0.25rem;
}

.method-tab {
    flex: 1;
    background: transparent;
    border: none;
    padding: 1rem;
    border-radius: 8px;
    color: #94a3b8;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 0.25rem;
    font-size: 0.9rem;
}

.method-tab:hover {
    background: rgba(51, 65, 85, 0.5);
    color: #e2e8f0;
}

.method-tab.active {
    background: linear-gradient(135deg, #6366f1, #8b5cf6);
    color: white;
    box-shadow: 0 4px 12px rgba(99, 102, 241, 0.3);
}

.method-tab span {
    font-weight: 600;
}

.method-tab small {
    font-size: 0.75rem;
    opacity: 0.8;
}

.method-tab i {
    font-size: 1.25rem;
}

/* 登录表单切换 */
.login-method-form {
    display: none;
    animation: fadeIn 0.3s ease-in-out;
}

.login-method-form.active {
    display: block;
}

@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.login-form .form-group {
    margin-bottom: 1.5rem;
}

.form-label {
    color: #e2e8f0;
    font-weight: 600;
    margin-bottom: 0.75rem;
    display: flex;
    align-items: center;
}

.input-group-text {
    background: rgba(51, 65, 85, 0.8);
    border: 1px solid rgba(71, 85, 105, 0.5);
    color: #94a3b8;
    border-right: none;
}

.form-control {
    background: rgba(51, 65, 85, 0.8);
    border: 1px solid rgba(71, 85, 105, 0.5);
    color: #f8fafc;
    padding: 0.875rem 1rem;
    font-size: 1rem;
    border-left: none;
    transition: all 0.3s ease;
}

.form-control:focus {
    background: rgba(30, 41, 59, 0.9);
    border-color: #6366f1;
    box-shadow: 0 0 0 0.2rem rgba(99, 102, 241, 0.25);
    color: #f8fafc;
}

.form-control::placeholder {
    color: #64748b;
}

.btn-login {
    width: 100%;
    padding: 0.875rem;
    font-size: 1rem;
    font-weight: 600;
    background: linear-gradient(135deg, #6366f1, #8b5cf6);
    border: none;
    border-radius: 12px;
    color: white;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.btn-login:hover {
    transform: translateY(-2px);
    box-shadow: 0 10px 25px rgba(99, 102, 241, 0.4);
}

.btn-login:active {
    transform: translateY(0);
}

.login-footer {
    padding: 1.5rem 2rem 2rem;
    border-top: 1px solid rgba(71, 85, 105, 0.3);
    background: rgba(15, 23, 42, 0.5);
}

.login-info {
    display: flex;
    justify-content: space-around;
    margin-bottom: 1rem;
}

.info-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 0.5rem;
    font-size: 0.8rem;
    color: #94a3b8;
}

.info-item i {
    font-size: 1.25rem;
}

.copyright {
    text-align: center;
}

.alert {
    border-radius: 12px;
    border: none;
    margin-bottom: 1.5rem;
    animation: shake 0.5s ease-in-out;
}

@keyframes shake {
    0%, 100% { transform: translateX(0); }
    25% { transform: translateX(-5px); }
    75% { transform: translateX(5px); }
}

/* 响应式设计 */
@media (max-width: 480px) {
    .login-container {
        padding: 1rem;
    }
    
    .login-card {
        max-width: 100%;
    }
    
    .login-header {
        padding: 2rem 1.5rem 1rem;
    }
    
    .login-body {
        padding: 1.5rem;
    }
    
    .login-footer {
        padding: 1rem 1.5rem 1.5rem;
    }
    
    .login-info {
        flex-direction: column;
        gap: 1rem;
    }
}
</style>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // 登录方式切换
    const methodTabs = document.querySelectorAll('.method-tab');
    const methodForms = document.querySelectorAll('.login-method-form');

    methodTabs.forEach(tab => {
        tab.addEventListener('click', function() {
            const method = this.dataset.method;

            // 更新标签状态
            methodTabs.forEach(t => t.classList.remove('active'));
            this.classList.add('active');

            // 切换表单
            methodForms.forEach(form => {
                form.classList.remove('active');
                if (form.id === method + '-login') {
                    form.classList.add('active');
                }
            });

            // 更新页面标题
            const subtitle = document.querySelector('.login-subtitle');
            if (method === 'v1') {
                subtitle.textContent = '请输入您的Token进行登录';
            } else if (method === 'v2') {
                subtitle.textContent = '请输入您的微信ID进行登录';
            } else if (method === 'v3') {
                subtitle.textContent = '请输入Token和应用ID进行登录';
            }
        });
    });

    // 表单验证
    const forms = document.querySelectorAll('.needs-validation');
    forms.forEach(form => {
        form.addEventListener('submit', function(event) {
            if (!form.checkValidity()) {
                event.preventDefault();
                event.stopPropagation();
            }
            form.classList.add('was-validated');
        });
    });
});
</script>
{% endblock %}
