#!/usr/bin/env python3
"""
清理旧启动文件脚本
将旧的启动文件移动到backup目录
"""
import os
import shutil
import logging
from datetime import datetime

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def create_backup_directory():
    """创建备份目录"""
    backup_dir = "backup_startup_files"
    if not os.path.exists(backup_dir):
        os.makedirs(backup_dir)
        logger.info(f"创建备份目录: {backup_dir}")
    return backup_dir

def backup_file(file_path, backup_dir):
    """备份文件"""
    if os.path.exists(file_path):
        backup_path = os.path.join(backup_dir, os.path.basename(file_path))
        shutil.move(file_path, backup_path)
        logger.info(f"备份文件: {file_path} -> {backup_path}")
        return True
    else:
        logger.info(f"文件不存在，跳过: {file_path}")
        return False

def main():
    """主函数"""
    logger.info("开始清理旧启动文件...")
    
    # 创建备份目录
    backup_dir = create_backup_directory()
    
    # 需要备份的旧启动文件
    old_files = [
        "run.py",
        "start.py", 
        "quick_start.py",
        "check_login_status.py"
    ]
    
    backed_up_count = 0
    
    for file_path in old_files:
        if backup_file(file_path, backup_dir):
            backed_up_count += 1
    
    # 创建说明文件
    readme_path = os.path.join(backup_dir, "README.md")
    with open(readme_path, 'w', encoding='utf-8') as f:
        f.write(f"""# 旧启动文件备份

备份时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}

## 说明

这些文件已被新的统一启动系统替代：

- **main.py** - 新的统一启动脚本
- **setup_env.py** - 环境配置工具

## 备份的文件

""")
        for file_path in old_files:
            if os.path.exists(os.path.join(backup_dir, os.path.basename(file_path))):
                f.write(f"- `{file_path}` - 已备份\n")
            else:
                f.write(f"- `{file_path}` - 不存在\n")
        
        f.write(f"""
## 新的启动方式

```bash
# 配置环境（首次使用）
python setup_env.py

# 启动系统
python main.py
```

## 恢复旧文件

如果需要恢复旧文件，可以从此目录复制回项目根目录。

""")
    
    logger.info(f"清理完成！备份了 {backed_up_count} 个文件到 {backup_dir}")
    logger.info("现在可以使用新的启动方式:")
    logger.info("  python main.py")

if __name__ == '__main__':
    main()
