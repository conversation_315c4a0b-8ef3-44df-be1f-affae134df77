"""
统一数据模型
整合两个系统的数据模型
"""
import pymysql
from app.config import Config
import logging
import re
from typing import Dict, Optional

logger = logging.getLogger(__name__)


def get_db_connection():
    """获取数据库连接"""
    try:
        connection = pymysql.connect(**Config.get_db_config())
        return connection
    except Exception as e:
        logger.error(f"数据库连接失败: {str(e)}")
        raise


class Agent:
    """代理商模型（支持双API系统）"""

    def __init__(self, id, name, token=None, wechat_base_url=None, proxy=None, group=None,
                 api_version='v1', wxid=None, device_id=None, app_id=None):
        self.id = id
        self.name = name
        self.token = token
        self.wechat_base_url = wechat_base_url or ''
        self.proxy = proxy
        self.group = group or ''
        self.wechat_login_status = 0
        self.vip_is_active = True
        self.wechat_is_active = False

        # V2 API支持
        self.api_version = api_version
        self.wxid = wxid
        self.device_id = device_id

        # V3 API支持
        self.app_id = app_id
    
    @property
    def is_admin(self):
        """判断是否为管理员"""
        return self.token == Config.ADMIN_TOKEN

    def parse_proxy_string(self) -> Optional[Dict[str, str]]:
        """
        解析代理字符串，支持多种格式

        支持的格式：
        - socks5://username:password@host:port
        - *****************************:port
        - username:password@host:port
        - host:port

        Returns:
            Dict包含ProxyIp, ProxyUser, ProxyPassword字段，用于V2 API调用
            如果解析失败返回None
        """
        if not self.proxy:
            return None

        proxy_str = self.proxy.strip()
        if not proxy_str:
            return None

        try:
            # 匹配完整的代理URL格式: protocol://username:password@host:port
            full_pattern = r'^(?:(?P<protocol>https?|socks[45]?)://)?(?:(?P<username>[^:@]+):(?P<password>[^@]+)@)?(?P<host>[^:]+):(?P<port>\d+)$'
            match = re.match(full_pattern, proxy_str)

            if match:
                groups = match.groupdict()
                proxy_info = {
                    'ProxyIp': f"{groups['host']}:{groups['port']}",
                    'ProxyUser': groups.get('username', ''),
                    'ProxyPassword': groups.get('password', '')
                }

                logger.debug(f"解析代理字符串成功: {proxy_str} -> {proxy_info}")
                return proxy_info

            # 如果正则匹配失败，尝试简单的host:port格式
            simple_pattern = r'^([^:]+):(\d+)$'
            simple_match = re.match(simple_pattern, proxy_str)
            if simple_match:
                host, port = simple_match.groups()
                proxy_info = {
                    'ProxyIp': f"{host}:{port}",
                    'ProxyUser': '',
                    'ProxyPassword': ''
                }

                logger.debug(f"解析简单代理格式成功: {proxy_str} -> {proxy_info}")
                return proxy_info

            logger.warning(f"无法解析代理字符串: {proxy_str}")
            return None

        except Exception as e:
            logger.error(f"解析代理字符串时发生错误: {proxy_str}, 错误: {str(e)}")
            return None

    def get_proxy_for_v2_api(self) -> Optional[Dict[str, str]]:
        """
        获取用于V2 API调用的代理信息

        Returns:
            符合V2 API要求的代理信息字典，如果没有代理或解析失败返回None
        """
        return self.parse_proxy_string()
    
    @staticmethod
    def get_by_token(token):
        """根据Token获取代理商"""
        if token == Config.ADMIN_TOKEN:
            # 返回管理员对象
            return Agent(
                id=0,
                name="管理员",
                token=Config.ADMIN_TOKEN,
                wechat_base_url="",
                proxy=None,
                group="ADMIN"
            )
        
        connection = get_db_connection()
        try:
            with connection.cursor(pymysql.cursors.DictCursor) as cursor:
                sql = """
                SELECT * FROM agents 
                WHERE token = %s AND (wechat_is_active = 1 OR vip_is_active = 1)
                """
                cursor.execute(sql, (token,))
                data = cursor.fetchone()
                
                if data:
                    agent = Agent(
                        id=data['id'],
                        name=data['name'],
                        token=data['token'],
                        wechat_base_url=data.get('wechat_base_url', ''),
                        proxy=data.get('proxy'),
                        group=data.get('group', ''),
                        api_version=data.get('api_version', 'v1'),
                        wxid=data.get('wxid'),
                        device_id=data.get('device_id'),
                        app_id=data.get('app_id')
                    )
                    agent.wechat_login_status = data.get('wechat_login_status', 0)
                    agent.vip_is_active = data.get('vip_is_active', True)
                    agent.wechat_is_active = data.get('wechat_is_active', False)
                    return agent
                
                return None
        finally:
            connection.close()

    @staticmethod
    def get_by_wxid(wxid):
        """根据WXID获取代理商（V2 API）"""
        connection = get_db_connection()
        try:
            with connection.cursor(pymysql.cursors.DictCursor) as cursor:
                sql = """
                SELECT * FROM agents
                WHERE wxid = %s AND wechat_is_active = 1 AND api_version = 'v2'
                """
                cursor.execute(sql, (wxid,))
                data = cursor.fetchone()

                if data:
                    agent = Agent(
                        id=data['id'],
                        name=data['name'],
                        token=data.get('token'),
                        wechat_base_url=data.get('wechat_base_url', ''),
                        proxy=data.get('proxy'),
                        group=data.get('group', ''),
                        api_version=data.get('api_version', 'v2'),
                        wxid=data['wxid'],
                        device_id=data.get('device_id'),
                        app_id=data.get('app_id')
                    )
                    agent.wechat_login_status = data.get('wechat_login_status', 0)
                    agent.vip_is_active = data.get('vip_is_active', True)
                    agent.wechat_is_active = data.get('wechat_is_active', False)
                    return agent

                return None
        finally:
            connection.close()

    @staticmethod
    def get_by_id(agent_id):
        """根据ID获取代理商"""
        if agent_id == 0:
            # 返回管理员对象
            return Agent(
                id=0,
                name="管理员",
                token=Config.ADMIN_TOKEN,
                wechat_base_url="",
                proxy=None,
                group="ADMIN"
            )
        
        connection = get_db_connection()
        try:
            with connection.cursor(pymysql.cursors.DictCursor) as cursor:
                sql = "SELECT * FROM agents WHERE id = %s"
                cursor.execute(sql, (agent_id,))
                data = cursor.fetchone()
                
                if data:
                    agent = Agent(
                        id=data['id'],
                        name=data['name'],
                        token=data['token'],
                        wechat_base_url=data.get('wechat_base_url', ''),
                        proxy=data.get('proxy'),
                        group=data.get('group', ''),
                        api_version=data.get('api_version', 'v1'),
                        wxid=data.get('wxid'),
                        device_id=data.get('device_id'),
                        app_id=data.get('app_id')
                    )
                    agent.wechat_login_status = data.get('wechat_login_status', 0)
                    agent.vip_is_active = data.get('vip_is_active', True)
                    agent.wechat_is_active = data.get('wechat_is_active', False)
                    return agent
                
                return None
        finally:
            connection.close()


class VIPCard:
    """VIP会员卡模型"""
    
    @staticmethod
    def get_by_agent(agent_id, limit=50, offset=0):
        """获取代理商的会员卡列表"""
        connection = get_db_connection()
        try:
            with connection.cursor(pymysql.cursors.DictCursor) as cursor:
                sql = """
                SELECT * FROM vip_cards 
                WHERE agent_id = %s 
                ORDER BY created_at DESC 
                LIMIT %s OFFSET %s
                """
                cursor.execute(sql, (agent_id, limit, offset))
                return cursor.fetchall()
        finally:
            connection.close()
    
    @staticmethod
    def count_by_agent(agent_id):
        """统计代理商的会员卡数量"""
        connection = get_db_connection()
        try:
            with connection.cursor() as cursor:
                sql = "SELECT COUNT(*) FROM vip_cards WHERE agent_id = %s"
                cursor.execute(sql, (agent_id,))
                return cursor.fetchone()[0]
        finally:
            connection.close()


def get_agent_api_key(agent_id):
    """获取代理商API密钥（使用token字段）"""
    connection = get_db_connection()
    try:
        with connection.cursor(pymysql.cursors.DictCursor) as cursor:
            sql = "SELECT token FROM agents WHERE id = %s"
            cursor.execute(sql, (agent_id,))
            result = cursor.fetchone()
            return result['token'] if result else None
    finally:
        connection.close()
