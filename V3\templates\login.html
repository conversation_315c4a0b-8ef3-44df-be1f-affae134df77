<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>YBA-PPMT微信登录</title>
    <style>
        body {
            font-family: "PingFang SC", "Microsoft YaHei", sans-serif;
            background-color: #f0f0f0;
            margin: 0;
            padding: 0;
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
        }
        .container {
            background-color: #fff;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            width: 90%;
            max-width: 400px;
            padding: 20px;
            text-align: center;
            position: relative;
        }
        .header {
            margin-bottom: 20px;
        }
        .header h1 {
            color: #09bb07;
            font-size: 24px;
            margin: 0;
        }
        .qrcode-container {
            margin: 20px 0;
            position: relative;
        }
        .qrcode-img {
            width: 200px;
            height: 200px;
            border: 1px solid #ddd;
            margin: 0 auto;
            display: flex;
            justify-content: center;
            align-items: center;
        }
        .qrcode-img img {
            max-width: 100%;
            max-height: 100%;
        }
        .qrcode-expired {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-color: rgba(255, 255, 255, 0.8);
            display: flex;
            justify-content: center;
            align-items: center;
            font-size: 16px;
            color: #ff3b30;
            display: none;
        }
        .timer {
            margin: 10px 0;
            font-size: 14px;
            color: #666;
        }
        .captcha-container {
            margin: 20px 0;
            display: block;
        }
        .captcha-input {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
            box-sizing: border-box;
            font-size: 16px;
            text-align: center;
            margin-bottom: 10px;
        }
        .btn {
            background-color: #09bb07;
            color: white;
            border: none;
            border-radius: 4px;
            padding: 10px 20px;
            font-size: 16px;
            cursor: pointer;
            width: 100%;
            margin: 10px 0;
            transition: background-color 0.3s;
        }
        .btn:hover {
            background-color: #07a906;
        }
        .btn:active {
            background-color: #068c05;
        }
        .btn:disabled {
            background-color: #ccc;
            cursor: not-allowed;
        }
        .status {
            margin: 10px 0;
            font-size: 14px;
            color: #666;
        }
        .loading {
            display: inline-block;
            width: 20px;
            height: 20px;
            border: 3px solid #f3f3f3;
            border-top: 3px solid #09bb07;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin-right: 10px;
            vertical-align: middle;
        }
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        .tips {
            margin-top: 15px;
            padding: 10px;
            background-color: #f8f8f8;
            border-radius: 4px;
            font-size: 13px;
            color: #666;
            text-align: left;
        }
        .tips p {
            margin: 5px 0;
        }
        .tips h3 {
            margin: 5px 0;
            color: #09bb07;
            font-size: 14px;
        }
        .watermark {
            position: absolute;
            bottom: 10px;
            right: 15px;
            font-size: 12px;
            color: #ccc;
        }
        .debug-section {
            margin-top: 20px;
            display: none;
            border-top: 1px solid #eee;
            padding-top: 15px;
        }
        .debug-section textarea {
            width: 100%;
            height: 150px;
            margin-top: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
            padding: 5px;
            font-size: 12px;
            font-family: monospace;
        }
        .small-btn {
            padding: 5px 10px;
            font-size: 12px;
            width: auto;
            display: inline-block;
            margin: 5px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>YBA-PPMT微信登录</h1>
            <p id="sub-heading">扫描二维码登录微信</p>
        </div>
        
        <div class="qrcode-container">
            <div class="qrcode-img" id="qrcode">
                <div class="loading"></div>
            </div>
            <div class="qrcode-expired" id="qrcode-expired">
                二维码已过期，点击刷新
            </div>
        </div>
        
        <div class="timer" id="timer">有效时间：<span id="countdown">60</span>秒</div>
        
        <div class="captcha-container" id="captcha-container">
            <input type="text" class="captcha-input" id="captcha-input" placeholder="验证码(可选)">
        </div>
        
        <button class="btn" id="confirm-btn">已扫码，确认登录</button>
        <button class="btn" id="refresh-btn" style="display: none;">刷新二维码</button>
        
        <div class="status" id="status">正在加载...</div>
        
        <div class="tips">
            <h3>登录提示：</h3>
            <p>1. 请使用微信扫描上方二维码</p>
            <p>2. 在手机上确认登录</p>
            <p>3. 若需要验证码，请在验证码框中输入</p>
            <p>4. 点击确认登录按钮完成登录</p>
            <p><a href="#" id="debug-toggle" style="color: #ccc; font-size: 12px;">调试模式</a></p>
        </div>
        
        <div class="debug-section" id="debug-section">
            <h3>调试信息</h3>
            <div>
                <button class="btn small-btn" id="debug-refresh">手动刷新二维码</button>
                <button class="btn small-btn" id="debug-check">检查登录状态</button>
                <button class="btn small-btn" id="debug-direct">尝试直接登录</button>
            </div>
            <textarea id="debug-info" readonly>等待操作...</textarea>
        </div>
        
        <div class="watermark">YBA-PPMT</div>
    </div>

    <script>
        // 基本变量定义
        const wxid = "{{ wxid }}";
        const token = "{{ token }}";
        const serverUrl = "{{ server_url }}";
        
        let countdownInterval;
        let statusCheckInterval;
        let expiryTime = 60;
        let loginStatus = 'pending';
        let deviceInfo = null;
        let debugMode = false;
        
        // DOM元素
        const qrcodeImg = document.getElementById('qrcode');
        const qrcodeExpired = document.getElementById('qrcode-expired');
        const countdownEl = document.getElementById('countdown');
        const timerEl = document.getElementById('timer');
        const captchaContainer = document.getElementById('captcha-container');
        const captchaInput = document.getElementById('captcha-input');
        const submitCaptchaBtn = document.getElementById('submit-captcha');
        const confirmBtn = document.getElementById('confirm-btn');
        const refreshBtn = document.getElementById('refresh-btn');
        const statusEl = document.getElementById('status');
        const subHeadingEl = document.getElementById('sub-heading');
        const debugToggle = document.getElementById('debug-toggle');
        const debugSection = document.getElementById('debug-section');
        const debugInfo = document.getElementById('debug-info');
        const debugRefreshBtn = document.getElementById('debug-refresh');
        const debugCheckBtn = document.getElementById('debug-check');
        const debugDirectBtn = document.getElementById('debug-direct');
        
        // 获取设备信息
        function getDeviceInfo() {
            const info = {
                userAgent: navigator.userAgent,
                language: navigator.language,
                platform: navigator.platform,
                screenWidth: window.screen.width,
                screenHeight: window.screen.height,
                timezone: Intl.DateTimeFormat().resolvedOptions().timeZone,
                timestamp: new Date().toISOString()
            };
            return info;
        }
        
        // 添加调试信息
        function logDebug(message, obj = null) {
            const timestamp = new Date().toLocaleTimeString();
            let logMessage = `[${timestamp}] ${message}`;
            
            if (obj) {
                try {
                    if (typeof obj === 'string') {
                        logMessage += `\n${obj}`;
                    } else {
                        logMessage += `\n${JSON.stringify(obj, null, 2)}`;
                    }
                } catch (e) {
                    logMessage += `\n[无法序列化对象: ${e.message}]`;
                }
            }
            
            debugInfo.value = `${logMessage}\n\n${debugInfo.value}`.slice(0, 5000);
            console.log(message, obj);
        }
        
        // 页面加载完成后获取二维码
        document.addEventListener('DOMContentLoaded', () => {
            // 收集设备信息
            deviceInfo = getDeviceInfo();
            
            // 获取二维码
            getQrCode();
            
            // 事件绑定
            confirmBtn.addEventListener('click', confirmLogin);
            refreshBtn.addEventListener('click', getQrCode);
            qrcodeExpired.addEventListener('click', getQrCode);
            
            // 验证码输入框按回车触发确认登录按钮
            captchaInput.addEventListener('keypress', (e) => {
                if (e.key === 'Enter') {
                    confirmBtn.click();
                }
            });
            
            // 调试模式切换
            debugToggle.addEventListener('click', (e) => {
                e.preventDefault();
                debugMode = !debugMode;
                debugSection.style.display = debugMode ? 'block' : 'none';
                logDebug(`调试模式 ${debugMode ? '开启' : '关闭'}`);
            });
            
            // 调试按钮
            debugRefreshBtn.addEventListener('click', () => {
                logDebug('手动触发刷新二维码');
                getQrCode();
            });
            
            debugCheckBtn.addEventListener('click', () => {
                logDebug('手动检查登录状态');
                checkLoginStatus();
            });
            
            debugDirectBtn.addEventListener('click', async () => {
                logDebug('尝试直接登录');
                try {
                    const response = await fetch(`${serverUrl}/api/confirm_login`, {
                        method: 'POST',
                        headers: { 'Content-Type': 'application/json' },
                        body: JSON.stringify({ token, deviceInfo })
                    });
                    
                    const data = await response.json();
                    logDebug('直接登录响应', data);
                } catch (error) {
                    logDebug('直接登录出错', error.message);
                }
            });
            
            // 检查URL参数是否有debug=1
            if (new URLSearchParams(window.location.search).get('debug') === '1') {
                debugMode = true;
                debugSection.style.display = 'block';
                logDebug('通过URL参数开启调试模式');
            }
        });
        
        // 获取二维码
        async function getQrCode() {
            // 重置状态
            loginStatus = 'pending';
            qrcodeImg.innerHTML = '<div class="loading"></div>';
            qrcodeExpired.style.display = 'none';
            confirmBtn.style.display = 'block';
            refreshBtn.style.display = 'none';
            statusEl.textContent = '正在获取二维码...';
            
            if (debugMode) {
                logDebug('开始获取二维码', {token, wxid});
            }
            
            try {
                const response = await fetch(`${serverUrl}/api/get_qrcode`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({ 
                        token,
                        deviceInfo
                    })
                });
                
                const data = await response.json();
                console.log('获取二维码响应:', data);
                
                if (debugMode) {
                    logDebug('二维码响应', data);
                }
                
                if (data.ret === 200) {
                    if (data.status === 'online') {
                        // 已在线，无需登录
                        loginStatus = 'online';
                        statusEl.textContent = `已在线，无需登录 (${data.nickname || '未知用户'})`;
                        subHeadingEl.textContent = '当前微信号已登录，无需重新扫码';
                        timerEl.style.display = 'none';
                        confirmBtn.style.display = 'none';
                        qrcodeImg.innerHTML = '<div style="color: #09bb07; font-size: 18px;">✓ 已登录</div>';
                        clearInterval(countdownInterval);
                        clearInterval(statusCheckInterval);
                        return;
                    }
                    
                    if (!data.data || !data.data.qr_image) {
                        throw new Error('服务器未返回二维码数据');
                    }
                    
                    // 显示二维码
                    const qrImage = data.data.qr_image;
                    
                    // 确保二维码数据不包含data:image前缀
                    let qrImageSrc = qrImage;
                    if (!qrImageSrc.startsWith('data:')) {
                        qrImageSrc = `data:image/png;base64,${qrImageSrc}`;
                    }
                    
                    if (debugMode) {
                        // 在调试模式下检查数据是否为有效的base64
                        try {
                            const validBase64 = /^[A-Za-z0-9+/]*={0,2}$/;
                            const base64Part = qrImageSrc.split(',')[1] || qrImageSrc;
                            const isValid = validBase64.test(base64Part);
                            logDebug(`二维码数据验证: ${isValid ? '有效' : '无效'} (长度: ${base64Part.length})`);
                            
                            // 创建临时图片检查是否能加载
                            const tempImg = new Image();
                            tempImg.onload = () => logDebug('二维码图片加载成功');
                            tempImg.onerror = (e) => logDebug('二维码图片加载失败', e.message);
                            tempImg.src = qrImageSrc;
                        } catch (e) {
                            logDebug('二维码数据验证出错', e.message);
                        }
                    }
                    
                    qrcodeImg.innerHTML = `<img src="${qrImageSrc}" alt="二维码" onerror="this.onerror=null; this.src='data:image/svg+xml;utf8,<svg xmlns=\\'http://www.w3.org/2000/svg\\' width=\\'200\\' height=\\'200\\'><text x=\\'10\\' y=\\'20\\' fill=\\'red\\'>二维码加载失败</text></svg>';">`;
                    expiryTime = data.data.expired_time || 60;
                    countdownEl.textContent = expiryTime;
                    
                    // 开始倒计时
                    startCountdown();
                    
                    // 开始状态检查
                    startStatusCheck();
                    
                    statusEl.textContent = '请使用微信扫描二维码登录';
                } else {
                    throw new Error(data.msg || '获取二维码失败');
                }
            } catch (error) {
                console.error('获取二维码失败:', error);
                statusEl.textContent = `获取二维码失败: ${error.message}`;
                qrcodeImg.innerHTML = '<div style="color: #ff3b30;">获取二维码失败</div>';
                refreshBtn.style.display = 'block';
                confirmBtn.style.display = 'none';
                
                if (debugMode) {
                    logDebug('获取二维码出错', error.stack || error.message);
                }
            }
        }
        
        // 开始倒计时
        function startCountdown() {
            clearInterval(countdownInterval);
            
            countdownInterval = setInterval(() => {
                expiryTime--;
                countdownEl.textContent = expiryTime;
                
                if (expiryTime <= 0) {
                    clearInterval(countdownInterval);
                    qrcodeExpired.style.display = 'flex';
                    refreshBtn.style.display = 'block';
                    confirmBtn.style.display = 'none';
                    statusEl.textContent = '二维码已过期，请刷新';
                }
            }, 1000);
        }
        
        // 开始状态检查
        function startStatusCheck() {
            clearInterval(statusCheckInterval);
            
            // 立即执行一次
            checkLoginStatus();
            
            // 每5秒检查一次
            statusCheckInterval = setInterval(checkLoginStatus, 5000);
        }
        
        // 检查登录状态
        async function checkLoginStatus() {
            if (loginStatus === 'online') {
                clearInterval(statusCheckInterval);
                return;
            }
            
            try {
                const response = await fetch(`${serverUrl}/api/check_login_status`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({ 
                        token,
                        deviceInfo
                    })
                });
                
                const data = await response.json();
                console.log('检查登录状态响应:', data);
                
                if (debugMode) {
                    logDebug('检查登录状态响应', data);
                }
                
                if (data.ret === 200) {
                    // 更新状态
                    loginStatus = data.status;
                    
                    if (data.status === 'online') {
                        // 登录成功
                        clearInterval(countdownInterval);
                        clearInterval(statusCheckInterval);
                        statusEl.textContent = `登录成功 (${data.nickname || '未知用户'})`;
                        qrcodeImg.innerHTML = '<div style="color: #09bb07; font-size: 18px;">✓ 登录成功</div>';
                        confirmBtn.style.display = 'none';
                        timerEl.style.display = 'none';
                        subHeadingEl.textContent = '微信登录成功';
                    } else if (data.status === 'waiting' || data.status === 'pending') {
                        // 等待登录确认
                        statusEl.textContent = '等待确认登录';
                        // 更新倒计时时间
                        if (data.expired_time && data.expired_time !== expiryTime) {
                            expiryTime = data.expired_time;
                            countdownEl.textContent = expiryTime;
                        }
                    }
                } else if (data.ret === 201) {
                    // 二维码已刷新，更新二维码
                    if (!data.data || !data.data.qr_image) {
                        throw new Error('服务器未返回新的二维码数据');
                    }
                    
                    // 确保二维码数据不包含data:image前缀
                    let qrImageSrc = data.data.qr_image;
                    if (!qrImageSrc.startsWith('data:')) {
                        qrImageSrc = `data:image/png;base64,${qrImageSrc}`;
                    }
                    
                    qrcodeImg.innerHTML = `<img src="${qrImageSrc}" alt="二维码">`;
                    expiryTime = data.data.expired_time || 60;
                    countdownEl.textContent = expiryTime;
                    qrcodeExpired.style.display = 'none';
                    confirmBtn.style.display = 'block';
                    refreshBtn.style.display = 'none';
                    statusEl.textContent = '二维码已更新，请重新扫描';
                    
                    // 重新开始倒计时
                    startCountdown();
                } else if (data.ret === 202 && data.status === 'need_captcha') {
                    // 需要输入验证码
                    loginStatus = 'need_captcha';
                    statusEl.textContent = '请输入手机验证码';
                } else if (data.ret === 403 && data.status === 'need_captcha') {
                    // 需要输入验证码
                    loginStatus = 'need_captcha';
                    statusEl.textContent = '请输入手机验证码';
                } else {
                    throw new Error(data.msg || '检查登录状态失败');
                }
            } catch (error) {
                console.error('检查登录状态失败:', error);
                statusEl.textContent = `检查登录状态失败: ${error.message}`;
            }
        }
        
        // 确认登录
        async function confirmLogin() {
            try {
                statusEl.textContent = '正在确认登录...';
                confirmBtn.disabled = true;
                
                // 获取验证码输入
                const captchaValue = captchaInput.value.trim();
                
                // 准备请求数据
                const requestData = { 
                    token,
                    deviceInfo
                };
                
                // 如果有验证码则添加到请求中
                if (captchaValue) {
                    requestData.captcha = captchaValue;
                }
                
                if (debugMode) {
                    logDebug('确认登录请求', requestData);
                }
                
                const response = await fetch(`${serverUrl}/api/confirm_login`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(requestData)
                });
                
                const data = await response.json();
                
                if (debugMode) {
                    logDebug('确认登录响应', data);
                }
                
                if (data.ret === 200 && data.status === 'online') {
                    // 登录成功
                    clearInterval(countdownInterval);
                    clearInterval(statusCheckInterval);
                    statusEl.textContent = `登录成功 (${data.nickname})`;
                    qrcodeImg.innerHTML = '<div style="color: #09bb07; font-size: 18px;">✓ 登录成功</div>';
                    confirmBtn.style.display = 'none';
                    timerEl.style.display = 'none';
                    subHeadingEl.textContent = '微信登录成功';
                    loginStatus = 'online';
                } else if (data.ret === 202) {
                    // 等待确认登录
                    statusEl.textContent = '等待确认登录，请稍候...';
                    setTimeout(() => {
                        confirmBtn.disabled = false;
                    }, 3000);
                } else if (data.ret === 403 && data.msg.includes('验证码')) {
                    // 需要验证码或验证码错误
                    statusEl.textContent = data.msg;
                    confirmBtn.disabled = false;
                    loginStatus = 'need_captcha';
                    captchaInput.focus();
                } else if (data.ret === 201) {
                    // 二维码已刷新，更新二维码
                    if (data.data && data.data.qr_image) {
                        // 确保二维码数据不包含data:image前缀
                        let qrImageSrc = data.data.qr_image;
                        if (!qrImageSrc.startsWith('data:')) {
                            qrImageSrc = `data:image/png;base64,${qrImageSrc}`;
                        }
                        
                        qrcodeImg.innerHTML = `<img src="${qrImageSrc}" alt="二维码">`;
                        expiryTime = data.data.expired_time || 60;
                        countdownEl.textContent = expiryTime;
                        qrcodeExpired.style.display = 'none';
                        confirmBtn.disabled = false;
                        refreshBtn.style.display = 'none';
                        statusEl.textContent = '二维码已更新，请重新扫描';
                        
                        // 重新开始倒计时
                        startCountdown();
                    } else {
                        throw new Error('服务器未返回新的二维码数据');
                    }
                } else {
                    throw new Error(data.msg || '确认登录失败');
                }
            } catch (error) {
                console.error('确认登录失败:', error);
                statusEl.textContent = `确认登录失败: ${error.message}`;
                confirmBtn.disabled = false;
            }
        }
    </script>
</body>
</html> 