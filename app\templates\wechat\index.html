{% extends "base.html" %}

{% block title %}微信登录管理 - YBA监控管理系统{% endblock %}

{% block page_title %}
微信登录管理
{% if api_version %}
<span class="badge {% if api_version == 'v3' %}bg-warning{% elif api_version == 'v2' %}bg-success{% else %}bg-info{% endif %} ms-2">
    {% if api_version == 'v3' %}V3 API (GeWeChat){% elif api_version == 'v2' %}V2 API (WXID认证){% else %}V1 API (Token认证){% endif %}
</span>
{% endif %}
{% endblock %}

{% block content %}
<div class="wechat-container">
    {% if api_error %}
    <!-- API配置错误 -->
    <div class="alert alert-danger" role="alert">
        <h5><i class="bi bi-exclamation-triangle me-2"></i>配置错误</h5>
        <p>微信API配置存在问题，请联系管理员检查以下配置：</p>
        <ul class="mb-0">
            <li>API版本: {{ debug_info.api_version or 'v1' }}</li>
            <li>微信API基础URL: {{ debug_info.base_url or '未设置' }}</li>
            {% if debug_info.api_version == 'v3' %}
            <li>Token: {{ debug_info.token or '未设置' }}</li>
            <li>应用ID (App ID): {{ debug_info.app_id or '未设置' }}</li>
            {% elif debug_info.api_version == 'v2' %}
            <li>微信ID (WXID): {{ debug_info.wxid or '未设置' }}</li>
            {% else %}
            <li>API密钥: {{ debug_info.api_key_provided or '未设置' }}</li>
            {% endif %}
            <li>代理设置: {{ debug_info.proxy or '无' }}</li>
            {% if debug_info.error %}
            <li>错误信息: {{ debug_info.error }}</li>
            {% endif %}
        </ul>
    </div>
    {% else %}
    <!-- 正常显示 -->
    <div class="row g-4">
        <!-- 登录状态卡片 -->
        <div class="col-xl-8">
            <div class="card status-card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="bi bi-wechat me-2"></i>微信登录状态
                        {% if api_version %}
                        <span class="badge {% if api_version == 'v3' %}bg-warning{% elif api_version == 'v2' %}bg-success{% else %}bg-info{% endif %} ms-2">
                            {% if api_version == 'v3' %}V3 API{% elif api_version == 'v2' %}V2 API{% else %}V1 API{% endif %}
                        </span>
                        {% endif %}
                    </h5>
                </div>
                <div class="card-body">
                    {% if login_status and login_status.Code == 200 and login_status.Data.loginState == 1 %}
                    <!-- 已登录状态 -->
                    <div class="status-online">
                        <!-- 头像和基本状态 -->
                        <div class="status-header">
                            <div class="avatar-section">
                                <div class="user-avatar">
                                    {% if user_info and user_info.Data and user_info.Data.head_img %}
                                    <img src="{{ user_info.Data.head_img }}" alt="微信头像" class="avatar-img">
                                    {% else %}
                                    <div class="avatar-placeholder">
                                        <i class="bi bi-person-circle"></i>
                                    </div>
                                    {% endif %}
                                </div>
                                <div class="status-indicator">
                                    <div class="status-icon bg-success">
                                        <i class="bi bi-check-circle"></i>
                                    </div>
                                    <div class="status-content">
                                        <h4 class="status-title text-success">微信已登录</h4>
                                        <p class="status-subtitle">{{ detailed_status.loginErrMsg or '微信账号正常在线' }}</p>
                                        {% if detailed_status.loginTime %}
                                        <small class="text-muted">登录时间: {{ detailed_status.loginTime }}</small>
                                        {% endif %}
                                    </div>
                                </div>
                            </div>
                        </div>

                        {% if user_info and user_info.Code == 200 and user_info.Data %}
                        <!-- 用户基本信息 -->
                        <div class="user-info-section mt-4">
                            <h6 class="section-title">
                                <i class="bi bi-person me-2"></i>账号信息
                                {% if api_version %}
                                <span class="badge {% if api_version == 'v2' %}bg-success{% else %}bg-info{% endif %} ms-2">
                                    {% if api_version == 'v2' %}V2 API{% else %}V1 API{% endif %}
                                </span>
                                {% endif %}
                            </h6>
                            <div class="user-info-grid">
                                <div class="info-item">
                                    <label><i class="bi bi-person-badge me-1"></i>微信号</label>
                                    <span>{{ user_info.Data.wxid or '未知' }}</span>
                                </div>
                                <div class="info-item">
                                    <label><i class="bi bi-person me-1"></i>昵称</label>
                                    <span>{{ user_info.Data.nick_name or user_info.Data.nickname or '未知' }}</span>
                                </div>
                                {% if api_version == 'v1' %}
                                <div class="info-item">
                                    <label><i class="bi bi-phone me-1"></i>手机号</label>
                                    <span>{{ user_info.Data.mobile or '未绑定' }}</span>
                                </div>
                                {% endif %}
                                <div class="info-item">
                                    <label><i class="bi bi-gear me-1"></i>API版本</label>
                                    <span class="badge {% if api_version == 'v2' %}bg-success{% else %}bg-info{% endif %}">
                                        {% if api_version == 'v2' %}V2 (WXID认证){% else %}V1 (Token认证){% endif %}
                                    </span>
                                </div>
                                <div class="info-item">
                                    <label><i class="bi bi-circle-fill text-success me-1"></i>登录状态</label>
                                    <span class="badge bg-success">在线</span>
                                </div>
                            </div>
                        </div>
                        {% endif %}

                        {% if detailed_status %}
                        <!-- 在线状态详情 -->
                        <div class="online-status-section mt-4">
                            <h6 class="section-title">
                                <i class="bi bi-clock me-2"></i>在线状态详情
                            </h6>
                            <div class="status-details-grid">
                                {% if detailed_status.onlineTime %}
                                <div class="status-detail-item">
                                    <div class="detail-icon bg-primary">
                                        <i class="bi bi-clock"></i>
                                    </div>
                                    <div class="detail-content">
                                        <label>本次在线</label>
                                        <span>{{ detailed_status.onlineTime }}</span>
                                    </div>
                                </div>
                                {% endif %}

                                {% if detailed_status.totalOnline %}
                                <div class="status-detail-item">
                                    <div class="detail-icon bg-info">
                                        <i class="bi bi-calendar-check"></i>
                                    </div>
                                    <div class="detail-content">
                                        <label>总计在线</label>
                                        <span>{{ detailed_status.totalOnline }}</span>
                                    </div>
                                </div>
                                {% endif %}

                                {% if detailed_status.expiryTime %}
                                <div class="status-detail-item">
                                    <div class="detail-icon bg-warning">
                                        <i class="bi bi-calendar-x"></i>
                                    </div>
                                    <div class="detail-content">
                                        <label>到期时间</label>
                                        <span>{{ detailed_status.expiryTime }}</span>
                                    </div>
                                </div>
                                {% endif %}

                                {% if detailed_status.onlineDays is defined %}
                                <div class="status-detail-item">
                                    <div class="detail-icon bg-success">
                                        <i class="bi bi-calendar3"></i>
                                    </div>
                                    <div class="detail-content">
                                        <label>在线天数</label>
                                        <span>{{ detailed_status.onlineDays }} 天</span>
                                    </div>
                                </div>
                                {% endif %}
                            </div>
                        </div>

                        <!-- 自动刷新令牌信息 -->
                        {% if detailed_status.autoRefreshToken %}
                        <div class="auto-refresh-section mt-4">
                            <h6 class="section-title">
                                <i class="bi bi-arrow-clockwise me-2"></i>自动刷新令牌
                            </h6>
                            <div class="refresh-info-card">
                                <div class="refresh-status">
                                    <div class="refresh-indicator">
                                        {% if detailed_status.autoRefreshToken.auto_refresh_enabled %}
                                        <span class="badge bg-success">
                                            <i class="bi bi-check-circle me-1"></i>已启用
                                        </span>
                                        {% else %}
                                        <span class="badge bg-secondary">
                                            <i class="bi bi-x-circle me-1"></i>已禁用
                                        </span>
                                        {% endif %}

                                        {% if detailed_status.autoRefreshToken.needs_refresh %}
                                        <span class="badge bg-warning ms-2">
                                            <i class="bi bi-exclamation-triangle me-1"></i>需要刷新
                                        </span>
                                        {% endif %}
                                    </div>
                                </div>

                                <div class="refresh-details mt-3">
                                    <div class="row">
                                        {% if detailed_status.autoRefreshToken.last_refresh_time %}
                                        <div class="col-md-6">
                                            <div class="refresh-detail">
                                                <label><i class="bi bi-clock-history me-1"></i>上次刷新</label>
                                                <span>{{ detailed_status.autoRefreshToken.last_refresh_time | timestamp_to_datetime }}</span>
                                            </div>
                                        </div>
                                        {% endif %}

                                        {% if detailed_status.autoRefreshToken.minutes_until_next_refresh %}
                                        <div class="col-md-6">
                                            <div class="refresh-detail">
                                                <label><i class="bi bi-clock me-1"></i>下次刷新</label>
                                                <span class="text-primary">{{ detailed_status.autoRefreshToken.display_time or detailed_status.autoRefreshToken.minutes_until_next_refresh + '分钟' }}后</span>
                                            </div>
                                        </div>
                                        {% endif %}
                                    </div>

                                    {% if detailed_status.autoRefreshToken.time_since_last_refresh %}
                                    <div class="refresh-detail mt-2">
                                        <label><i class="bi bi-stopwatch me-1"></i>距离上次刷新</label>
                                        <span>{{ detailed_status.autoRefreshToken.time_since_last_refresh }} 秒</span>
                                    </div>
                                    {% endif %}
                                </div>
                            </div>
                        </div>
                        {% endif %}

                        <!-- 网络信息 -->
                        {% if detailed_status.proxyUrl or detailed_status.targetIp %}
                        <div class="network-info-section mt-4">
                            <h6 class="section-title">
                                <i class="bi bi-globe me-2"></i>网络信息
                            </h6>
                            <div class="network-info-grid">
                                {% if detailed_status.proxyUrl %}
                                <div class="network-item">
                                    <label><i class="bi bi-shield-check me-1"></i>代理地址</label>
                                    <span class="text-break">{{ detailed_status.proxyUrl }}</span>
                                </div>
                                {% endif %}

                                {% if detailed_status.targetIp %}
                                <div class="network-item">
                                    <label><i class="bi bi-hdd-network me-1"></i>目标服务器</label>
                                    <span>{{ detailed_status.targetIp }}</span>
                                </div>
                                {% endif %}
                            </div>
                        </div>
                        {% endif %}

                        <!-- 登录日志 -->
                        {% if detailed_status.loginJournal and detailed_status.loginJournal.logs %}
                        <div class="login-journal-section mt-4">
                            <h6 class="section-title">
                                <i class="bi bi-journal-text me-2"></i>登录日志
                                <span class="badge bg-secondary ms-2">{{ detailed_status.loginJournal.count }}</span>
                            </h6>
                            <div class="journal-logs">
                                {% for log in detailed_status.loginJournal.logs %}
                                <div class="log-entry">
                                    <div class="log-time">{{ log.time or log.timestamp }}</div>
                                    <div class="log-message">{{ log.message or log.msg }}</div>
                                </div>
                                {% endfor %}
                            </div>
                        </div>
                        {% endif %}
                        {% endif %}

                        <!-- 操作按钮 -->
                        <div class="action-buttons mt-4">
                            <a href="{{ url_for('wechat.logout') }}" class="btn btn-outline-danger"
                               onclick="return confirm('确定要退出微信登录吗？')">
                                <i class="bi bi-box-arrow-right me-2"></i>退出登录
                            </a>
                            <button class="btn btn-outline-primary" onclick="refreshStatus()">
                                <i class="bi bi-arrow-clockwise me-2"></i>刷新状态
                            </button>
                        </div>
                    </div>
                    {% else %}
                    <!-- 未登录状态 -->
                    <div class="status-offline">
                        <div class="status-indicator">
                            <div class="status-icon bg-secondary">
                                <i class="bi bi-x-circle"></i>
                            </div>
                            <div class="status-content">
                                <h4 class="status-title text-secondary">微信未登录</h4>
                                <p class="status-subtitle">请扫描二维码登录微信</p>
                            </div>
                        </div>
                        
                        <!-- 登录信息 -->
                        <div class="login-info mt-4">
                            <div class="info-card">
                                <h6><i class="bi bi-info-circle me-2"></i>登录说明</h6>
                                <ul class="info-list">
                                    <li>点击"获取二维码"按钮生成登录二维码</li>
                                    <li>使用微信扫描二维码</li>
                                    <li>在手机上确认登录</li>
                                    <li>登录成功后页面会自动刷新</li>
                                </ul>
                            </div>
                        </div>
                        
                        <!-- 操作按钮 -->
                        <div class="action-buttons mt-4">
                            {% if debug_info.api_version == 'v3' %}
                            <div class="btn-group" role="group">
                                <a href="{{ url_for('wechat.qrcode') }}" class="btn btn-primary">
                                    <i class="bi bi-qr-code me-2"></i>扫码登录
                                </a>
                                <a href="{{ url_for('wechat.v3_login') }}" class="btn btn-warning">
                                    <i class="bi bi-gear me-2"></i>V3管理
                                </a>
                            </div>
                            {% elif debug_info.api_version == 'v1' %}
                            <a href="{{ url_for('wechat.qrcode') }}" class="btn btn-primary">
                                <i class="bi bi-qr-code me-2"></i>获取二维码
                            </a>
                            {% elif debug_info.api_version == 'v2' %}
                            <div class="btn-group" role="group">
                                <a href="{{ url_for('wechat.qrcode') }}" class="btn btn-primary">
                                    <i class="bi bi-qr-code me-2"></i>标准登录
                                </a>
                                <a href="{{ url_for('wechat.v2_car_qr_login') }}" class="btn btn-warning">
                                    <i class="bi bi-car-front me-2"></i>车载登录
                                </a>
                            </div>
                            {% else %}
                            <a href="{{ url_for('wechat.qrcode') }}" class="btn btn-primary">
                                <i class="bi bi-qr-code me-2"></i>获取二维码
                            </a>
                            {% endif %}
                            <button class="btn btn-outline-primary" onclick="refreshStatus()">
                                <i class="bi bi-arrow-clockwise me-2"></i>刷新状态
                            </button>
                        </div>
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>
        
        <!-- 快捷操作 -->
        <div class="col-xl-4">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="bi bi-lightning me-2"></i>快捷操作
                    </h5>
                </div>
                <div class="card-body">
                    <div class="quick-actions">
                        <!-- V1 API 扫码登录 -->
                        {% if debug_info.api_version == 'v1' %}
                        <a href="{{ url_for('wechat.qrcode') }}" class="action-item">
                            <div class="action-icon bg-primary">
                                <i class="bi bi-qr-code"></i>
                            </div>
                            <div class="action-content">
                                <h6>扫码登录</h6>
                                <p>V1 API 二维码登录</p>
                            </div>
                        </a>
                        {% endif %}

                        <!-- V2 API 扫码登录选项 -->
                        {% if debug_info.api_version == 'v2' %}
                        <a href="{{ url_for('wechat.qrcode') }}" class="action-item">
                            <div class="action-icon bg-primary">
                                <i class="bi bi-qr-code"></i>
                            </div>
                            <div class="action-content">
                                <h6>标准扫码登录</h6>
                                <p>V2 API 标准二维码</p>
                            </div>
                        </a>

                        <a href="{{ url_for('wechat.v2_car_qr_login') }}" class="action-item">
                            <div class="action-icon bg-warning">
                                <i class="bi bi-car-front"></i>
                            </div>
                            <div class="action-content">
                                <h6>车载扫码登录</h6>
                                <p>V2 API 车载模式</p>
                            </div>
                        </a>
                        {% endif %}

                        <a href="{{ url_for('vip.index') }}" class="action-item">
                            <div class="action-icon bg-success">
                                <i class="bi bi-credit-card-2-front"></i>
                            </div>
                            <div class="action-content">
                                <h6>VIP管理</h6>
                                <p>管理会员卡</p>
                            </div>
                        </a>

                        <a href="{{ url_for('auth.dashboard') }}" class="action-item">
                            <div class="action-icon bg-info">
                                <i class="bi bi-speedometer2"></i>
                            </div>
                            <div class="action-content">
                                <h6>返回仪表盘</h6>
                                <p>查看系统概览</p>
                            </div>
                        </a>
                    </div>
                </div>
            </div>
            
            <!-- 系统信息 -->
            <div class="card mt-4">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="bi bi-gear me-2"></i>系统信息
                    </h5>
                </div>
                <div class="card-body">
                    <div class="system-info">
                        <div class="info-row">
                            <span class="label">用户名称</span>
                            <span class="value">{{ debug_info.name }}</span>
                        </div>
                        <div class="info-row">
                            <span class="label">API地址</span>
                            <span class="value">{{ debug_info.base_url[:30] }}...</span>
                        </div>
                        <div class="info-row">
                            <span class="label">代理设置</span>
                            <span class="value">{{ debug_info.proxy or '无' }}</span>
                        </div>
                        <div class="info-row">
                            <span class="label">权限级别</span>
                            <span class="value">
                                {% if debug_info.is_admin %}
                                <span class="badge bg-danger">管理员</span>
                                {% else %}
                                <span class="badge bg-primary">代理商</span>
                                {% endif %}
                            </span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    {% endif %}
</div>

<style>
.wechat-container {
    animation: fadeInUp 0.6s ease-out;
}

.status-card {
    border: none;
    box-shadow: var(--shadow-lg);
}

/* 头像和状态头部样式 */
.status-header {
    margin-bottom: 1.5rem;
}

.avatar-section {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.user-avatar {
    flex-shrink: 0;
}

.avatar-img {
    width: 80px;
    height: 80px;
    border-radius: 50%;
    object-fit: cover;
    border: 3px solid #28a745;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.avatar-placeholder {
    width: 80px;
    height: 80px;
    border-radius: 50%;
    background: linear-gradient(135deg, #6c757d, #495057);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 2.5rem;
    border: 3px solid #6c757d;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.status-indicator {
    display: flex;
    flex: 1;
    align-items: center;
    gap: 1.5rem;
    padding: 1.5rem;
    background: rgba(99, 102, 241, 0.05);
    border-radius: var(--border-radius);
    border: 1px solid rgba(99, 102, 241, 0.1);
}

.status-icon {
    width: 80px;
    height: 80px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 2rem;
    color: white;
    flex-shrink: 0;
}

.status-title {
    font-size: 1.5rem;
    font-weight: 700;
    margin: 0 0 0.5rem;
}

.status-subtitle {
    color: var(--text-secondary);
    margin: 0;
}

.user-info-section {
    background: var(--bg-tertiary);
    border-radius: var(--border-radius);
    padding: 1.5rem;
}

.section-title {
    color: var(--text-primary);
    font-weight: 600;
    margin-bottom: 1rem;
    font-size: 1rem;
}

.user-info-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1rem;
}

.info-item {
    display: flex;
    flex-direction: column;
    gap: 0.25rem;
}

.info-item label {
    font-size: 0.85rem;
    color: var(--text-secondary);
    font-weight: 500;
}

.info-item span {
    color: var(--text-primary);
    font-weight: 600;
}

/* 在线状态详情样式 */
.online-status-section {
    background: linear-gradient(135deg, #f8f9fa, #e9ecef);
    border-radius: 12px;
    padding: 1.5rem;
    border: 1px solid #dee2e6;
    margin-top: 1rem;
}

.status-details-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1rem;
    margin-top: 1rem;
}

.status-detail-item {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    padding: 1rem;
    background: white;
    border-radius: 8px;
    border: 1px solid #e9ecef;
    transition: all 0.3s ease;
}

.status-detail-item:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.detail-icon {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1.1rem;
    flex-shrink: 0;
}

.detail-content {
    flex: 1;
}

.detail-content label {
    display: block;
    font-size: 0.875rem;
    color: #6c757d;
    margin-bottom: 0.25rem;
    font-weight: 500;
}

.detail-content span {
    font-size: 1rem;
    font-weight: 600;
    color: #212529;
}

/* 自动刷新令牌样式 */
.auto-refresh-section {
    background: linear-gradient(135deg, #e3f2fd, #f3e5f5);
    border-radius: 12px;
    padding: 1.5rem;
    border: 1px solid #bbdefb;
    margin-top: 1rem;
}

.refresh-info-card {
    background: white;
    border-radius: 8px;
    padding: 1rem;
    border: 1px solid #e1f5fe;
}

.refresh-status {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 1rem;
}

.refresh-indicator {
    display: flex;
    gap: 0.5rem;
}

.refresh-details .row {
    margin: 0;
}

.refresh-detail {
    display: flex;
    flex-direction: column;
    gap: 0.25rem;
    margin-bottom: 0.75rem;
}

.refresh-detail label {
    font-size: 0.875rem;
    color: #6c757d;
    font-weight: 500;
}

.refresh-detail span {
    font-weight: 600;
    color: #212529;
}

/* 网络信息样式 */
.network-info-section {
    background: linear-gradient(135deg, #fff3e0, #fce4ec);
    border-radius: 12px;
    padding: 1.5rem;
    border: 1px solid #ffcc02;
    margin-top: 1rem;
}

.network-info-grid {
    display: grid;
    gap: 1rem;
    margin-top: 1rem;
}

.network-item {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
    padding: 1rem;
    background: white;
    border-radius: 8px;
    border: 1px solid #ffe0b2;
}

.network-item label {
    font-size: 0.875rem;
    color: #6c757d;
    font-weight: 500;
}

.network-item span {
    font-weight: 600;
    color: #212529;
    word-break: break-all;
}

/* 登录日志样式 */
.login-journal-section {
    background: linear-gradient(135deg, #f1f8e9, #e8f5e8);
    border-radius: 12px;
    padding: 1.5rem;
    border: 1px solid #c8e6c9;
    margin-top: 1rem;
}

.journal-logs {
    margin-top: 1rem;
    max-height: 200px;
    overflow-y: auto;
}

.log-entry {
    display: flex;
    gap: 1rem;
    padding: 0.75rem;
    background: white;
    border-radius: 6px;
    border: 1px solid #e8f5e8;
    margin-bottom: 0.5rem;
}

.log-time {
    font-size: 0.875rem;
    color: #6c757d;
    font-weight: 500;
    white-space: nowrap;
    min-width: 120px;
}

.log-message {
    flex: 1;
    font-size: 0.875rem;
    color: #212529;
}

.login-info .info-card {
    background: rgba(59, 130, 246, 0.1);
    border: 1px solid rgba(59, 130, 246, 0.2);
    border-radius: var(--border-radius);
    padding: 1.5rem;
}

.info-list {
    margin: 0.75rem 0 0;
    padding-left: 1.25rem;
    color: var(--text-secondary);
}

.info-list li {
    margin-bottom: 0.5rem;
}

.action-buttons {
    display: flex;
    gap: 1rem;
    flex-wrap: wrap;
}

.quick-actions {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.action-item {
    display: flex;
    align-items: center;
    gap: 1rem;
    padding: 1rem;
    background: var(--bg-tertiary);
    border-radius: var(--border-radius);
    text-decoration: none;
    color: inherit;
    transition: var(--transition);
    border: 1px solid var(--border-color);
}

.action-item:hover {
    background: rgba(99, 102, 241, 0.1);
    border-color: var(--primary-color);
    color: inherit;
    text-decoration: none;
    transform: translateY(-2px);
}

.action-icon {
    width: 50px;
    height: 50px;
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.25rem;
    color: white;
    flex-shrink: 0;
}

.action-content h6 {
    margin: 0 0 0.25rem;
    font-weight: 600;
    color: var(--text-primary);
}

.action-content p {
    margin: 0;
    font-size: 0.85rem;
    color: var(--text-secondary);
}

.system-info {
    display: flex;
    flex-direction: column;
    gap: 0.75rem;
}

.info-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.5rem 0;
    border-bottom: 1px solid var(--border-color);
}

.info-row:last-child {
    border-bottom: none;
}

.info-row .label {
    font-size: 0.9rem;
    color: var(--text-secondary);
    font-weight: 500;
}

.info-row .value {
    font-size: 0.9rem;
    color: var(--text-primary);
    font-weight: 600;
    text-align: right;
}

/* 登录成功通知样式 */
.login-success-notification {
    position: fixed;
    top: 20px;
    right: 20px;
    background: linear-gradient(135deg, #d4edda, #c3e6cb);
    border: 1px solid #28a745;
    border-radius: 12px;
    padding: 1.5rem;
    box-shadow: 0 8px 25px rgba(40, 167, 69, 0.3);
    z-index: 9999;
    min-width: 300px;
    max-width: 400px;
    transform: translateX(100%);
    opacity: 0;
    transition: all 0.3s ease;
}

.login-success-notification.show {
    transform: translateX(0);
    opacity: 1;
}

.notification-content {
    display: flex;
    align-items: flex-start;
    gap: 1rem;
    margin-bottom: 1rem;
}

.notification-icon i {
    font-size: 2rem;
    color: #28a745;
}

.notification-text h5 {
    margin: 0 0 0.5rem 0;
    color: #155724;
    font-weight: 600;
}

.notification-text p {
    margin: 0;
    color: #155724;
    font-size: 0.9rem;
}

.notification-progress .progress-bar {
    width: 100%;
    height: 4px;
    background: rgba(40, 167, 69, 0.2);
    border-radius: 2px;
    overflow: hidden;
}

.notification-progress .progress-fill {
    height: 100%;
    background: #28a745;
    border-radius: 2px;
    width: 0%;
    transition: width 2s ease;
}
</style>

<script>
function refreshStatus() {
    showLoading('正在刷新状态...');
    window.location.reload();
}

// 检查是否从二维码登录跳转过来
function checkLoginRedirect() {
    const urlParams = new URLSearchParams(window.location.search);
    const fromQrcode = urlParams.get('from') === 'qrcode';
    const refresh = urlParams.get('refresh') === '1';
    const loginSuccess = sessionStorage.getItem('wechat_login_success') === 'true';
    const loginTimestamp = sessionStorage.getItem('login_timestamp');

    if (fromQrcode && refresh && loginSuccess) {
        // 检查登录时间戳，确保是最近的登录
        const now = Date.now();
        const loginTime = parseInt(loginTimestamp) || 0;
        const timeDiff = now - loginTime;

        if (timeDiff < 60000) { // 1分钟内的登录
            // 显示登录成功提示
            showLoginSuccessNotification();

            // 清除会话标记
            sessionStorage.removeItem('wechat_login_success');
            sessionStorage.removeItem('login_timestamp');

            // 清理URL参数
            const newUrl = window.location.pathname;
            window.history.replaceState({}, document.title, newUrl);

            // 自动刷新状态
            setTimeout(() => {
                refreshStatus();
            }, 2000);
        }
    }
}

// 显示登录成功通知
function showLoginSuccessNotification() {
    // 创建成功通知
    const notification = $(`
        <div class="login-success-notification">
            <div class="notification-content">
                <div class="notification-icon">
                    <i class="bi bi-check-circle-fill text-success"></i>
                </div>
                <div class="notification-text">
                    <h5>登录成功！</h5>
                    <p>微信账号已成功登录，正在刷新状态...</p>
                </div>
            </div>
            <div class="notification-progress">
                <div class="progress-bar">
                    <div class="progress-fill"></div>
                </div>
            </div>
        </div>
    `);

    // 添加到页面
    $('body').append(notification);

    // 显示动画
    setTimeout(() => {
        notification.addClass('show');
    }, 100);

    // 进度条动画
    setTimeout(() => {
        notification.find('.progress-fill').css('width', '100%');
    }, 500);

    // 自动隐藏
    setTimeout(() => {
        notification.removeClass('show');
        setTimeout(() => {
            notification.remove();
        }, 300);
    }, 3000);
}

// 页面加载完成后的动画和检查登录跳转
$(document).ready(function() {
    // 检查登录跳转
    checkLoginRedirect();

    // 页面动画
    $('.status-card, .card').each(function(index) {
        $(this).css({
            'opacity': '0',
            'transform': 'translateY(20px)'
        }).delay(index * 100).animate({
            'opacity': '1'
        }, 500).css('transform', 'translateY(0)');
    });
});
</script>
{% endblock %}
