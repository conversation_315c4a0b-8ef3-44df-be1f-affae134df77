# Ubuntu服务快速启动指南

## 一键部署

```bash
# 1. 给脚本执行权限
chmod +x deploy_service.sh manage_service.sh

# 2. 运行部署脚本
sudo ./deploy_service.sh

# 3. 配置环境变量（如果需要）
sudo nano /opt/wechat-bot/.env

# 4. 重启服务
sudo systemctl restart wechat-bot
```

## 环境变量配置模板

创建 `/opt/wechat-bot/.env` 文件：

```env
# Flask配置
SECRET_KEY=your-secret-key-change-this
FLASK_HOST=0.0.0.0
FLASK_PORT=5000
FLASK_DEBUG=False
FLASK_ENV=production

# 数据库配置
MYSQL_HOST=localhost
MYSQL_PORT=3306
MYSQL_USER=your_db_user
MYSQL_PASSWORD=your_db_password
MYSQL_DB=your_db_name

# 管理员配置
ADMIN_TOKEN=your-admin-token

# 登录检查配置
ENABLE_PERIODIC_CHECK=true
LOGIN_CHECK_INTERVAL=300
```

## 常用命令

```bash
# 查看服务状态
./manage_service.sh status

# 查看实时日志
./manage_service.sh follow

# 重启服务
sudo ./manage_service.sh restart

# 检查配置
./manage_service.sh check
```

## 验证部署

1. 检查服务状态：`sudo systemctl status wechat-bot`
2. 访问Web界面：`http://your-server-ip:5000`
3. 查看日志：`sudo journalctl -u wechat-bot -f`

## 故障排除

- **服务启动失败**: 检查 `.env` 配置文件
- **数据库连接失败**: 验证数据库配置和权限
- **端口冲突**: 修改 `FLASK_PORT` 配置

详细说明请参考 `UBUNTU_SERVICE_DEPLOYMENT.md`
