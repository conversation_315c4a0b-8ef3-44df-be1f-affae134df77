{% extends "base.html" %}

{% block title %}VIP会员管理 - YBA监控管理系统{% endblock %}

{% block page_title %}VIP会员管理{% endblock %}

{% block content %}
<div class="vip-container">
    <!-- 统计卡片 -->
    <div class="stats-section mb-5">
        {% if is_admin %}
        <!-- 管理员统计 -->
        <div class="row g-4">
            <div class="col-xl-3 col-md-6">
                <div class="stat-card">
                    <div class="stat-icon bg-primary">
                        <i class="bi bi-credit-card-2-front"></i>
                    </div>
                    <div class="stat-content">
                        <h3 class="stat-number">{{ stats.total_cards or 0 }}</h3>
                        <p class="stat-label">VIP卡总数</p>
                    </div>
                </div>
            </div>
            
            <div class="col-xl-3 col-md-6">
                <div class="stat-card">
                    <div class="stat-icon bg-success">
                        <i class="bi bi-link-45deg"></i>
                    </div>
                    <div class="stat-content">
                        <h3 class="stat-number">{{ stats.bound_cards or 0 }}</h3>
                        <p class="stat-label">已绑定卡片</p>
                    </div>
                </div>
            </div>
            
            <div class="col-xl-3 col-md-6">
                <div class="stat-card">
                    <div class="stat-icon bg-warning">
                        <i class="bi bi-people"></i>
                    </div>
                    <div class="stat-content">
                        <h3 class="stat-number">{{ stats.active_agents or 0 }}</h3>
                        <p class="stat-label">活跃代理</p>
                    </div>
                </div>
            </div>
            
            <div class="col-xl-3 col-md-6">
                <div class="stat-card">
                    <div class="stat-icon bg-info">
                        <i class="bi bi-plus-circle"></i>
                    </div>
                    <div class="stat-content">
                        <h3 class="stat-number">{{ stats.today_cards or 0 }}</h3>
                        <p class="stat-label">今日新增</p>
                    </div>
                </div>
            </div>
        </div>
        {% else %}
        <!-- 代理商统计 -->
        <div class="row g-4">
            <div class="col-xl-3 col-md-6">
                <div class="stat-card">
                    <div class="stat-icon bg-primary">
                        <i class="bi bi-credit-card-2-front"></i>
                    </div>
                    <div class="stat-content">
                        <h3 class="stat-number">{{ stats.my_cards or 0 }}</h3>
                        <p class="stat-label">我的VIP卡</p>
                    </div>
                </div>
            </div>
            
            <div class="col-xl-3 col-md-6">
                <div class="stat-card">
                    <div class="stat-icon bg-success">
                        <i class="bi bi-link-45deg"></i>
                    </div>
                    <div class="stat-content">
                        <h3 class="stat-number">{{ stats.my_bound_cards or 0 }}</h3>
                        <p class="stat-label">已绑定卡片</p>
                    </div>
                </div>
            </div>
            
            <div class="col-xl-3 col-md-6">
                <div class="stat-card">
                    <div class="stat-icon bg-warning">
                        <i class="bi bi-gem"></i>
                    </div>
                    <div class="stat-content">
                        <h3 class="stat-number">{{ stats.total_points or 0 }}</h3>
                        <p class="stat-label">总积分</p>
                    </div>
                </div>
            </div>
            
            <div class="col-xl-3 col-md-6">
                <div class="stat-card">
                    <div class="stat-icon bg-info">
                        <i class="bi bi-plus-circle"></i>
                    </div>
                    <div class="stat-content">
                        <h3 class="stat-number">{{ stats.my_today_cards or 0 }}</h3>
                        <p class="stat-label">今日新增</p>
                    </div>
                </div>
            </div>
        </div>
        {% endif %}
    </div>

    <!-- 快捷操作 -->
    <div class="row g-4">
        <!-- 主要操作 -->
        <div class="col-xl-8">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="bi bi-lightning me-2"></i>快捷操作
                    </h5>
                </div>
                <div class="card-body">
                    <div class="quick-actions-grid">
                        <a href="{{ url_for('vip.create_card') }}" class="action-card">
                            <div class="action-icon bg-primary">
                                <i class="bi bi-plus-square"></i>
                            </div>
                            <div class="action-content">
                                <h6>创建VIP卡</h6>
                                <p>生成新的会员卡</p>
                            </div>
                        </a>
                        
                        <a href="{{ url_for('vip.cards') }}" class="action-card">
                            <div class="action-icon bg-success">
                                <i class="bi bi-list-ul"></i>
                            </div>
                            <div class="action-content">
                                <h6>VIP卡列表</h6>
                                <p>查看所有会员卡</p>
                            </div>
                        </a>
                        
                        <a href="#" class="action-card" onclick="showUserSearchModal()">
                            <div class="action-icon bg-warning">
                                <i class="bi bi-search"></i>
                            </div>
                            <div class="action-content">
                                <h6>用户查询</h6>
                                <p>查询用户的VIP卡</p>
                            </div>
                        </a>
                        
                        <a href="{{ url_for('vip.cards', status='bound') }}" class="action-card">
                            <div class="action-icon bg-info">
                                <i class="bi bi-link-45deg"></i>
                            </div>
                            <div class="action-content">
                                <h6>已绑定卡片</h6>
                                <p>查看已绑定的VIP卡</p>
                            </div>
                        </a>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- 侧边信息 -->
        <div class="col-xl-4">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="bi bi-info-circle me-2"></i>系统信息
                    </h5>
                </div>
                <div class="card-body">
                    <div class="system-info">
                        <div class="info-row">
                            <span class="label">代理分组</span>
                            <span class="value">{{ agent.group or '未设置' }}</span>
                        </div>
                        <div class="info-row">
                            <span class="label">代理名称</span>
                            <span class="value">{{ agent.name }}</span>
                        </div>
                        <div class="info-row">
                            <span class="label">VIP功能</span>
                            <span class="value">
                                {% if agent.vip_is_active %}
                                <span class="badge bg-success">已激活</span>
                                {% else %}
                                <span class="badge bg-secondary">未激活</span>
                                {% endif %}
                            </span>
                        </div>
                        <div class="info-row">
                            <span class="label">权限级别</span>
                            <span class="value">
                                {% if is_admin %}
                                <span class="badge bg-danger">管理员</span>
                                {% else %}
                                <span class="badge bg-primary">代理商</span>
                                {% endif %}
                            </span>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- 使用说明 -->
            <div class="card mt-4">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="bi bi-question-circle me-2"></i>使用说明
                    </h5>
                </div>
                <div class="card-body">
                    <div class="help-content">
                        <div class="help-item">
                            <h6><i class="bi bi-1-circle me-2"></i>创建VIP卡</h6>
                            <p>可以单张或批量创建VIP会员卡</p>
                        </div>
                        
                        <div class="help-item">
                            <h6><i class="bi bi-2-circle me-2"></i>绑定用户</h6>
                            <p>将VIP卡绑定到具体的用户ID</p>
                        </div>
                        
                        <div class="help-item">
                            <h6><i class="bi bi-3-circle me-2"></i>管理积分</h6>
                            <p>可以设置、增加或减少VIP卡积分</p>
                        </div>
                        
                        <div class="help-item">
                            <h6><i class="bi bi-4-circle me-2"></i>设置有效期</h6>
                            <p>为VIP卡设置或延长有效期</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 用户搜索模态框 -->
<div class="modal fade" id="userSearchModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">用户查询</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="userSearchForm">
                    <div class="mb-3">
                        <label for="searchUserId" class="form-label">用户ID</label>
                        <input type="text" class="form-control" id="searchUserId" placeholder="请输入用户ID" required>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                <button type="button" class="btn btn-primary" onclick="searchUser()">查询</button>
            </div>
        </div>
    </div>
</div>

<style>
.vip-container {
    animation: fadeInUp 0.6s ease-out;
}

.stat-card {
    background: var(--bg-secondary);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    padding: 1.5rem;
    display: flex;
    align-items: center;
    gap: 1rem;
    transition: var(--transition);
    box-shadow: var(--shadow-sm);
}

.stat-card:hover {
    transform: translateY(-4px);
    box-shadow: var(--shadow-lg);
}

.stat-icon {
    width: 60px;
    height: 60px;
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    color: white;
    flex-shrink: 0;
}

.stat-number {
    font-size: 2rem;
    font-weight: 700;
    margin: 0;
    color: var(--text-primary);
}

.stat-label {
    margin: 0;
    color: var(--text-secondary);
    font-size: 0.9rem;
}

.quick-actions-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1rem;
}

.action-card {
    display: flex;
    align-items: center;
    gap: 1rem;
    padding: 1.5rem;
    background: var(--bg-tertiary);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    text-decoration: none;
    color: inherit;
    transition: var(--transition);
}

.action-card:hover {
    background: rgba(99, 102, 241, 0.1);
    border-color: var(--primary-color);
    color: inherit;
    text-decoration: none;
    transform: translateY(-2px);
    box-shadow: var(--shadow-md);
}

.action-icon {
    width: 50px;
    height: 50px;
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.25rem;
    color: white;
    flex-shrink: 0;
}

.action-content h6 {
    margin: 0 0 0.25rem;
    font-weight: 600;
    color: var(--text-primary);
}

.action-content p {
    margin: 0;
    font-size: 0.85rem;
    color: var(--text-secondary);
}

.system-info {
    display: flex;
    flex-direction: column;
    gap: 0.75rem;
}

.info-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.5rem 0;
    border-bottom: 1px solid var(--border-color);
}

.info-row:last-child {
    border-bottom: none;
}

.info-row .label {
    font-size: 0.9rem;
    color: var(--text-secondary);
    font-weight: 500;
}

.info-row .value {
    font-size: 0.9rem;
    color: var(--text-primary);
    font-weight: 600;
    text-align: right;
}

.help-content {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.help-item h6 {
    margin: 0 0 0.25rem;
    color: var(--text-primary);
    font-weight: 600;
    font-size: 0.9rem;
}

.help-item p {
    margin: 0;
    color: var(--text-secondary);
    font-size: 0.8rem;
}
</style>

<script>
function showUserSearchModal() {
    const modal = new bootstrap.Modal(document.getElementById('userSearchModal'));
    modal.show();
}

function searchUser() {
    const userId = document.getElementById('searchUserId').value.trim();
    if (!userId) {
        showError('请输入用户ID');
        return;
    }
    
    // 跳转到用户查询页面
    window.location.href = `{{ url_for('vip.user_cards', user_id='USER_ID') }}`.replace('USER_ID', encodeURIComponent(userId));
}

// 统计数字动画
$(document).ready(function() {
    $('.stat-number').each(function() {
        const $this = $(this);
        const countTo = parseInt($this.text()) || 0;
        
        if (countTo > 0) {
            $({ countNum: 0 }).animate({
                countNum: countTo
            }, {
                duration: 2000,
                easing: 'swing',
                step: function() {
                    $this.text(Math.floor(this.countNum));
                },
                complete: function() {
                    $this.text(countTo);
                }
            });
        }
    });
});
</script>
{% endblock %}
