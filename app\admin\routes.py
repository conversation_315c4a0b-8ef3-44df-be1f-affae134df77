"""
用户管理路由（管理员功能）
"""
from flask import render_template, request, redirect, url_for, flash, session, jsonify
from app.admin import bp
from app.auth.decorators import login_required, admin_required
from app.admin.utils import AgentManager
from app.models import get_db_connection, Agent
import logging
import uuid
import hashlib

logger = logging.getLogger(__name__)


@bp.route('/')
@admin_required
def index():
    """用户管理首页"""
    # 获取统计数据
    connection = get_db_connection()
    try:
        with connection.cursor() as cursor:
            # 代理商总数
            cursor.execute("SELECT COUNT(*) FROM agents")
            total_agents = cursor.fetchone()[0]
            
            # 激活的代理商数
            cursor.execute("SELECT COUNT(*) FROM agents WHERE vip_is_active = 1 OR wechat_is_active = 1")
            active_agents = cursor.fetchone()[0]
            
            # VIP功能激活的代理商数
            cursor.execute("SELECT COUNT(*) FROM agents WHERE vip_is_active = 1")
            vip_active_agents = cursor.fetchone()[0]
            
            # 微信功能激活的代理商数
            cursor.execute("SELECT COUNT(*) FROM agents WHERE wechat_is_active = 1")
            wechat_active_agents = cursor.fetchone()[0]
            
            # 今日新增代理商
            cursor.execute("SELECT COUNT(*) FROM agents WHERE DATE(created_at) = CURDATE()")
            today_agents = cursor.fetchone()[0]
    finally:
        connection.close()
    
    stats = {
        'total_agents': total_agents,
        'active_agents': active_agents,
        'vip_active_agents': vip_active_agents,
        'wechat_active_agents': wechat_active_agents,
        'today_agents': today_agents
    }
    
    return render_template('admin/index.html', stats=stats)


@bp.route('/agents')
@admin_required
def agents():
    """代理商列表"""
    # 分页参数
    page = request.args.get('page', 1, type=int)
    per_page = 20
    offset = (page - 1) * per_page
    
    # 简化查询，不使用搜索和筛选
    where_clause = "1=1"
    params = []
    
    # 获取代理商列表
    connection = get_db_connection()
    try:
        with connection.cursor() as cursor:
            # 获取总数
            count_sql = f"SELECT COUNT(*) FROM agents WHERE {where_clause}"
            cursor.execute(count_sql, params)
            total = cursor.fetchone()[0]
            
            # 获取列表数据（明确指定字段顺序以支持双API系统）
            list_sql = f"""
            SELECT id, name, token, wechat_base_url, wechat_login_status,
                   proxy, `group`, vip_is_active, wechat_is_active, contact,
                   created_at, last_check_time, api_version, wxid, device_id
            FROM agents
            WHERE {where_clause}
            ORDER BY created_at DESC
            LIMIT %s OFFSET %s
            """
            cursor.execute(list_sql, params + [per_page, offset])
            agents_list = cursor.fetchall()
    finally:
        connection.close()
    
    # 计算分页信息
    total_pages = (total + per_page - 1) // per_page
    has_prev = page > 1
    has_next = page < total_pages
    
    return render_template('admin/agents.html',
                          agents=agents_list,
                          page=page,
                          total_pages=total_pages,
                          has_prev=has_prev,
                          has_next=has_next,
                          total=total)


@bp.route('/agents/create', methods=['GET', 'POST'])
@admin_required
def create_agent():
    """创建代理商"""
    if request.method == 'POST':
        try:
            # 获取表单数据
            name = request.form.get('name', '').strip()
            group = request.form.get('group', '').strip() or None
            contact = request.form.get('contact', '').strip() or None
            api_version = request.form.get('api_version', '').strip()
            vip_is_active = bool(request.form.get('vip_is_active'))
            wechat_is_active = bool(request.form.get('wechat_is_active'))

            # V1 API字段
            token = request.form.get('token', '').strip() or None
            wechat_base_url_v1 = request.form.get('wechat_base_url', '').strip() or None
            proxy_v1 = request.form.get('proxy', '').strip() or None

            # V2 API字段
            wxid = request.form.get('wxid', '').strip() or None
            device_id = request.form.get('device_id', '').strip() or None
            wechat_base_url_v2 = request.form.get('wechat_base_url_v2', '').strip() or None
            proxy_v2 = request.form.get('proxy_v2', '').strip() or None

            # V3 API字段
            token_v3 = request.form.get('token_v3', '').strip() or None
            app_id = request.form.get('app_id', '').strip() or None
            wechat_base_url_v3 = request.form.get('wechat_base_url_v3', '').strip() or None
            proxy_v3 = request.form.get('proxy_v3', '').strip() or None

            # 根据API版本确定最终使用的字段值
            if api_version == 'v1':
                wechat_base_url = wechat_base_url_v1
                proxy = proxy_v1
            elif api_version == 'v2':
                wechat_base_url = wechat_base_url_v2
                proxy = proxy_v2
            else:  # v3
                wechat_base_url = wechat_base_url_v3
                proxy = proxy_v3
                # V3使用token_v3作为token
                if token_v3:
                    token = token_v3

            if not name:
                flash('代理商名称不能为空', 'danger')
                return render_template('admin/create_agent.html')

            if not api_version:
                flash('请选择API版本', 'danger')
                return render_template('admin/create_agent.html')

            # 根据API版本验证必填字段
            if api_version == 'v1':
                if not token or not wechat_base_url:
                    flash('V1 API需要填写Token和微信API地址', 'danger')
                    return render_template('admin/create_agent.html')
            elif api_version == 'v2':
                if not wxid or not wechat_base_url:
                    flash('V2 API需要填写WXID和微信API地址', 'danger')
                    return render_template('admin/create_agent.html')
            elif api_version == 'v3':
                if not token or not wechat_base_url:
                    flash('V3 API需要填写Token和微信API地址', 'danger')
                    return render_template('admin/create_agent.html')

            # 直接插入数据库
            connection = get_db_connection()
            try:
                with connection.cursor() as cursor:
                    sql = """
                    INSERT INTO agents (name, `group`, contact, api_version, token,
                                      wechat_base_url, proxy, wxid, device_id, app_id,
                                      vip_is_active, wechat_is_active, created_at)
                    VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, NOW())
                    """
                    cursor.execute(sql, (name, group, contact, api_version, token,
                                       wechat_base_url, proxy, wxid, device_id, app_id,
                                       vip_is_active, wechat_is_active))
                    connection.commit()
                    agent_id = cursor.lastrowid
            finally:
                connection.close()

            flash(f'代理商 {name} 创建成功！', 'success')
            return redirect(url_for('admin.agents'))
            
        except Exception as e:
            logger.error(f"创建代理商失败: {str(e)}")
            flash(f'创建代理商失败: {str(e)}', 'danger')
    
    return render_template('admin/create_agent.html')


@bp.route('/agents/<int:agent_id>')
@admin_required
def agent_detail(agent_id):
    """代理商详情"""
    # 获取代理商信息
    connection = get_db_connection()
    try:
        with connection.cursor() as cursor:
            sql = "SELECT * FROM agents WHERE id = %s"
            cursor.execute(sql, (agent_id,))
            agent = cursor.fetchone()
            
            if not agent:
                flash('代理商不存在', 'danger')
                return redirect(url_for('admin.agents'))
            
            # 获取VIP卡统计
            cursor.execute("SELECT COUNT(*) FROM vip_cards WHERE agent_id = %s", (agent_id,))
            total_cards = cursor.fetchone()[0]
            
            cursor.execute("SELECT COUNT(*) FROM vip_cards WHERE agent_id = %s AND user_id IS NOT NULL AND user_id != ''", (agent_id,))
            bound_cards = cursor.fetchone()[0]
            
            cursor.execute("SELECT SUM(points) FROM vip_cards WHERE agent_id = %s", (agent_id,))
            result = cursor.fetchone()
            total_points = result[0] if result[0] else 0
    finally:
        connection.close()
    
    stats = {
        'total_cards': total_cards,
        'bound_cards': bound_cards,
        'total_points': total_points
    }
    
    return render_template('admin/agent_detail.html', agent=agent, stats=stats)


@bp.route('/agents/<int:agent_id>/edit', methods=['GET', 'POST'])
@admin_required
def edit_agent(agent_id):
    """编辑代理商"""
    # 获取代理商信息
    agent = Agent.get_by_id(agent_id)
    if not agent:
        flash('代理商不存在', 'danger')
        return redirect(url_for('admin.agents'))
    
    if request.method == 'POST':
        try:
            # 获取表单数据
            name = request.form.get('name', '').strip()
            group = request.form.get('group', '').strip() or None
            contact = request.form.get('contact', '').strip() or None
            api_version = request.form.get('api_version', '').strip()
            vip_is_active = bool(request.form.get('vip_is_active'))
            wechat_is_active = bool(request.form.get('wechat_is_active'))

            # V1 API字段
            token = request.form.get('token', '').strip() or None
            wechat_base_url_v1 = request.form.get('wechat_base_url', '').strip() or None
            proxy_v1 = request.form.get('proxy', '').strip() or None

            # V2 API字段
            wxid = request.form.get('wxid', '').strip() or None
            device_id = request.form.get('device_id', '').strip() or None
            wechat_base_url_v2 = request.form.get('wechat_base_url_v2', '').strip() or None
            proxy_v2 = request.form.get('proxy_v2', '').strip() or None

            # 根据API版本确定最终使用的字段值
            if api_version == 'v1':
                wechat_base_url = wechat_base_url_v1
                proxy = proxy_v1
            else:  # v2
                wechat_base_url = wechat_base_url_v2
                proxy = proxy_v2

            if not name:
                flash('名称不能为空', 'danger')
                return render_template('admin/edit_agent.html', agent=agent)

            if not api_version:
                flash('请选择API版本', 'danger')
                return render_template('admin/edit_agent.html', agent=agent)

            # 根据API版本验证必填字段
            if api_version == 'v1':
                if not token or not wechat_base_url:
                    flash('V1 API需要填写Token和微信API地址', 'danger')
                    return render_template('admin/edit_agent.html', agent=agent)
            elif api_version == 'v2':
                if not wxid or not wechat_base_url:
                    flash('V2 API需要填写WXID和微信API地址', 'danger')
                    return render_template('admin/edit_agent.html', agent=agent)

            # 更新代理商信息
            connection = get_db_connection()
            try:
                with connection.cursor() as cursor:
                    sql = """
                    UPDATE agents
                    SET name = %s, `group` = %s, contact = %s, api_version = %s,
                        token = %s, wechat_base_url = %s, proxy = %s,
                        wxid = %s, device_id = %s,
                        vip_is_active = %s, wechat_is_active = %s
                    WHERE id = %s
                    """
                    cursor.execute(sql, (name, group, contact, api_version,
                                       token, wechat_base_url, proxy,
                                       wxid, device_id,
                                       vip_is_active, wechat_is_active, agent_id))
                    connection.commit()
            finally:
                connection.close()

            flash(f'代理商 {name} 更新成功', 'success')
            return redirect(url_for('admin.agents'))
            
        except Exception as e:
            logger.error(f"更新代理商失败: {str(e)}")
            flash(f'更新代理商失败: {str(e)}', 'danger')
    
    return render_template('admin/edit_agent.html', agent=agent)


@bp.route('/agents/<int:agent_id>/reset_token', methods=['POST'])
@admin_required
def reset_token(agent_id):
    """重置代理商Token"""
    try:
        # 生成新Token
        new_token = str(uuid.uuid4()).replace('-', '')[:32]
        
        # 更新数据库
        connection = get_db_connection()
        try:
            with connection.cursor() as cursor:
                sql = "UPDATE agents SET token = %s WHERE id = %s"
                cursor.execute(sql, (new_token, agent_id))
                connection.commit()
                
                # 获取代理商名称
                cursor.execute("SELECT name FROM agents WHERE id = %s", (agent_id,))
                agent = cursor.fetchone()
                agent_name = agent['name'] if agent else '未知'
        finally:
            connection.close()
        
        return jsonify({
            'success': True, 
            'message': f'Token重置成功',
            'token': new_token
        })
        
    except Exception as e:
        logger.error(f"重置Token失败: {str(e)}")
        return jsonify({'success': False, 'message': str(e)})


@bp.route('/agents/<int:agent_id>/toggle_status', methods=['POST'])
@admin_required
def toggle_status(agent_id):
    """切换代理商状态"""
    try:
        data = request.get_json()
        status_type = data.get('type')  # vip 或 wechat
        status = data.get('status', False)
        
        if status_type not in ['vip', 'wechat']:
            return jsonify({'success': False, 'message': '无效的状态类型'})
        
        # 更新数据库
        connection = get_db_connection()
        try:
            with connection.cursor() as cursor:
                if status_type == 'vip':
                    sql = "UPDATE agents SET vip_is_active = %s WHERE id = %s"
                else:
                    sql = "UPDATE agents SET wechat_is_active = %s WHERE id = %s"
                
                cursor.execute(sql, (status, agent_id))
                connection.commit()
        finally:
            connection.close()
        
        status_text = '激活' if status else '停用'
        function_text = 'VIP功能' if status_type == 'vip' else '微信功能'
        
        return jsonify({
            'success': True, 
            'message': f'{function_text}{status_text}成功'
        })
        
    except Exception as e:
        logger.error(f"切换状态失败: {str(e)}")
        return jsonify({'success': False, 'message': str(e)})


@bp.route('/agents/<int:agent_id>/delete', methods=['POST'])
@admin_required
def delete_agent(agent_id):
    """删除代理商"""
    try:
        # 检查是否有关联的VIP卡
        connection = get_db_connection()
        try:
            with connection.cursor() as cursor:
                cursor.execute("SELECT COUNT(*) FROM vip_cards WHERE agent_id = %s", (agent_id,))
                card_count = cursor.fetchone()[0]
                
                if card_count > 0:
                    return jsonify({
                        'success': False, 
                        'message': f'该代理商下还有 {card_count} 张VIP卡，无法删除'
                    })
                
                # 获取代理商名称
                cursor.execute("SELECT name FROM agents WHERE id = %s", (agent_id,))
                agent = cursor.fetchone()
                agent_name = agent['name'] if agent else '未知'
                
                # 删除代理商
                cursor.execute("DELETE FROM agents WHERE id = %s", (agent_id,))
                connection.commit()
        finally:
            connection.close()
        
        return jsonify({
            'success': True, 
            'message': f'代理商 {agent_name} 删除成功'
        })
        
    except Exception as e:
        logger.error(f"删除代理商失败: {str(e)}")
        return jsonify({'success': False, 'message': str(e)})
