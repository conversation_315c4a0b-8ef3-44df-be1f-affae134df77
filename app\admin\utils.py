"""
管理员工具类
"""
import uuid
import hashlib
import datetime
import logging
from app.models import get_db_connection

logger = logging.getLogger(__name__)


class AgentManager:
    """代理商管理器"""
    
    def __init__(self):
        """初始化代理商管理器"""
        pass
    
    def generate_token(self):
        """生成Token"""
        return str(uuid.uuid4()).replace('-', '')[:32]
    
    def create_agent(self, group, name, contact=None, wechat_base_url=None, proxy=None, 
                    vip_is_active=True, wechat_is_active=False):
        """
        创建代理商
        
        Args:
            group: 代理分组
            name: 代理名称
            contact: 联系方式
            wechat_base_url: 微信API基础URL
            proxy: 代理设置
            vip_is_active: VIP功能是否激活
            wechat_is_active: 微信功能是否激活
            
        Returns:
            dict: 创建的代理商信息
        """
        connection = get_db_connection()
        try:
            with connection.cursor() as cursor:
                # 检查分组是否已存在
                sql = "SELECT COUNT(*) FROM agents WHERE `group` = %s"
                cursor.execute(sql, (group,))
                if cursor.fetchone()[0] > 0:
                    raise ValueError(f"代理分组 {group} 已存在")
                
                # 检查名称是否已存在
                sql = "SELECT COUNT(*) FROM agents WHERE name = %s"
                cursor.execute(sql, (name,))
                if cursor.fetchone()[0] > 0:
                    raise ValueError(f"代理名称 {name} 已存在")
                
                # 生成Token
                token = self.generate_token()
                
                # 创建代理商
                insert_sql = """
                INSERT INTO agents (group, name, contact, token, wechat_base_url, proxy, 
                                  vip_is_active, wechat_is_active, created_at)
                VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s)
                """
                
                now = datetime.datetime.now()
                cursor.execute(insert_sql, (
                    group, name, contact, token, wechat_base_url, proxy,
                    vip_is_active, wechat_is_active, now
                ))
                
                agent_id = cursor.lastrowid
                connection.commit()
                
                return {
                    'id': agent_id,
                    'group': group,
                    'name': name,
                    'contact': contact,
                    'token': token,
                    'wechat_base_url': wechat_base_url,
                    'proxy': proxy,
                    'vip_is_active': vip_is_active,
                    'wechat_is_active': wechat_is_active,
                    'created_at': now
                }
        except Exception as e:
            connection.rollback()
            raise ValueError(f"创建代理商失败: {str(e)}")
        finally:
            connection.close()
    
    def get_agent_by_id(self, agent_id):
        """根据ID获取代理商"""
        connection = get_db_connection()
        try:
            with connection.cursor() as cursor:
                sql = "SELECT * FROM agents WHERE id = %s"
                cursor.execute(sql, (agent_id,))
                return cursor.fetchone()
        finally:
            connection.close()
    
    def get_agent_by_group(self, group):
        """根据分组获取代理商"""
        connection = get_db_connection()
        try:
            with connection.cursor() as cursor:
                sql = "SELECT * FROM agents WHERE `group` = %s"
                cursor.execute(sql, (group,))
                return cursor.fetchone()
        finally:
            connection.close()
    
    def update_agent(self, agent_id, **kwargs):
        """
        更新代理商信息
        
        Args:
            agent_id: 代理商ID
            **kwargs: 要更新的字段
            
        Returns:
            bool: 更新是否成功
        """
        if not kwargs:
            return True
        
        # 构建更新SQL
        set_clauses = []
        params = []
        
        for field, value in kwargs.items():
            if field in ['name', 'contact', 'wechat_base_url', 'proxy', 'vip_is_active', 'wechat_is_active']:
                set_clauses.append(f"{field} = %s")
                params.append(value)
        
        if not set_clauses:
            return True
        
        params.append(agent_id)
        sql = f"UPDATE agents SET {', '.join(set_clauses)} WHERE id = %s"
        
        connection = get_db_connection()
        try:
            with connection.cursor() as cursor:
                cursor.execute(sql, params)
                connection.commit()
                return True
        except Exception as e:
            connection.rollback()
            raise ValueError(f"更新代理商失败: {str(e)}")
        finally:
            connection.close()
    
    def delete_agent(self, agent_id):
        """
        删除代理商
        
        Args:
            agent_id: 代理商ID
            
        Returns:
            bool: 删除是否成功
        """
        connection = get_db_connection()
        try:
            with connection.cursor() as cursor:
                # 检查是否有关联的VIP卡
                cursor.execute("SELECT COUNT(*) FROM vip_cards WHERE agent_id = %s", (agent_id,))
                card_count = cursor.fetchone()[0]
                
                if card_count > 0:
                    raise ValueError(f"该代理商下还有 {card_count} 张VIP卡，无法删除")
                
                # 删除代理商
                cursor.execute("DELETE FROM agents WHERE id = %s", (agent_id,))
                connection.commit()
                return True
        except Exception as e:
            connection.rollback()
            raise ValueError(f"删除代理商失败: {str(e)}")
        finally:
            connection.close()
    
    def reset_token(self, agent_id):
        """
        重置代理商Token
        
        Args:
            agent_id: 代理商ID
            
        Returns:
            str: 新的Token
        """
        new_token = self.generate_token()
        
        connection = get_db_connection()
        try:
            with connection.cursor() as cursor:
                sql = "UPDATE agents SET token = %s WHERE id = %s"
                cursor.execute(sql, (new_token, agent_id))
                connection.commit()
                return new_token
        except Exception as e:
            connection.rollback()
            raise ValueError(f"重置Token失败: {str(e)}")
        finally:
            connection.close()
    
    def toggle_status(self, agent_id, status_type, status):
        """
        切换代理商状态
        
        Args:
            agent_id: 代理商ID
            status_type: 状态类型 (vip, wechat)
            status: 状态值 (True/False)
            
        Returns:
            bool: 操作是否成功
        """
        if status_type not in ['vip', 'wechat']:
            raise ValueError("无效的状态类型")
        
        field = 'vip_is_active' if status_type == 'vip' else 'wechat_is_active'
        
        connection = get_db_connection()
        try:
            with connection.cursor() as cursor:
                sql = f"UPDATE agents SET {field} = %s WHERE id = %s"
                cursor.execute(sql, (status, agent_id))
                connection.commit()
                return True
        except Exception as e:
            connection.rollback()
            raise ValueError(f"切换状态失败: {str(e)}")
        finally:
            connection.close()
    
    def get_agent_stats(self, agent_id):
        """
        获取代理商统计信息
        
        Args:
            agent_id: 代理商ID
            
        Returns:
            dict: 统计信息
        """
        connection = get_db_connection()
        try:
            with connection.cursor() as cursor:
                # VIP卡总数
                cursor.execute("SELECT COUNT(*) FROM vip_cards WHERE agent_id = %s", (agent_id,))
                total_cards = cursor.fetchone()[0]
                
                # 已绑定VIP卡数
                cursor.execute("SELECT COUNT(*) FROM vip_cards WHERE agent_id = %s AND user_id IS NOT NULL AND user_id != ''", (agent_id,))
                bound_cards = cursor.fetchone()[0]
                
                # 总积分
                cursor.execute("SELECT SUM(points) FROM vip_cards WHERE agent_id = %s", (agent_id,))
                result = cursor.fetchone()
                total_points = result[0] if result[0] else 0
                
                # 今日新增VIP卡
                cursor.execute("SELECT COUNT(*) FROM vip_cards WHERE agent_id = %s AND DATE(created_at) = CURDATE()", (agent_id,))
                today_cards = cursor.fetchone()[0]
                
                return {
                    'total_cards': total_cards,
                    'bound_cards': bound_cards,
                    'total_points': total_points,
                    'today_cards': today_cards
                }
        finally:
            connection.close()
    
    def get_all_agents(self, active_only=False, search=None, status_filter=None):
        """
        获取所有代理商
        
        Args:
            active_only: 是否只返回激活的代理商
            search: 搜索关键词
            status_filter: 状态过滤 (vip_active, wechat_active, inactive)
            
        Returns:
            list: 代理商列表
        """
        connection = get_db_connection()
        try:
            with connection.cursor() as cursor:
                # 构建查询条件
                where_conditions = []
                params = []
                
                if active_only:
                    where_conditions.append("(vip_is_active = 1 OR wechat_is_active = 1)")
                
                if search:
                    where_conditions.append("(name LIKE %s OR `group` LIKE %s OR contact LIKE %s)")
                    params.extend([f'%{search}%', f'%{search}%', f'%{search}%'])
                
                if status_filter == 'vip_active':
                    where_conditions.append("vip_is_active = 1")
                elif status_filter == 'wechat_active':
                    where_conditions.append("wechat_is_active = 1")
                elif status_filter == 'inactive':
                    where_conditions.append("vip_is_active = 0 AND wechat_is_active = 0")
                
                where_clause = " AND ".join(where_conditions) if where_conditions else "1=1"
                
                sql = f"SELECT * FROM agents WHERE {where_clause} ORDER BY created_at DESC"
                cursor.execute(sql, params)
                return cursor.fetchall()
        finally:
            connection.close()
