#!/usr/bin/env python3
"""
YBA监控管理系统统一启动脚本
整合环境变量加载、Web服务启动、登录状态检查和数据库状态更新
"""
import os
import sys
import time
import logging
import signal
import threading
from datetime import datetime
from app import create_app
from app.utils.env_loader import EnvLoader

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('app.log'),
        logging.StreamHandler(sys.stdout)
    ]
)

logger = logging.getLogger(__name__)

class YBASystemManager:
    """YBA系统管理器"""
    
    def __init__(self):
        self.app = None
        self.shutdown_event = threading.Event()
        self.login_check_thread = None
        
    def load_environment(self):
        """加载环境变量"""
        logger.info("正在加载环境变量...")
        
        # 加载.env文件
        if not EnvLoader.load_env_file():
            logger.warning("未找到.env文件，将使用系统环境变量")
        
        # 验证必需的环境变量
        is_valid, missing_vars = EnvLoader.validate_required_env()
        if not is_valid:
            logger.error(f"缺少必需的环境变量: {', '.join(missing_vars)}")
            logger.info("请创建.env文件并设置以下环境变量:")
            for var in missing_vars:
                logger.info(f"  {var}=your-value-here")
            
            # 创建示例文件
            if not os.path.exists('.env.example'):
                EnvLoader.create_env_example()
                logger.info("已创建.env.example文件作为参考")
            
            return False
        
        logger.info("✓ 环境变量加载完成")
        return True
    
    def test_database_connection(self):
        """测试数据库连接"""
        logger.info("正在测试数据库连接...")
        
        try:
            from app.models import get_db_connection
            connection = get_db_connection()
            
            with connection.cursor() as cursor:
                cursor.execute("SELECT 1")
                cursor.fetchone()
            
            connection.close()
            logger.info("✓ 数据库连接测试成功")
            return True
            
        except Exception as e:
            logger.error(f"✗ 数据库连接测试失败: {str(e)}")
            logger.error("请检查数据库配置和连接")
            return False
    
    def create_flask_app(self):
        """创建Flask应用"""
        logger.info("正在创建Flask应用...")
        
        try:
            self.app = create_app()
            logger.info("✓ Flask应用创建成功")
            return True
            
        except Exception as e:
            logger.error(f"✗ Flask应用创建失败: {str(e)}")
            return False
    
    def check_login_status_once(self):
        """执行一次登录状态检查"""
        try:
            from app.utils.startup_checker import run_startup_check
            
            logger.info("正在检查微信登录状态...")
            result = run_startup_check()
            
            if result['success']:
                logger.info(f"✓ 登录状态检查完成: {result['message']}")
                logger.info(f"  总计: {result['total']}, 在线: {result['online']}, 离线: {result['offline']}")
            else:
                logger.warning(f"⚠ 登录状态检查部分失败: {result['message']}")
            
            return result
            
        except Exception as e:
            logger.error(f"✗ 登录状态检查失败: {str(e)}")
            return {'success': False, 'message': str(e), 'total': 0, 'online': 0, 'offline': 0}
    
    def periodic_login_check(self):
        """定期检查登录状态"""
        check_interval = int(os.environ.get('LOGIN_CHECK_INTERVAL', 300))  # 默认5分钟
        
        logger.info(f"启动定期登录状态检查，间隔: {check_interval}秒")
        
        while not self.shutdown_event.is_set():
            try:
                # 等待指定时间或直到收到关闭信号
                if self.shutdown_event.wait(check_interval):
                    break
                
                # 执行检查
                logger.info("执行定期登录状态检查...")
                result = self.check_login_status_once()
                
                if result['success']:
                    logger.info(f"定期检查完成 - 在线: {result['online']}/{result['total']}")
                else:
                    logger.warning("定期检查出现问题")
                    
            except Exception as e:
                logger.error(f"定期检查过程中出现异常: {str(e)}")
                # 出现异常时等待一段时间再继续
                self.shutdown_event.wait(60)
        
        logger.info("定期登录状态检查已停止")
    
    def start_background_tasks(self):
        """启动后台任务"""
        # 启动定期登录检查线程
        if os.environ.get('ENABLE_PERIODIC_CHECK', 'true').lower() == 'true':
            self.login_check_thread = threading.Thread(
                target=self.periodic_login_check,
                daemon=True,
                name="LoginChecker"
            )
            self.login_check_thread.start()
            logger.info("✓ 后台任务已启动")
    
    def setup_signal_handlers(self):
        """设置信号处理器"""
        def signal_handler(signum, frame):
            logger.info(f"收到信号 {signum}，正在优雅关闭...")
            self.shutdown()
        
        signal.signal(signal.SIGINT, signal_handler)
        signal.signal(signal.SIGTERM, signal_handler)
    
    def shutdown(self):
        """优雅关闭系统"""
        logger.info("正在关闭系统...")
        
        # 设置关闭事件
        self.shutdown_event.set()
        
        # 等待后台线程结束
        if self.login_check_thread and self.login_check_thread.is_alive():
            logger.info("等待后台任务结束...")
            self.login_check_thread.join(timeout=5)
        
        logger.info("系统已关闭")
        sys.exit(0)
    
    def start_web_server(self):
        """启动Web服务器"""
        try:
            # 获取配置
            host = os.environ.get('FLASK_HOST', '0.0.0.0')
            port = int(os.environ.get('FLASK_PORT', 5000))
            debug = os.environ.get('FLASK_DEBUG', 'False').lower() == 'true'
            
            logger.info(f"启动Web服务器: http://{host}:{port}")
            logger.info("按 Ctrl+C 停止服务器")
            
            # 启动Flask应用
            self.app.run(
                host=host,
                port=port,
                debug=debug,
                use_reloader=False,  # 禁用重载器以避免重复启动后台任务
                threaded=True
            )
            
        except Exception as e:
            logger.error(f"Web服务器启动失败: {str(e)}")
            raise
    
    def run(self):
        """运行系统"""
        logger.info("=" * 60)
        logger.info("YBA监控管理系统启动")
        logger.info("=" * 60)
        
        try:
            # 1. 加载环境变量
            if not self.load_environment():
                return False
            
            # 2. 测试数据库连接
            if not self.test_database_connection():
                logger.warning("数据库连接失败，但系统仍将启动")
            
            # 3. 创建Flask应用
            if not self.create_flask_app():
                return False
            
            # 4. 执行初始登录状态检查
            self.check_login_status_once()
            
            # 5. 设置信号处理器
            self.setup_signal_handlers()
            
            # 6. 启动后台任务
            self.start_background_tasks()
            
            # 7. 启动Web服务器
            self.start_web_server()
            
        except KeyboardInterrupt:
            logger.info("收到中断信号")
            self.shutdown()
        except Exception as e:
            logger.error(f"系统启动失败: {str(e)}")
            return False
        
        return True


def main():
    """主函数"""
    manager = YBASystemManager()
    success = manager.run()
    sys.exit(0 if success else 1)


if __name__ == '__main__':
    main()
