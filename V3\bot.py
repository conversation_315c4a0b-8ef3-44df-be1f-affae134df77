import json
import os
import time
import sys
import subprocess
from gewechat_client import GewechatClient
import logging
from flask import Flask, send_file, request, render_template, send_from_directory, make_response
from flask_caching import Cache
from threading import Thread
from message_handler import MessageHandler
import requests
from PIL import Image, ImageEnhance
import io
import base64
import socket
import re
import xml.etree.ElementTree as ET

# 禁用 Werkzeug 日志
log = logging.getLogger('werkzeug')
log.setLevel(logging.ERROR)

class WeChatBot:
    def __init__(self, bot_id=None):
        # 配置日志
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(levelname)s - %(message)s'
        )
        self.logger = logging.getLogger(__name__)
        
        # 机器人ID，主机器人为None，副机器人为wxid
        self.bot_id = bot_id
        self.is_master = bot_id is None
        
        # 加载机器人配置
        self.config = self.load_bot_config()
        self.bots_info = self.load_bots_info()
        
        # 配置参数
        self.base_url = self.config.get('base_url', os.environ.get("BASE_URL", "http://127.0.0.1:2531/v2/api"))
        self.token = self.config.get('token')
        self.app_id = self.config.get('appid')
        self.wxid = self.config.get('wxid')
        self.nickname = self.config.get('nickname', 'YBA_BOT')
        self.callback_port = self.config.get('callback_port', 2533)
        base_callback = self.config.get('callback_url', "http://cloud.yaoboan.com")
        self.callback_base_url = self._normalize_url(base_callback)
        
        # 广告开关
        self.show_ads = False
        if self.wxid and self.wxid in self.bots_info:
            self.show_ads = self.bots_info[self.wxid].get('show_ads', False)
        
        # 如果是副机器人，使用主机器人的回调地址
        if not self.is_master:
            master_config = {}
            try:
                with open("wechat_config.json", 'r', encoding='utf-8') as f:
                    master_config = json.load(f)
                master_port = master_config.get('callback_port', 2533)
                self.callback_url = self._build_url(self.callback_base_url, master_port, "/api/receive_message")
            except Exception as e:
                self.logger.error(f"加载主配置失败: {str(e)}")
                self.callback_url = self._build_url(self.callback_base_url, self.callback_port, "/api/receive_message")
        else:
            self.show_ads = True
            self.callback_url = self._build_url(self.callback_base_url, self.callback_port, "/api/receive_message")
        
        # ext_url 固定包含端口号，除非配置中特别指定
        self.ext_url = self.config.get('ext_url', "http://cloud.yaoboan.com:2533")
        self.master_wxid = self.config.get('master_wxid', ['YBA-19990312'])
        if isinstance(self.master_wxid, str):
            self.master_wxid = [self.master_wxid]
        self.master_group = self.config.get('master_group', [])
        if isinstance(self.master_group, str):
            self.master_group = [self.master_group] if self.master_group else []
        
        # 主机器人特定配置
        if self.is_master:
            self.sub_bots = {}  # 存储子机器人进程
            self.bot_ports = {}  # 存储机器人端口映射 {wxid: port}
            # 初始化时注册所有副机器人的端口
            # self._register_all_sub_bots_ports()
        
        # 创建 GewechatClient 实例
        self.client = None
        if self.base_url:
            self.client = GewechatClient(self.base_url, self.token)
            
            # 如果未配置token，尝试获取token
            if not self.token and self.base_url:
                self.logger.info("未配置token，正在获取...")
                try:
                    token_result = self.client.get_token()
                    print(token_result)
                    if token_result.get('ret') == 200 and token_result.get('data'):
                        self.token = token_result['data']
                        self.logger.info("成功获取token")
                        self.save_bot_config()  # 保存新获取的token
                        # 重新创建 GewechatClient 实例
                        self.client = GewechatClient(self.base_url, self.token)
                    else:
                        self.logger.error("获取token失败")
                except Exception as e:
                    self.logger.error(f"获取token时发生错误: {str(e)}")
        else:
            self.logger.info("base_url为空，不创建GewechatClient实例")
        
        # 配置图片目录
        self.image_dir = self._get_image_dir()
        os.makedirs(self.image_dir, exist_ok=True)
        
        # 配置Flask应用
        self.app = Flask(__name__)
        
        # 禁用 Flask 的开发模式日志
        self.app.logger.disabled = True
        self.app.config['PROPAGATE_EXCEPTIONS'] = False
        
        # 配置缓存
        cache_config = {
            'CACHE_TYPE': 'filesystem',
            'CACHE_DIR': 'flask_cache',
            'CACHE_DEFAULT_TIMEOUT': 1 * 24 * 60 * 60  # 1天缓存
        }
        self.app.config.from_mapping(cache_config)
        self.cache = Cache(self.app)
        
        # 设置路由
        self.setup_routes()
        
        # MessageHandler 将在 run 方法中初始化
        self.message_handler = None
        
        # 添加UDP接收缓冲区
        self.udp_buffer = {}
        
        # 启动UDP监听线程（仅副机器人）
        if not self.is_master:
            # 标记为运行中
            self.is_running = True
            self.start_udp_listener()

    def get_config_path(self):
        """获取配置文件路径"""
        if self.is_master:
            return "wechat_config.json"
        else:
            # 创建子机器人配置目录
            os.makedirs("bot_configs", exist_ok=True)
            return f"bot_configs/{self.bot_id}.json"

    def load_bots_info(self):
        """加载机器人信息文件"""
        try:
            config_file = "bots.json"
            if not os.path.exists(config_file):
                # 创建默认机器人信息文件
                default_config = {}
                with open(config_file, 'w', encoding='utf-8') as f:
                    json.dump(default_config, f, indent=2, ensure_ascii=False)
                self.logger.info("已创建默认机器人信息文件")
                return default_config
                
            with open(config_file, 'r', encoding='utf-8') as f:
                config = json.load(f)
                self.logger.info("成功加载机器人信息文件")
                return config
        except Exception as e:
            self.logger.error(f"加载机器人信息文件失败: {str(e)}")
            return {}

    def save_bots_info(self):
        """保存机器人信息"""
        try:
            with open('bots.json', 'w', encoding='utf-8') as f:
                json.dump(self.bots_info, f, indent=2, ensure_ascii=False)
            self.logger.info("机器人信息已保存")
        except Exception as e:
            self.logger.error(f"保存机器人信息失败: {str(e)}")

    def update_bot_in_bots_info(self, status=None):
        """更新机器人信息文件中的机器人信息"""
        if not self.wxid:
            return

        if self.is_master:
            return
            
        # 获取当前时间
        current_time = time.strftime("%Y-%m-%d %H:%M:%S", time.localtime())
        
        # 检查机器人是否已存在
        if self.wxid not in self.bots_info:
            # 新建机器人信息
            self.bots_info[self.wxid] = {
                "nickname": self.nickname,
                "created_at": current_time,
                "effective_date": current_time,
                "expiry_date": "",  # 默认不设置过期时间
                "status": status or ("running" if hasattr(self, 'is_running') and self.is_running else "stopped"),
                "show_ads": self.show_ads  # 添加广告开关
            }
        else:
            # 更新现有机器人信息
            self.bots_info[self.wxid].update({
                "nickname": self.nickname,
                "last_updated": current_time
            })
            if status:
                self.bots_info[self.wxid]["status"] = status
            elif hasattr(self, 'is_running'):
                self.bots_info[self.wxid]["status"] = "running" if self.is_running else "stopped"
            # 更新广告开关
            self.bots_info[self.wxid]["show_ads"] = self.show_ads
        
        self.save_bots_info()

    def load_bot_config(self):
        """加载机器人配置文件"""
        try:
            config_file = self.get_config_path()
            if not os.path.exists(config_file):
                self.logger.warning(f"配置文件 {config_file} 不存在")
                # 如果是副机器人，从主配置获取基本信息
                if not self.is_master:
                    # 先加载主配置
                    master_config = {}
                    try:
                        with open("wechat_config.json", 'r', encoding='utf-8') as f:
                            master_config = json.load(f)
                    except Exception as e:
                        self.logger.error(f"加载主配置失败: {str(e)}")
                    
                    # 创建副机器人配置
                    config = {
                        "wxid": self.bot_id,
                        "appid": "",
                        "token": "",
                        "nickname": f"副机器人_{self.bot_id[-4:]}",
                        "base_url": master_config.get('base_url', "http://127.0.0.1:2531/v2/api"),
                        "callback_url": master_config.get('callback_url', "http://cloud.yaoboan.com"),
                        "callback_port": master_config.get('callback_port', 2533) + 1, # 副机器人端口偏移
                        "ext_url": master_config.get('ext_url', "http://cloud.yaoboan.com")
                    }
                    
                    with open(config_file, 'w', encoding='utf-8') as f:
                        json.dump(config, f, indent=2, ensure_ascii=False)
                    return config
                return {}
                
            with open(config_file, 'r', encoding='utf-8') as f:
                config = json.load(f)
                self.logger.info(f"成功加载机器人配置文件: {config_file}")
                return config
        except Exception as e:
            self.logger.error(f"加载机器人配置文件失败: {str(e)}")
            return {}

    def _normalize_url(self, url):
        """标准化URL，移除端口号和末尾斜杠"""
        if not url:
            return url
        # 移除末尾的斜杠
        url = url.rstrip('/')
        # 分离协议和主机部分
        if '://' in url:
            protocol, rest = url.split('://', 1)
            # 移除端口号
            if ':' in rest:
                host = rest.split(':')[0]
                return f"{protocol}://{host}"
            return url
        return url

    def _build_url(self, base_url, port=None, path=None):
        """构建完整的URL
        
        Args:
            base_url: 基础URL（不含端口号）
            port: 端口号（可选）
            path: 路径（可选）
            
        Returns:
            str: 完整的URL
        """
        url = self._normalize_url(base_url)
        if port:
            url = f"{url}:{port}"
        if path:
            # 确保路径以/开头
            if not path.startswith('/'):
                path = '/' + path
            url = f"{url}{path}"
        return url

    def _calculate_sub_bot_port(self, base_port):
        """计算副机器人端口号
        
        Args:
            base_port: 基础端口号
            
        Returns:
            int: 新的端口号
        """
        # 获取所有已使用的端口
        used_ports = set()
        for wxid in self.bots_info.keys():
            config_path = f"bot_configs/{wxid}.json"
            try:
                with open(config_path, 'r', encoding='utf-8') as f:
                    config = json.load(f)
                    used_ports.add(config.get('callback_port', 0))
            except:
                continue
        
        # 从基础端口开始，找到第一个未使用的端口
        port = base_port + 1
        while port in used_ports:
            port += 1
        
        return port

    def save_bot_config(self):
        """保存机器人配置"""
        try:
            config_file = self.get_config_path()
            config = {
                'appid': getattr(self, 'app_id', ''),
                'wxid': getattr(self, 'wxid', ''),
                'token': getattr(self, 'token', ''),
                'nickname': getattr(self, 'nickname', ''),
                'master_wxid': getattr(self, 'master_wxid', []),
                'master_group': getattr(self, 'master_group', []),
                'base_url': getattr(self, 'base_url', ''),
                'callback_url': getattr(self, 'callback_base_url', ''),
                'callback_port': getattr(self, 'callback_port', 2533),
                'ext_url': getattr(self, 'ext_url', '')
            }
            
            with open(config_file, 'w', encoding='utf-8') as f:
                json.dump(config, f, indent=2, ensure_ascii=False)
            self.logger.info("机器人配置已保存")
            
            # 更新机器人信息
            self.update_bot_in_bots_info()
        except Exception as e:
            self.logger.error(f"保存机器人配置失败: {str(e)}")

    def _get_image_dir(self):
        """获取机器人的图片目录"""
        if self.is_master:
            return "product_qr_img"
        else:
            return f"product_qr_img/{self.bot_id}"

    def setup_routes(self):
        """设置Flask路由"""

        @self.app.route('/product_qr_img/<path:filename>')
        def serve_product_image(filename):
            try:
                print("访问图片资源："+ filename)
                # 如果是主机器人，直接在主目录下查找
                if self.is_master:
                    file_path = os.path.join('product_qr_img', filename)
                else:
                    # 副机器人在自己的子目录下查找
                    file_path = os.path.join(self.image_dir, filename)
                
                if not os.path.exists(file_path):
                    # 如果在自己的目录下找不到，尝试在主目录下查找（用于共享资源）
                    if not self.is_master:
                        main_file_path = os.path.join('product_qr_img', filename)
                        if os.path.exists(main_file_path):
                            file_path = main_file_path
                        else:
                            return '', 404
                    else:
                        return '', 404
                    
                # # 读取图片二进制内容
                # with open(file_path, 'rb') as f:
                #     image_binary = f.read()
                
                # # 根据文件扩展名设置正确的Content-Type
                # content_type = 'image/jpeg' if filename.lower().endswith('.jpeg') else 'image/jpeg'
                
                # # 创建响应对象
                # response = make_response(image_binary)
                # response.headers['Content-Type'] = content_type
                # return response

                # 设置响应头允许跨域访问
                response = send_file(file_path, mimetype='image/jpeg')
                response.headers['Access-Control-Allow-Origin'] = '*'
                return response
            except Exception as e:
                self.logger.error(f"提供图片访问失败: {str(e)}")
                return '', 404

        @self.app.route('/login/<wxid>/<token>')
        def login_page(wxid, token=None):
            """登录页面路由"""
            try:
                # 如果提供了token，验证其有效性
                if token:
                    token_key = f"login_token_{token}_{wxid}"
                    if not self.cache.get(token_key):
                        return "登录链接已过期或无效，请重新获取", 403
                else:
                    return "无效的登录请求", 403
                
                # 生成会话token用于后续API调用
                session_token = f"{time.time()}_{wxid}"
                # 缓存token和wxid的映射关系，有效期五分钟
                self.cache.set(f"login_token_{session_token}", wxid, timeout=300)
                
                return render_template('login.html', 
                                      wxid=wxid, 
                                      token=session_token, 
                                      server_url=self.ext_url)
            except Exception as e:
                self.logger.error(f"生成登录页面失败: {str(e)}")
                return f"生成登录页面失败: {str(e)}", 500

        @self.app.route('/api/get_qrcode', methods=['POST'])
        def get_qrcode():
            """获取登录二维码API"""
            try:
                data = request.json
                token = data.get('token')
                device_info = data.get('deviceInfo', {})
                
                if not token:
                    return {'ret': 400, 'msg': '无效的请求参数'}
                
                # 从缓存获取wxid和token关联信息
                token_info = self.cache.get(f"login_token_{token}")
                if not token_info:
                    return {'ret': 401, 'msg': '登录令牌已过期，请重新发起登录'}
                
                wxid = token_info
                
                # 检查是否已经存在会话
                existing_session = self.cache.get(f"login_session_{token}")
                if existing_session:
                    # 检查设备信息
                    if device_info and existing_session.get('device_info'):
                        # 比较设备信息，确保是同一台设备
                        # 简单检查，实际应用中可以更复杂
                        orig_info = existing_session.get('device_info')
                        if (orig_info.get('userAgent') != device_info.get('userAgent') or
                            orig_info.get('platform') != device_info.get('platform')):
                            self.logger.warning(f"设备信息不匹配: {token}")
                    
                    # 更新设备信息和检查时间
                    existing_session['device_info'] = device_info
                    existing_session['last_check'] = time.time()
                    self.cache.set(f"login_session_{token}", existing_session, timeout=3600)
                    
                    # 如果已经登录成功，直接返回在线状态
                    if existing_session.get('status') == 'online':
                        return {
                            'ret': 200,
                            'status': 'online',
                            'nickname': existing_session.get('nickname', '未知用户')
                        }
                    
                    # 尝试使用现有会话获取二维码
                    client_base_url = existing_session.get('client_base_url')
                    client_token = existing_session.get('client_token')
                    if client_base_url and client_token:
                        try:
                            client_tmp = GewechatClient(client_base_url, client_token)
                            app_id = existing_session.get('app_id')
                            
                            # 如果已有app_id，重新获取二维码
                            if app_id:
                                qr_result = client_tmp.get_qr(app_id)
                                if qr_result and isinstance(qr_result, dict) and qr_result.get('ret') == 200:
                                    qr_data = qr_result.get('data', {})
                                    if qr_data:
                                        # 更新会话信息
                                        existing_session['uuid'] = qr_data.get('uuid')
                                        existing_session['expired_time'] = qr_data.get('expiredTime', 60)
                                        self.cache.set(f"login_session_{token}", existing_session, timeout=3600)
                                        
                                        # 获取二维码图片的Base64数据
                                        qr_image = qr_data.get('qrImgBase64', '')
                                        # 如果返回的是qrData（URL）而不是base64，则需要获取图片
                                        if not qr_image and qr_data.get('qrData'):
                                            try:
                                                response = requests.get(qr_data.get('qrData'))
                                                if response.status_code == 200:
                                                    # 将图片转换为base64
                                                    qr_image = base64.b64encode(response.content).decode('utf-8')
                                            except Exception as e:
                                                self.logger.error(f"获取二维码图片失败: {str(e)}")
                                        
                                        # 确保二维码数据不包含前缀
                                        if qr_image and qr_image.startswith('data:'):
                                            qr_image = qr_image.split(',', 1)[1]
                                        
                                        if qr_image:
                                            return {
                                                'ret': 200,
                                                'msg': '获取二维码成功',
                                                'data': {
                                                    'qr_image': qr_image,
                                                    'expired_time': qr_data.get('expiredTime', 60)
                                                }
                                            }
                        except Exception as e:
                            self.logger.error(f"使用现有会话获取二维码失败: {str(e)}")
                            # 继续尝试创建新会话，不要直接返回
                
                # 从配置获取机器人信息
                config_path = f"bot_configs/{wxid}.json"
                if not os.path.exists(config_path):
                    return {'ret': 404, 'msg': f'未找到机器人 {wxid} 的配置文件'}
                
                with open(config_path, 'r', encoding='utf-8') as f:
                    config = json.load(f)
                
                # 检查配置是否完整
                if not config.get('base_url'):
                    return {'ret': 500, 'msg': '机器人配置错误，缺少base_url'}
                    
                # 创建临时客户端
                client_tmp = None
                try:
                    client_tmp = GewechatClient(config.get('base_url'), config.get('token'))
                    
                    # 如果未配置token，尝试获取token
                    if not config.get('token') and config.get('base_url'):
                        token_result = client_tmp.get_token()
                        if token_result.get('ret') == 200 and token_result.get('data'):
                            config['token'] = token_result['data']
                            with open(config_path, 'w', encoding='utf-8') as f:
                                json.dump(config, f, indent=2, ensure_ascii=False)
                            # 重新创建客户端
                            client_tmp = GewechatClient(config.get('base_url'), config.get('token'))
                except Exception as e:
                    self.logger.error(f"创建临时客户端失败: {str(e)}")
                    return {'ret': 500, 'msg': f'创建临时客户端失败: {str(e)}'}
                
                # 检查是否已在线
                try:
                    if config.get('appid'):
                        check_online_response = client_tmp.check_online(config.get('appid'))
                        if check_online_response.get('ret') == 200 and check_online_response.get('data') == True:
                            # 缓存会话信息供确认登录使用
                            session_data = {
                                'wxid': wxid,
                                'app_id': config.get('appid'),
                                'client_base_url': config.get('base_url'),
                                'client_token': config.get('token'),
                                'status': 'online',
                                'device_info': device_info,
                                'created_at': time.time(),
                                'last_check': time.time(),
                                'nickname': config.get('nickname', '未知用户')
                            }
                            self.cache.set(f"login_session_{token}", session_data, timeout=3600)
                            
                            return {
                                'ret': 200, 
                                'msg': '已在线，无需登录',
                                'status': 'online',
                                'nickname': config.get('nickname', '')
                            }
                except Exception as e:
                    self.logger.error(f"检查在线状态失败: {str(e)}")
                    # 继续尝试获取二维码，不要直接返回
                
                # 获取登录二维码
                try:
                    qr_result = client_tmp.get_qr(config.get('appid', ''))
                    self.logger.info(f"获取二维码结果: {qr_result}")
                    
                    if qr_result and isinstance(qr_result, dict) and qr_result.get('ret') == 200:
                        qr_data = qr_result.get('data', {})
                        if qr_data:
                            # 更新 appid
                            config['appid'] = qr_data.get('appId', config['appid'])
                            with open(config_path, 'w', encoding='utf-8') as f:
                                json.dump(config, f, indent=2, ensure_ascii=False)
                            
                            # 获取二维码图片的Base64数据
                            qr_image = qr_data.get('qrImgBase64', '')
                            # 如果返回的是qrData（URL）而不是base64，则需要获取图片
                            if not qr_image and qr_data.get('qrData'):
                                try:
                                    response = requests.get(qr_data.get('qrData'))
                                    if response.status_code == 200:
                                        # 将图片转换为base64
                                        qr_image = base64.b64encode(response.content).decode('utf-8')
                                except Exception as e:
                                    self.logger.error(f"获取二维码图片失败: {str(e)}")
                            
                            # 确保二维码数据不包含前缀
                            if qr_image and qr_image.startswith('data:'):
                                qr_image = qr_image.split(',', 1)[1]
                            
                            if not qr_image:
                                return {'ret': 500, 'msg': '获取二维码图片数据失败'}
                            
                            # 缓存会话信息供确认登录使用
                            session_data = {
                                'wxid': wxid,
                                'app_id': qr_data.get('appId'),
                                'uuid': qr_data.get('uuid'),
                                'client_base_url': config.get('base_url'),
                                'client_token': config.get('token'),
                                'status': 'pending',
                                'expired_time': qr_data.get('expiredTime', 60),
                                'device_info': device_info,
                                'created_at': time.time(),
                                'last_check': time.time()
                            }
                            self.cache.set(f"login_session_{token}", session_data, timeout=3600)
                            
                            # 记录日志
                            self.logger.info(f"用户请求登录: wxid={wxid}, token={token}, device={device_info.get('platform', 'unknown')}")
                            
                            return {
                                'ret': 200,
                                'msg': '获取二维码成功',
                                'data': {
                                    'qr_image': qr_image,
                                    'expired_time': qr_data.get('expiredTime', 60)
                                }
                            }
                    
                    return {'ret': 500, 'msg': '获取二维码失败，请稍后重试'}
                
                except Exception as e:
                    self.logger.error(f"获取二维码失败: {str(e)}")
                    import traceback
                    self.logger.error(traceback.format_exc())
                    return {'ret': 500, 'msg': f'获取二维码失败: {str(e)}'}
            
            except Exception as e:
                self.logger.error(f"获取二维码请求处理失败: {str(e)}")
                import traceback
                self.logger.error(traceback.format_exc())
                return {'ret': 500, 'msg': f'获取二维码请求处理失败: {str(e)}'}

        @self.app.route('/api/check_login_status', methods=['POST'])
        def check_login_status():
            """检查登录状态API"""
            try:
                data = request.json
                token = data.get('token')
                device_info = data.get('deviceInfo', {})
                
                if not token:
                    return {'ret': 400, 'msg': '无效的请求参数'}
                
                # 获取会话信息
                session_data = self.cache.get(f"login_session_{token}")
                if not session_data:
                    return {'ret': 401, 'msg': '登录会话已过期，请重新发起登录'}
                
                # 更新检查时间
                session_data['last_check'] = time.time()
                
                # 更新设备信息（如果提供）
                if device_info:
                    session_data['device_info'] = device_info
                
                # 如果已登录成功
                if session_data.get('status') == 'online':
                    # 更新缓存
                    self.cache.set(f"login_session_{token}", session_data, timeout=3600)
                    return {
                        'ret': 200,
                        'status': 'online',
                        'nickname': session_data.get('nickname', '未知用户')
                    }
                
                # 获取临时客户端信息
                wxid = session_data.get('wxid')
                app_id = session_data.get('app_id')
                uuid = session_data.get('uuid')
                client_base_url = session_data.get('client_base_url')
                client_token = session_data.get('client_token')
                
                if not all([wxid, app_id, client_base_url, client_token]):
                    return {'ret': 500, 'msg': '会话信息不完整，请重新发起登录'}
                
                # 创建临时客户端
                client_tmp = GewechatClient(client_base_url, client_token)
                
                # 先使用check_online检查是否已经登录
                try:
                    online_result = client_tmp.check_online(app_id)
                    
                    if online_result and isinstance(online_result, dict) and online_result.get('ret') == 200:
                        # 判断是否在线
                        if online_result.get('data') == True:
                            # 已登录，获取用户信息
                            try:
                                profile = client_tmp.get_self_info(app_id)
                                nickname = ""
                                if profile and isinstance(profile, dict) and profile.get('ret') == 200:
                                    profile_data = profile.get('data', {})
                                    if profile_data:
                                        nickname = profile_data.get('nickname', '未知用户')
                                
                                # 更新会话信息
                                session_data['status'] = 'online'
                                session_data['nickname'] = nickname
                                
                                # 更新机器人信息
                                bot_info = self.bots_info.get(wxid, {})
                                if bot_info:
                                    bot_info['appId'] = app_id
                                    bot_info['nickname'] = nickname
                                    self.bots_info[wxid] = bot_info
                                    self._save_bots_info()
                                
                                # 保存会话信息
                                self.cache.set(f"login_session_{token}", session_data, timeout=3600)
                                
                                # 启动机器人线程
                                self.start_sub_bot(wxid)
                                
                                return {
                                    'ret': 200,
                                    'status': 'online',
                                    'nickname': nickname
                                }
                            except Exception as e:
                                self.logger.error(f"获取用户信息失败: {str(e)}")
                        
                        # 如果不在线但有登录流程，则检查二维码状态
                        if uuid:
                            # 使用check_qr检查二维码状态
                            qr_result = client_tmp.check_qr(app_id, uuid, "")
                            
                            if qr_result and isinstance(qr_result, dict) and qr_result.get('ret') == 200:
                                qr_data = qr_result.get('data', {})
                                status = qr_data.get('status')
                                
                                # 如果登录成功
                                if status == 2:
                                    nickname = qr_data.get('nickName', '未知用户')
                                    
                                    # 更新会话信息
                                    session_data['status'] = 'online'
                                    session_data['nickname'] = nickname
                                    self.cache.set(f"login_session_{token}", session_data, timeout=3600)
                                    
                                    # 启动机器人
                                    self.start_sub_bot(wxid)
                                    
                                    return {
                                        'ret': 200,
                                        'status': 'online',
                                        'nickname': nickname
                                    }
                                
                                # 如果等待扫码或确认
                                elif status == 0:
                                    session_data['status'] = 'pending'
                                    self.cache.set(f"login_session_{token}", session_data, timeout=3600)
                                    
                                    return {
                                        'ret': 200,
                                        'status': 'pending',
                                        'msg': '等待扫码或确认',
                                        'expired_time': qr_data.get('expiredTime', 60)
                                    }
                                
                                # 检查是否需要验证码
                                elif qr_data.get('msg') == "本次登录需要输入安全验证码":
                                    session_data['status'] = 'need_captcha'
                                    self.cache.set(f"login_session_{token}", session_data, timeout=3600)
                                    
                                    return {
                                        'ret': 202,
                                        'status': 'need_captcha',
                                        'msg': '需要输入验证码'
                                    }
                            
                            # 检查二维码是否过期
                            # qr_result 可能返回错误，检查错误信息
                            error_msg = qr_result.get('msg', '')
                            if '二维码已过期' in error_msg or '二维码已失效' in error_msg:
                                return self._refresh_qrcode(token, session_data, client_tmp, app_id)
                            
                            # 检查时间是否超出二维码有效期
                            created_time = session_data.get('created_at', 0)
                            expired_time = session_data.get('expired_time', 60)
                            if time.time() - created_time > expired_time:
                                return self._refresh_qrcode(token, session_data, client_tmp, app_id)
                    
                    # 默认返回等待状态
                    return {
                        'ret': 200,
                        'status': 'pending',
                        'msg': '等待扫码',
                        'expired_time': session_data.get('expired_time', 60)
                    }
                    
                except Exception as e:
                    self.logger.error(f"检查登录状态出现异常: {str(e)}")
                    import traceback
                    self.logger.error(traceback.format_exc())
                    
                    # 检查是否需要刷新二维码
                    err_str = str(e).lower()
                    if '二维码已过期' in err_str or '二维码已失效' in err_str or 'qrcode expired' in err_str:
                        self.logger.info(f"二维码已过期异常，准备刷新二维码: {token}")
                        return self._refresh_qrcode(token, session_data, client_tmp, app_id)
                    
                    return {'ret': 500, 'msg': f'检查登录状态出现异常: {str(e)}'}
            
            except Exception as e:
                self.logger.error(f"处理检查登录状态请求失败: {str(e)}")
                import traceback
                self.logger.error(traceback.format_exc())
                return {'ret': 500, 'msg': f'处理检查登录状态请求失败: {str(e)}'}

        def _refresh_qrcode(self, token, session_data, client, app_id):
            """刷新二维码的辅助方法"""
            try:
                self.logger.info(f"正在刷新二维码: token={token}, app_id={app_id}")
                
                # 请求新的二维码
                qr_result = client.get_qr(app_id)
                
                if not qr_result or not isinstance(qr_result, dict):
                    self.logger.error(f"刷新二维码失败：无效的响应 - {qr_result}")
                    return {'ret': 500, 'msg': '刷新二维码失败：无效的响应'}
                
                if qr_result.get('ret') != 200:
                    err_msg = qr_result.get('msg', '未知错误')
                    self.logger.error(f"刷新二维码API返回错误: {err_msg}")
                    return {'ret': qr_result.get('ret', 500), 'msg': f'刷新二维码失败: {err_msg}'}
                
                qr_data = qr_result.get('data', {})
                if not qr_data:
                    self.logger.error(f"刷新二维码失败：响应数据为空")
                    return {'ret': 500, 'msg': '刷新二维码失败：响应数据为空'}
                
                # 获取二维码图片的Base64数据
                qr_image = qr_data.get('qrImgBase64', '')
                # 如果返回的是qrData（URL）而不是base64，则需要获取图片
                if not qr_image and qr_data.get('qrData'):
                    try:
                        response = requests.get(qr_data.get('qrData'))
                        if response.status_code == 200:
                            # 将图片转换为base64
                            qr_image = base64.b64encode(response.content).decode('utf-8')
                    except Exception as e:
                        self.logger.error(f"获取刷新二维码图片失败: {str(e)}")
                        return {'ret': 500, 'msg': f'获取刷新二维码图片失败: {str(e)}'}
                
                # 确保二维码数据不包含前缀
                if qr_image and qr_image.startswith('data:'):
                    qr_image = qr_image.split(',', 1)[1]
                
                if not qr_image:
                    self.logger.error(f"刷新二维码失败：无法获取二维码图片数据")
                    return {'ret': 500, 'msg': '刷新二维码失败：无法获取二维码图片数据'}
                
                # 更新会话信息
                session_data['uuid'] = qr_data.get('uuid')
                session_data['expired_time'] = qr_data.get('expiredTime', 60)
                session_data['created_at'] = time.time()
                session_data['last_check'] = time.time()
                session_data['status'] = 'pending'
                
                # 保存会话信息
                self.cache.set(f"login_session_{token}", session_data, timeout=3600)
                
                self.logger.info(f"二维码刷新成功: token={token}, app_id={app_id}")
                
                return {
                    'ret': 201,  # 使用201表示资源已创建
                    'status': 'refresh',
                    'msg': '二维码已刷新',
                    'data': {
                        'qr_image': qr_image,
                        'expired_time': qr_data.get('expiredTime', 60)
                    }
                }
            
            except Exception as e:
                self.logger.error(f"刷新二维码过程中出现异常: {str(e)}")
                import traceback
                self.logger.error(traceback.format_exc())
                return {'ret': 500, 'msg': f'刷新二维码失败: {str(e)}'}

        def _save_bots_info(self):
            """保存机器人信息到文件"""
            try:
                with open('bots.json', 'w', encoding='utf-8') as f:
                    json.dump(self.bots_info, f, indent=2, ensure_ascii=False)
                self.logger.info("机器人信息已保存")
                return True
            except Exception as e:
                self.logger.error(f"保存机器人信息失败: {str(e)}")
                return False

        @self.app.route('/api/confirm_login', methods=['POST'])
        def confirm_login():
            """确认登录API - 统一处理有无验证码的情况"""
            try:
                data = request.json
                token = data.get('token')
                device_info = data.get('deviceInfo', {})
                captcha = data.get('captcha', '')  # 获取可能提交的验证码
                
                if not token:
                    return {'ret': 400, 'msg': '无效的请求参数'}
                
                # 从缓存获取会话信息
                session_data = self.cache.get(f"login_session_{token}")
                if not session_data:
                    return {'ret': 401, 'msg': '登录会话已过期，请重新发起登录'}
                
                # 更新检查时间
                session_data['last_check'] = time.time()
                self.cache.set(f"login_session_{token}", session_data, timeout=3600)
                
                # 如果已经登录成功，直接返回
                if session_data.get('status') == 'online':
                    return {
                        'ret': 200,
                        'status': 'online',
                        'nickname': session_data.get('nickname', '未知用户')
                    }
                
                # 获取临时客户端信息
                wxid = session_data.get('wxid')
                app_id = session_data.get('app_id')
                uuid = session_data.get('uuid')
                client_base_url = session_data.get('client_base_url')
                client_token = session_data.get('client_token')
                
                if not all([wxid, app_id, uuid, client_base_url, client_token]):
                    return {'ret': 500, 'msg': '会话信息不完整，请重新发起登录'}
                
                # 创建临时客户端
                client_tmp = GewechatClient(client_base_url, client_token)
                
                # 记录确认登录请求
                if captcha:
                    self.logger.info(f"用户确认登录并提交验证码: wxid={wxid}, captcha={captcha[:2]}***")
                else:
                    self.logger.info(f"用户确认登录: wxid={wxid}")
                
                try:
                    # 使用check_qr方法，传入验证码（即使是空字符串）
                    login_result = client_tmp.check_qr(app_id, uuid, captcha)
                    
                    if not login_result or not isinstance(login_result, dict):
                        self.logger.error(f"确认登录失败：无效的响应 - {login_result}")
                        return {'ret': 500, 'msg': '确认登录失败：无效的响应'}
                    
                    # 处理API返回的错误
                    if login_result.get('ret') != 200:
                        err_msg = login_result.get('msg', '')
                        self.logger.warning(f"确认登录API返回错误: {err_msg}")
                        
                        # 检查是否需要验证码
                        if "需要输入安全验证码" in err_msg:
                            return {'ret': 403, 'msg': '需要输入验证码', 'status': 'need_captcha'}
                        
                        # 检查验证码是否错误
                        if "验证码错误" in err_msg:
                            return {'ret': 403, 'msg': '验证码错误，请重新输入', 'status': 'captcha_error'}
                        
                        # 检查二维码是否过期
                        if "二维码已过期" in err_msg or "二维码已失效" in err_msg:
                            # 刷新二维码并返回
                            self.logger.info(f"确认登录时发现二维码已过期: {token}")
                            return self._refresh_qrcode(token, session_data, client_tmp, app_id)
                        
                        return {'ret': login_result.get('ret', 500), 'msg': err_msg}
                    
                    # 获取登录结果数据
                    login_data = login_result.get('data', {})
                    
                    if not login_data:
                        self.logger.error(f"确认登录失败：响应数据为空")
                        return {'ret': 500, 'msg': '确认登录失败：响应数据为空'}
                    
                    # 检查登录状态
                    status = login_data.get('status')
                    
                    # 如果登录成功
                    if status == 2:
                        # 获取昵称
                        nickname = login_data.get('nickName', '未知用户')
                        
                        # 更新会话状态
                        session_data['status'] = 'online'
                        session_data['nickname'] = nickname
                        self.cache.set(f"login_session_{token}", session_data, timeout=3600)
                        
                        # 启动机器人
                        self.start_sub_bot(wxid)
                        
                        # 写入登录日志
                        self.logger.info(f"确认登录成功: wxid={wxid}, nickname={nickname}")
                        
                        return {
                            'ret': 200,
                            'status': 'online',
                            'nickname': nickname
                        }
                    elif status == 0:
                        # 等待确认
                        return {'ret': 202, 'msg': '等待确认登录', 'status': 'waiting'}
                    else:
                        # 其他状态
                        return {'ret': 202, 'msg': '登录状态未确认，请重试', 'status': 'unknown'}
                    
                except Exception as e:
                    self.logger.error(f"确认登录过程出现异常: {str(e)}")
                    import traceback
                    self.logger.error(traceback.format_exc())
                    return {'ret': 500, 'msg': f'确认登录失败: {str(e)}'}
                
            except Exception as e:
                self.logger.error(f"处理确认登录请求失败: {str(e)}")
                import traceback
                self.logger.error(traceback.format_exc())
                return {'ret': 500, 'msg': f'处理确认登录请求失败: {str(e)}'}

        @self.app.route('/api/receive_message', methods=['POST'])
        def receive_message():
            try:
                if self.message_handler is None:
                    return {'ret': 500, 'msg': 'Message handler not initialized'}
                message = request.json
                
                # 检查消息格式
                if 'Data' not in message:
                    return {'ret': 400, 'msg': 'Invalid message format'}
                
                # 获取消息所属的微信号和设备ID
                wxid = message.get('Wxid', '')
                app_id = message.get('Appid', '')
                msg_type = message.get('TypeName', '')
                data = message.get('Data', {})

                if self.is_master:
                    # 检查消息类型和来源
                    is_self_message = (wxid == self.wxid or app_id == self.app_id)
                    
                    is_special_message = False
                    if not is_self_message:
                        is_special_message = (
                            msg_type == 'ModContacts' or
                            (msg_type == 'AddMsg' and (
                                # 群公告消息
                                (data.get('MsgType') == 49 and 
                                data.get('Content', {}).get('string', '').find('<type>87</type>') != -1) or
                                # openim消息
                                data.get('Content', {}).get('string', '').find('@openim') != -1
                            ))
                        )

                    # 如果是特殊消息或自己的消息,不进行转发
                    if not (is_special_message or is_self_message):
                        # 查找目标机器人的端口
                        target_port = None
                        if wxid in self.bot_ports:
                            target_port = self.bot_ports[wxid]
                        # elif app_id in self.bot_ports:
                        #     target_port = self.bot_ports[app_id]
                        
                        if target_port:
                            # 转发消息到目标机器人
                            try:
                                forward_url = f"http://127.0.0.1:{target_port}/api/receive_message"
                                headers = {'Content-Type': 'application/json'}
                                response = requests.post(forward_url, json=message, headers=headers)
                                if response.status_code == 200:
                                    self.logger.debug(f"消息已成功转发到端口 {target_port}")
                                else:
                                    self.logger.error(f"消息转发失败: {response.status_code}")
                            except Exception as e:
                                self.logger.error(f"转发消息时发生错误: {str(e)}")
                        else:
                            self.logger.warning(f"未找到目标机器人的端口映射: wxid={wxid}, appid={app_id}")
                    else:
                        # 处理群公告、openim消息或自己的消息
                        self.message_handler.handle_message(message)
                elif wxid == self.wxid or app_id == self.app_id:
                    # 副机器人只处理属于自己的消息
                    self.message_handler.handle_message(message)
                
                return {'ret': 200, 'msg': 'success'}
            except Exception as e:
                self.logger.error(f"处理回调消息失败: {str(e)}")
                return {'ret': 500, 'msg': str(e)}

    def check_and_login(self):
        """检查登录状态并在需要时重新登录"""
        if not self.client:
            self.logger.info("GewechatClient未初始化，跳过登录检查")
            return False
            
        if self.app_id:
            # 检查是否在线
            check_result = self.client.check_online(self.app_id)
            if check_result.get('ret') == 200 and check_result.get('data'):
                # 获取个人信息
                if not self.wxid:
                    profile = self.client.get_profile(self.app_id)
                    if profile.get('ret') == 200 and profile.get('data'):
                        self.wxid = profile['data'].get('wxid')
                        self.nickname = profile['data'].get('nickname', 'YBA')
                        self.save_bot_config()
                self.logger.info(f"已在线，wxid: {self.wxid}, 昵称: {self.nickname}")
                return True
            else:
                # 不在线时更新状态
                self.is_running = False
                self.update_bot_in_bots_info("offline")
                self.logger.info(f"机器人 {self.wxid} 已离线")
                # 如果是副机器人，直接返回False不进行重新登录
                if not self.is_master:
                    return False

        # 需要重新登录（仅主机器人）
        if not self.is_master:
            return False
            
        self.logger.info("开始登录流程")
        app_id, error_msg = self.client.login(self.app_id)
        
        if error_msg:
            self.logger.error(f"登录失败: {error_msg}")
            return False
            
        self.app_id = app_id
        
        # 获取个人信息
        if self.client:
            try:
                profile = self.client.get_profile(self.app_id)
                if profile.get('ret') == 200 and profile.get('data'):
                    self.wxid = profile['data'].get('wxid')
                    self.nickname = profile['data'].get('nickName')
                    self.save_bot_config()
                else:
                    self.logger.error("获取个人信息失败")
                    return False
            except Exception as e:
                self.logger.error(f"获取个人信息过程出错: {str(e)}")
                # 如果是主机器人且离线，尝试重新登录
                if self.is_master:
                    self.logger.info("主机器人离线，尝试重新登录...")
                    try:
                        app_id, error_msg = self.client.login(self.app_id)
                        if error_msg:
                            self.logger.error(f"重新登录失败: {error_msg}")
                            return False
                        self.app_id = app_id
                        # 重新获取个人信息
                        profile = self.client.get_profile(self.app_id)
                        if profile.get('ret') == 200 and profile.get('data'):
                            self.wxid = profile['data'].get('wxid')
                            self.nickname = profile['data'].get('nickName')
                            self.save_bot_config()
                            self.logger.info("重新登录成功")
                        else:
                            self.logger.error("重新登录后获取个人信息失败")
                            return False
                    except Exception as e:
                        self.logger.error(f"重新登录过程出错: {str(e)}")
                        return False
                else:
                    return False
        else:
            self.logger.info("GewechatClient未初始化，跳过获取个人信息")

        return True

    def set_message_callback(self):
        """设置消息回调地址"""
        if not self.client:
            self.logger.info("GewechatClient未初始化，跳过设置回调地址")
            return True
            
        try:
            # 设置回调地址
            try:
                result = self.client.set_callback(self.token, self.callback_url)
                # 直接返回成功，因为如果有错误会抛出异常
                self.logger.info("设置回调地址成功:" + self.callback_url)
                return True
            except RuntimeError as e:
                self.logger.error(f"设置回调地址请求失败: {str(e)}")
                return False
                
        except Exception as e:
            self.logger.error(f"设置回调地址过程出错: {str(e)}")
            return False

    def start_http_server(self):
        """启动HTTP服务器"""
        try:
            port = self.callback_port
            
            # 禁用访问日志
            werkzeug_logger = logging.getLogger('werkzeug')
            werkzeug_logger.disabled = True
            
            def run_flask():
                from werkzeug.serving import run_simple
                run_simple('0.0.0.0', port, self.app, threaded=True)
            
            thread = Thread(target=run_flask)
            thread.daemon = True  # 设置为守护线程
            thread.start()
            self.logger.info(f"HTTP服务器已启动在端口 {port}")
            return True
        except Exception as e:
            self.logger.error(f"启动HTTP服务器失败: {str(e)}")
            return False

    def _register_all_sub_bots_ports(self):
        """注册所有副机器人的端口（仅主机器人）"""
        if not self.is_master:
            return
            
        try:
            # 遍历所有副机器人配置
            for wxid, bot_info in self.bots_info.items():
                config_path = f"bot_configs/{wxid}.json"
                if os.path.exists(config_path):
                    with open(config_path, 'r', encoding='utf-8') as f:
                        config = json.load(f)
                        port = config.get('callback_port')
                        app_id = config.get('appid')
                        if port:
                            self.register_bot_port(wxid, port)
                            # if app_id:
                            #     self.register_bot_appid(app_id, port)
            self.logger.info("已注册所有副机器人的端口映射")
        except Exception as e:
            self.logger.error(f"注册副机器人端口时出错: {str(e)}")

    def create_sub_bot(self, wxid, base_url=None):
        """创建副机器人
        
        Args:
            wxid: 机器人的wxid
            base_url: 基础URL，格式为 "http://ip:port/v2/api"，如果不提供则使用主机器人的base_url
            
        Returns:
            bool: 是否创建成功
        """
        if not self.is_master:
            self.logger.error("只有主机器人可以创建副机器人")
            return False
            
        # 检查机器人是否已存在
        if wxid in self.bots_info:
            self.logger.info(f"机器人 {wxid} 已存在，更新配置")
            
        # 创建副机器人配置
        os.makedirs("bot_configs", exist_ok=True)
        config_path = f"bot_configs/{wxid}.json"
        
        # 计算新端口
        new_port = self._calculate_sub_bot_port(self.callback_port)
        
        # 准备基本配置
        config = {
            "wxid": wxid,
            "appid": "",
            "token": "",
            "nickname": f"副机器人_{wxid[-4:]}",
            "base_url": base_url if base_url else "",
            "callback_url": self.callback_base_url,
            "callback_port": new_port,
            "ext_url": self.ext_url  # 直接使用主机器人的 ext_url
        }
        
        # 如果配置文件已存在,读取并保留某些字段
        if os.path.exists(config_path):
            try:
                with open(config_path, 'r', encoding='utf-8') as f:
                    old_config = json.load(f)
                    # 保留原有的 appid、token 和 nickname
                    config["appid"] = old_config.get("appid", "")
                    config["token"] = old_config.get("token", "")
                    config["nickname"] = old_config.get("nickname", config["nickname"])
            except Exception as e:
                self.logger.error(f"读取原配置失败: {str(e)}")
        
        # 保存配置
        try:
            with open(config_path, 'w', encoding='utf-8') as f:
                json.dump(config, f, indent=2, ensure_ascii=False)
        except Exception as e:
            self.logger.error(f"保存副机器人配置失败: {str(e)}")
            return False
            
        # 添加或更新机器人信息
        current_time = time.strftime("%Y-%m-%d %H:%M:%S", time.localtime())
        if wxid not in self.bots_info:
            self.bots_info[wxid] = {
                "nickname": config["nickname"],
                "created_at": current_time,
                "effective_date": current_time,
                "expiry_date": "", # 默认不设置过期时间
                "status": "created",
                "base_url": config["base_url"],  # 添加base_url到机器人信息中
                "show_ads": False  # 默认关闭广告
            }
        else:
            # 更新现有机器人信息
            self.bots_info[wxid].update({
                "nickname": config["nickname"],
                "last_updated": current_time,
                "base_url": config["base_url"]
            })
            # 如果没有show_ads字段，添加默认值
            if "show_ads" not in self.bots_info[wxid]:
                self.bots_info[wxid]["show_ads"] = False
        self.save_bots_info()
        
        self.logger.info(f"已{'更新' if wxid in self.bots_info else '创建'}副机器人 {wxid}，使用base_url: {config['base_url']}")
        return True

    def is_bot_running(self, wxid):
        """检查机器人是否在运行
        
        Args:
            wxid: 机器人的wxid
            
        Returns:
            bool: 是否在运行
        """
        try:
            # 获取机器人的端口
            port = None
            if wxid in self.bot_ports:
                port = self.bot_ports[wxid]
            else:
                # 从配置文件获取端口
                config_path = f"bot_configs/{wxid}.json"
                if os.path.exists(config_path):
                    with open(config_path, 'r', encoding='utf-8') as f:
                        config = json.load(f)
                        port = config.get('callback_port')
            
            if not port:
                return False
                
            # 尝试访问机器人的健康检查接口
            url = f"http://127.0.0.1:{port}/api/receive_message"
            response = requests.post(url, json={"Data": {}}, timeout=5)
            return response.status_code == 200
        except:
            return False

    def start_sub_bot(self, wxid):
        """启动副机器人"""
        # 检查机器人是否存在
        if wxid not in self.bots_info:
            self.logger.error(f"机器人 {wxid} 不存在，请先创建")
            return False
            
        # 检查机器人是否已在运行
        if self.is_bot_running(wxid):
            self.logger.warning(f"机器人 {wxid} 已在运行")
            return True
        
        # 检查机器人是否已过期
        if self.bots_info[wxid].get("expiry_date"):
            expiry_date = self.bots_info[wxid]["expiry_date"]
            try:
                expiry_time = time.mktime(time.strptime(expiry_date, "%Y-%m-%d %H:%M:%S"))
                current_time = time.time()
                if current_time > expiry_time:
                    self.logger.error(f"机器人 {wxid} 已过期，无法启动")
                    return False
            except Exception as e:
                self.logger.error(f"检查机器人过期时间出错: {str(e)}")
            
        # 启动副机器人进程
        try:
            cmd = [sys.executable, "bot.py", wxid]
            process = subprocess.Popen(cmd)
            self.sub_bots[wxid] = process
            
            # 等待机器人启动
            max_wait = 30  # 最多等待30秒
            wait_interval = 2  # 每2秒检查一次
            for _ in range(max_wait // wait_interval):
                if self.is_bot_running(wxid):
                    # 更新状态
                    self.bots_info[wxid]["status"] = "running"
                    self.save_bots_info()
                    
                    # 确保端口映射已注册
                    config_path = f"bot_configs/{wxid}.json"
                    if os.path.exists(config_path):
                        with open(config_path, 'r', encoding='utf-8') as f:
                            config = json.load(f)
                            port = config.get('callback_port')
                            app_id = config.get('appid')
                            if port:
                                self.register_bot_port(wxid, port)
                                # if app_id:
                                #     self.register_bot_appid(app_id, port)
                    
                    self.logger.info(f"已启动副机器人 {wxid}")
                    return True
                time.sleep(wait_interval)
            
            # 如果超时未启动，终止进程
            process.terminate()
            process.wait(timeout=5)
            self.logger.error(f"启动副机器人 {wxid} 超时")
            return False
            
        except Exception as e:
            self.logger.error(f"启动副机器人失败: {str(e)}")
            return False

    def stop_sub_bot(self, wxid):
        """停止副机器人"""
        if not self.is_master:
            self.logger.error("只有主机器人可以停止副机器人")
            return False
            
        # 检查机器人是否在运行
        if not self.is_bot_running(wxid):
            self.logger.warning(f"机器人 {wxid} 不在运行")
            
            # 更新状态
            if wxid in self.bots_info:
                self.bots_info[wxid]["status"] = "stopped"
                self.save_bots_info()
                
            # 删除端口映射
            if wxid in self.bot_ports:
                del self.bot_ports[wxid]
                self.logger.info(f"已删除机器人 {wxid} 的端口映射")
                
            return True
            
        # 终止进程
        try:
            if wxid in self.sub_bots:
                self.sub_bots[wxid].terminate()
                self.sub_bots[wxid].wait(timeout=30)
            
            # 等待机器人完全停止
            max_wait = 30  # 最多等待30秒
            wait_interval = 2  # 每2秒检查一次
            for _ in range(max_wait // wait_interval):
                if not self.is_bot_running(wxid):
                    # 更新状态
                    self.bots_info[wxid]["status"] = "stopped"
                    self.save_bots_info()
                    
                    # 删除端口映射
                    if wxid in self.bot_ports:
                        del self.bot_ports[wxid]
                        self.logger.info(f"已删除机器人 {wxid} 的端口映射")
                    
                    self.logger.info(f"已停止副机器人 {wxid}")
                    return True
                time.sleep(wait_interval)
            
            # 如果超时未停止，强制结束进程
            if wxid in self.sub_bots:
                self.sub_bots[wxid].kill()
            self.logger.error(f"停止副机器人 {wxid} 超时")
            return False
            
            
        except Exception as e:
            self.logger.error(f"停止副机器人失败: {str(e)}")
            return False

    def set_bot_expiry(self, wxid, expiry_date):
        """设置机器人过期时间"""
        if not self.is_master:
            self.logger.error("只有主机器人可以设置机器人过期时间")
            return False
        
        # 检查机器人是否存在
        if wxid not in self.bots_info:
            self.logger.error(f"机器人 {wxid} 不存在")
            return False
        
        # 设置过期时间
        try:
            self.bots_info[wxid]["expiry_date"] = expiry_date
            self.save_bots_info()
            self.logger.info(f"已设置机器人 {wxid} 的过期时间为 {expiry_date}")
            
            # 如果机器人已过期，停止它
            if expiry_date:
                try:
                    expiry_time = time.mktime(time.strptime(expiry_date, "%Y-%m-%d %H:%M:%S"))
                    current_time = time.time()
                    if current_time > expiry_time:
                        self.stop_sub_bot(wxid)
                except Exception as e:
                    self.logger.error(f"检查并停止过期机器人出错: {str(e)}")
            
            return True
        except Exception as e:
            self.logger.error(f"设置机器人过期时间出错: {str(e)}")
            return False

    def get_qrcode_for_bot(self, client_tmp, wxid):
        """获取指定机器人的登录二维码"""
        if not self.is_master:
            self.logger.error("只有主机器人可以获取登录二维码")
            return None
        
        if not self.client:
            self.logger.error("GewechatClient未初始化，无法获取登录二维码")
            return None
            
        # 检查机器人是否存在
        if wxid not in self.bots_info:
            self.logger.error(f"机器人 {wxid} 不存在")
            return None
        
        # 获取配置
        try:
            config_path = f"bot_configs/{wxid}.json"
            with open(config_path, 'r', encoding='utf-8') as f:
                config = json.load(f)

            if config.get('appid'):
                check_online_response = client_tmp.check_online(config.get('appid'))
                if check_online_response.get('ret') == 200 and check_online_response.get('data'):
                    print_green(f"AppID: {input_app_id} 已在线，无需登录")
                    client_tmp.post_text(config.get('appid'), wxid, f"已在线，无需登录")
                    return

            # 获取登录二维码
            qr_result = client_tmp.get_qr(config.get('appid', ''))
            print(f"qr_result: {qr_result}")
            if qr_result and isinstance(qr_result, dict) and qr_result.get('ret') == 200:
                qr_data = qr_result.get('data', {})
                if qr_data:
                    # 更新 appid
                    config['appid'] = qr_data.get('appId', config['appid'])
                    with open(config_path, 'w', encoding='utf-8') as f:
                        json.dump(config, f, indent=2, ensure_ascii=False)
                    
                    # 返回二维码数据
                    return {
                        'qr_data': qr_data.get('qrData'),
                        'qr_image': qr_data.get('qrImgBase64'),
                        'app_id': qr_data.get('appId'),
                        'uuid': qr_data.get('uuid')
                    }
            self.logger.error("获取登录二维码失败")
            return None
        except Exception as e:
            self.logger.error(f"获取登录二维码过程出错: {str(e)}")
            return None

    def send_qrcode_to_bot(self, wxid, target_wxid):
        """发送登录二维码给指定微信用户"""
        if not self.is_master:
            self.logger.error("只有主机器人可以发送登录二维码")
            return False
            
        if not self.client:
            self.logger.error("GewechatClient未初始化，无法发送登录二维码")
            return False

        config_path = f"bot_configs/{wxid}.json"
        with open(config_path, 'r', encoding='utf-8') as f:
            config = json.load(f)
        
        # 创建 GewechatClient 实例
        client_tmp = None
        if config.get('base_url'):
            client_tmp = GewechatClient(config.get('base_url'), config.get('token'))
            
            # 如果未配置token，尝试获取token
            if not config.get('token') and config.get('base_url'):
                self.logger.info("未配置token，正在获取...")
                try:
                    token_result = client_tmp.get_token()
                    print(token_result)
                    if token_result.get('ret') == 200 and token_result.get('data'):
                        config['token'] = token_result['data']
                        self.logger.info("成功获取token")
                        self.save_bot_config()  # 保存新获取的token
                        # 重新创建 GewechatClient 实例
                        client_tmp = GewechatClient(config.get('base_url'), config.get('token'))
                    else:
                        self.logger.error("获取token失败")
                except Exception as e:
                    self.logger.error(f"获取token时发生错误: {str(e)}")
        else:
            self.logger.error("base_url为空，无法获取登录二维码")
            return None

        # 获取二维码
        qrcode_info = self.get_qrcode_for_bot(client_tmp, wxid)
        if not qrcode_info:
            return False
        
        # 发送二维码
        try:
            # 确保目录存在
            os.makedirs(self.image_dir, exist_ok=True)
            
            # 如果已经有二维码图片，直接使用
            qr_image = qrcode_info.get("qr_image")
            if qr_image:
                # 处理可能包含data URI前缀的base64数据
                if qr_image.startswith('data:'):
                    # 移除data URI前缀
                    qr_image = qr_image.split(',', 1)[1]
                # 解码base64图片数据
                image_data = base64.b64decode(qr_image)
                output_path = os.path.join(self.image_dir, f"{wxid}.jpeg")
                with open(output_path, 'wb') as f:
                    f.write(image_data)
            else:
                # 如果没有base64图片，尝试从URL获取
                qr_data = qrcode_info.get("qr_data")
                if not qr_data:
                    self.logger.error("无法获取二维码数据")
                    return False
                    
                response = requests.get(qr_data)
                if response.status_code != 200:
                    self.logger.error(f"获取二维码图片失败: {response.status_code}")
                    return False
                    
                output_path = os.path.join(self.image_dir, f"{wxid}.jpeg")
                with open(output_path, 'wb') as f:
                    f.write(response.content)
            
            # 发送图片 - 构建相对路径URL
            relative_path = f"{wxid}.jpeg" if self.is_master else f"{self.bot_id}/{wxid}.jpeg"
            image_url = f"{self.ext_url}/product_qr_img/{relative_path}"
            result = self.client.post_image(self.app_id, target_wxid, image_url)

            # 3. 轮询检查登录状态
            retry_count = 0
            max_retries = 10  # 最大重试10次
            
            while retry_count < max_retries:
                login_status = client_tmp.check_qr(qrcode_info["app_id"], qrcode_info["uuid"], "")
                if login_status.get('ret') != 200:
                    print_red(f"检查登录状态失败: {login_status}")
                    if login_status.get('data').get('msg') == "本次登录需要输入安全验证码":
                        pass
                    else:
                        return False

                login_data = login_status.get('data', {})
                status = login_data.get('status')
                expired_time = login_data.get('expiredTime', 0)
                
                # 检查二维码是否过期，提前5秒重新获取
                if expired_time <= 5:
                    self.client.post_text(self.app_id, target_wxid, "二维码即将过期，正在重新获取...")
                    # 获取二维码
                    qrcode_info = self.get_qrcode_for_bot(client_tmp, wxid)
                    if not qrcode_info:
                        return False

                    # 确保目录存在
                    os.makedirs(self.image_dir, exist_ok=True)
                    
                    # 如果已经有二维码图片，直接使用
                    qr_image = qrcode_info.get("qr_image")
                    if qr_image:
                        # 处理可能包含data URI前缀的base64数据
                        if qr_image.startswith('data:'):
                            # 移除data URI前缀
                            qr_image = qr_image.split(',', 1)[1]
                        # 解码base64图片数据
                        image_data = base64.b64decode(qr_image)
                        output_path = os.path.join(self.image_dir, f"{wxid}.jpeg")
                        with open(output_path, 'wb') as f:
                            f.write(image_data)
                    else:
                        # 如果没有base64图片，尝试从URL获取
                        qr_data = qrcode_info.get("qr_data")
                        if not qr_data:
                            self.logger.error("无法获取二维码数据")
                            return False
                            
                        response = requests.get(qr_data)
                        if response.status_code != 200:
                            self.logger.error(f"获取二维码图片失败: {response.status_code}")
                            return False
                            
                        output_path = os.path.join(self.image_dir, f"{wxid}.jpeg")
                        with open(output_path, 'wb') as f:
                            f.write(response.content)
                    
                    # 发送图片 - 构建相对路径URL
                    relative_path = f"{wxid}.jpeg" if self.is_master else f"{self.bot_id}/{wxid}.jpeg"
                    image_url = f"{self.ext_url}/product_qr_img/{relative_path}"
                    result = self.client.post_image(self.app_id, target_wxid, image_url)
                    continue

                if status == 2:  # 登录成功
                    # self.nickname = login_data.get('nickName', '未知用户')
                    # print_green(f"\n登录成功！用户昵称: {self.nickname}")
                    # self.save_bots_info()
                    self.start_sub_bot(wxid)
                    return True
                else:
                    retry_count += 1
                    if retry_count >= max_retries:
                        self.client.post_text(self.app_id, target_wxid, "登录超时，请重新尝试")
                        return False
                    time.sleep(5)

        except Exception as e:
            self.logger.error(f"发送登录二维码过程出错: {str(e)}")
            return False

    def send_command_to_sub_bot(self, wxid, command):
        """向副机器人发送控制命令"""
        if not self.is_master:
            self.logger.error("只有主机器人可以向副机器人发送命令")
            return False
            
        if not self.client:
            self.logger.error("GewechatClient未初始化，无法发送命令")
            return False
        
        # 检查机器人是否存在
        if wxid not in self.bots_info:
            self.logger.error(f"机器人 {wxid} 不存在")
            return False
        
        # 发送命令消息
        try:
            # 消息格式: "BOTCMD:{command}"
            message = f"/bot {command}"
            result = self.client.post_text(self.app_id, wxid, message)
            
            if result.get('ret') == 200:
                self.logger.info(f"已发送命令到副机器人 {wxid}: {command}")
                return True
            else:
                self.logger.error(f"发送命令失败: {result}")
                return False
        except Exception as e:
            self.logger.error(f"发送命令过程出错: {str(e)}")
            return False

    def register_bot_port(self, wxid, port):
        """注册机器人端口映射（仅主机器人）"""
        if not self.is_master:
            return False
        self.bot_ports[wxid] = port
        self.logger.info(f"已注册机器人端口映射: {wxid} -> {port}")
        return True

    def register_bot_appid(self, appid, port):
        """注册机器人AppID端口映射（仅主机器人）"""
        if not self.is_master:
            return False
        self.bot_ports[appid] = port
        self.logger.info(f"已注册机器人AppID端口映射: {appid} -> {port}")
        return True

    def start_udp_listener(self):
        """启动UDP监听线程"""
        def udp_listener():
            try:
                # 创建UDP socket
                sock = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
                sock.setsockopt(socket.SOL_SOCKET, socket.SO_REUSEADDR, 1)
                sock.bind(('', 12580))  # 绑定到所有网络接口
                
                self.logger.info("UDP监听已启动")
                
                while self.is_running:
                    try:
                        # 接收数据
                        data, addr = sock.recvfrom(65535)
                        chunk_data = json.loads(data.decode('utf-8'))
                        
                        # 获取分片信息
                        chunk_id = chunk_data['chunk_id']
                        total_chunks = chunk_data['total_chunks']
                        chunk = base64.b64decode(chunk_data['data'])
                        
                        # 生成消息ID（使用时间戳）
                        msg_id = f"{time.time()}_{total_chunks}"
                        
                        # 存储分片
                        if msg_id not in self.udp_buffer:
                            self.udp_buffer[msg_id] = {}
                        self.udp_buffer[msg_id][chunk_id] = chunk
                        
                        # 检查是否收到所有分片
                        if len(self.udp_buffer[msg_id]) == total_chunks:
                            # 重组完整消息
                            complete_data = b''
                            for i in range(total_chunks):
                                complete_data += self.udp_buffer[msg_id][i]
                            
                            # 删除缓存
                            del self.udp_buffer[msg_id]
                            
                            # 处理消息
                            try:
                                message = json.loads(complete_data.decode('utf-8'))
                                if message.get('Wxid') == 'ALL' or message.get('Wxid') == self.bot_id:
                                    if message.get('TypeName') == 'NotifyInfo':
                                        self.message_handler.handle_notify_info(message)
                                    elif message.get('TypeName') == 'MiniAppXml':
                                        # 处理转发消息
                                        xml_content = message.get('Data', {}).get('xml')
                                        if xml_content and self.client:
                                            # 解析XML获取目标群
                                            try:
                                                # root = ET.fromstring(xml_content)
                                                # appmsg = root.find('appmsg')
                                                # if appmsg is not None:
                                                #     title = appmsg.find('title').text
                                                #     # 从标题中提取店铺名称
                                                #     store_match = re.search(r'【(.+?)】', title)
                                                #     if store_match:
                                                #         store_name = store_match.group(1)
                                                #         # 查找对应的群ID
                                                #         if self.message_handler:
                                                #             store_name, store_id = self.message_handler._find_best_match_store(store_name)
                                                #             if store_id:
                                                #                 group_id = None
                                                #                 # 从数据库获取群ID
                                                #                 with self.message_handler.db.get_connection() as conn:
                                                #                     cursor = conn.cursor()
                                                #                     cursor.execute('''
                                                #                         SELECT group_id
                                                #                         FROM store_groups
                                                #                         WHERE store_id = ?
                                                #                     ''', (store_id,))
                                                #                     result = cursor.fetchone()
                                                #                     if result:
                                                #                         group_id = result[0]
                                                                
                                                if self.master_group:
                                                    # 转发小程序到对应的群
                                                    self.client.forward_mini_app(
                                                        self.app_id,
                                                        self.master_group,
                                                        xml_content,
                                                        ''
                                                    )
                                            except Exception as e:
                                                self.logger.error(f"处理XML内容失败: {str(e)}")
                                    elif message.get('TypeName') == 'PostText':
                                        msg_content = message.get('Data', {}).get('msg')
                                        if msg_content and self.client:
                                            try:
                                                if self.master_group:
                                                    self.client.post_text(
                                                        self.app_id,
                                                        self.master_group,
                                                        msg_content
                                                    )
                                            except Exception as e:
                                                self.logger.error(f"处理MSG内容失败: {str(e)}")
                            except Exception as e:
                                self.logger.error(f"处理UDP消息失败: {str(e)}")
                            
                        # 清理过期的缓存（超过30秒的缓存）
                        current_time = time.time()
                        expired_msgs = [msg_id for msg_id in self.udp_buffer
                                      if float(msg_id.split('_')[0]) < current_time - 30]
                        for msg_id in expired_msgs:
                            del self.udp_buffer[msg_id]
                            
                    except Exception as e:
                        self.logger.error(f"UDP接收数据失败: {str(e)}")
                        continue
                        
            except Exception as e:
                self.logger.error(f"UDP监听线程异常: {str(e)}")
            finally:
                sock.close()
                
        # 启动监听线程
        thread = Thread(target=udp_listener)
        thread.daemon = True
        thread.start()
        self.logger.info("UDP监听线程已启动")

    def run(self):
        """运行机器人"""
        try:
            if not self.token and self.client:
                self.logger.error("token未配置")
                return
            
            # 启动HTTP服务器
            if not self.start_http_server():
                return
                
            # 等待HTTP服务器完全启动
            self.logger.info("等待HTTP服务器启动...")
            time.sleep(2)
            
            # 创建消息处理器
            self.logger.info("初始化消息处理器...")
            self.message_handler = MessageHandler(self)

            # 设置消息回调地址
            if not self.set_message_callback():
                # return
                pass
            time.sleep(1)
            
            # 标记为运行中
            self.is_running = True
            self.update_bot_in_bots_info("running")

            # 获取个人信息
            if self.client:
                try:
                    profile = self.client.get_profile(self.app_id)
                    if profile.get('ret') == 200 and profile.get('data'):
                        self.wxid = profile['data'].get('wxid')
                        self.nickname = profile['data'].get('nickName')
                        self.save_bot_config()
                    else:
                        self.logger.error("获取个人信息失败")
                        return False
                except Exception as e:
                    self.logger.error(f"获取个人信息过程出错: {str(e)}")
                    # 如果是主机器人且离线，尝试重新登录
                    if self.is_master:
                        self.logger.info("主机器人离线，尝试重新登录...")
                        try:
                            app_id, error_msg = self.client.login(self.app_id)
                            if error_msg:
                                self.logger.error(f"重新登录失败: {error_msg}")
                                return False
                            self.app_id = app_id
                            # 重新获取个人信息
                            profile = self.client.get_profile(self.app_id)
                            if profile.get('ret') == 200 and profile.get('data'):
                                self.wxid = profile['data'].get('wxid')
                                self.nickname = profile['data'].get('nickName')
                                self.save_bot_config()
                                self.logger.info("重新登录成功")
                            else:
                                self.logger.error("重新登录后获取个人信息失败")
                                return False
                        except Exception as e:
                            self.logger.error(f"重新登录过程出错: {str(e)}")
                            return False
                    else:
                        return False
            else:
                self.logger.info("GewechatClient未初始化，跳过获取个人信息")

            # 如果是主机器人，启动所有副机器人
            if self.is_master:
                self.logger.info("主机器人启动完成，开始启动副机器人...")
                # 遍历所有副机器人
                for wxid, bot_info in self.bots_info.items():
                    # 检查机器人状态
                    if bot_info.get("status") == "":
                        continue
                    self.logger.info(f"正在启动副机器人 {wxid}...")
                    if self.start_sub_bot(wxid):
                        self.logger.info(f"副机器人 {wxid} 启动成功")
                    else:
                        self.logger.error(f"副机器人 {wxid} 启动失败")
                    # 每个机器人启动后等待一段时间，避免同时启动造成资源竞争
                    time.sleep(1)
                self.logger.info("所有副机器人启动完成")

            # 登录检查
            while self.is_running:
                try:
                    if not self.is_master:
                        self.bots_info = self.load_bots_info()
                        if self.bots_info.get(self.bot_id).get("status") != "running":
                            self.logger.info(f"机器人 {self.bot_id} 未运行，程序退出")
                            self.is_running = False
                            break
                    if self.client and not self.check_and_login():
                        time.sleep(60)  # 登录失败后等待一分钟再试
                        continue
                    
                    # 检查是否已过期
                    if not self.is_master and self.bot_id in self.bots_info:
                        expiry_date = self.bots_info[self.bot_id].get("expiry_date")
                        if expiry_date:
                            try:
                                expiry_time = time.mktime(time.strptime(expiry_date, "%Y-%m-%d %H:%M:%S"))
                                current_time = time.time()
                                if current_time > expiry_time:
                                    self.logger.info(f"机器人 {self.bot_id} 已过期，程序退出")
                                    # 更新状态
                                    self.is_running = False
                                    self.update_bot_in_bots_info("expired")
                                    # 发送通知
                                    try:
                                        if self.client:
                                            master_wxid = next(iter(self.master_wxid), None) if isinstance(self.master_wxid, list) else None
                                            if master_wxid:
                                                self.client.post_text(self.app_id, master_wxid, f"机器人 {self.bot_id} 已过期，程序退出")
                                    except:
                                        pass
                                    return
                            except Exception as e:
                                self.logger.error(f"检查机器人过期时间出错: {str(e)}")
                    
                    # 维护VIP用户数据 - 每小时执行一次
                    if self.is_master and hasattr(self, 'message_handler') and hasattr(self.message_handler, 'maintain_vip_users'):
                        current_hour = time.localtime().tm_hour
                        # 使用静态变量存储上次执行的小时
                        if not hasattr(self, '_last_vip_check_hour'):
                            self._last_vip_check_hour = -1
                        
                        # 如果小时发生变化，执行维护
                        if current_hour != self._last_vip_check_hour:
                            self.logger.info(f"开始执行VIP用户维护，当前小时: {current_hour}")
                            self.message_handler.maintain_vip_users()
                            self._last_vip_check_hour = current_hour
                    
                    time.sleep(300)  # 每5分钟检查一次在线状态
                except KeyboardInterrupt:
                    self.logger.info("收到终止信号，正在退出...")
                    self.is_running = False
                    # self.update_bot_in_bots_info("stopped")
                    break
                except Exception as e:
                    self.logger.error(f"运行出错: {str(e)}")
                    time.sleep(60)  # 发生错误时等待一分钟再继续
                
            # 如果是主机器人，停止所有副机器人
            if self.is_master and hasattr(self, 'sub_bots'):
                self.logger.info("正在停止所有副机器人...")
                for wxid, process in self.sub_bots.items():
                    try:
                        # 先尝试正常终止
                        process.terminate()
                        process.wait(timeout=30)
                        self.logger.info(f"副机器人 {wxid} 已正常停止")
                    except Exception as e:
                        self.logger.error(f"停止副机器人 {wxid} 失败: {str(e)}")
                        try:
                            # 如果正常终止失败，强制结束进程
                            process.kill()
                            self.logger.info(f"副机器人 {wxid} 已被强制终止")
                        except:
                            self.logger.error(f"强制终止副机器人 {wxid} 失败")
            
            # # 执行登出操作
            # if self.client and self.app_id and not self.is_master:
            #     try:
            #         self.logger.info("正在执行登出操作...")
            #         result = self.client.logout(self.app_id)
            #         if result.get('ret') == 200:
            #             self.logger.info("登出成功")
            #         else:
            #             self.logger.error(f"登出失败: {result}")
            #     except Exception as e:
            #         self.logger.error(f"登出过程出错: {str(e)}")
                    
        except KeyboardInterrupt:
            self.logger.info("程序已停止")
            # 标记为已停止
            self.is_running = False
            # self.update_bot_in_bots_info("stopped")
            
            # 如果是主机器人，停止所有副机器人
            if self.is_master and hasattr(self, 'sub_bots'):
                self.logger.info("正在停止所有副机器人...")
                for wxid, process in self.sub_bots.items():
                    try:
                        # 先尝试正常终止
                        process.terminate()
                        process.wait(timeout=30)
                        self.logger.info(f"副机器人 {wxid} 已正常停止")
                    except Exception as e:
                        self.logger.error(f"停止副机器人 {wxid} 失败: {str(e)}")
                        try:
                            # 如果正常终止失败，强制结束进程
                            process.kill()
                            self.logger.info(f"副机器人 {wxid} 已被强制终止")
                        except:
                            self.logger.error(f"强制终止副机器人 {wxid} 失败")
            
            # # 执行登出操作
            # if self.client and self.app_id and not self.is_master:
            #     try:
            #         self.logger.info("正在执行登出操作...")
            #         result = self.client.logout(self.app_id)
            #         if result.get('ret') == 200:
            #             self.logger.info("登出成功")
            #         else:
            #             self.logger.error(f"登出失败: {result}")
            #     except Exception as e:
            #         self.logger.error(f"登出过程出错: {str(e)}")
                    
        except Exception as e:
            self.logger.error(f"运行出错: {str(e)}")
            # 标记为已停止
            self.is_running = False
            # self.update_bot_in_bots_info("error")

    def set_show_ads(self, show_ads):
        """设置广告开关"""
        self.show_ads = show_ads
        # 更新机器人信息
        if self.wxid and self.wxid in self.bots_info:
            self.bots_info[self.wxid]["show_ads"] = show_ads
            self.save_bots_info()
            return True
        return False

    def is_show_ads(self):
        """获取广告开关状态"""
        return self.show_ads

if __name__ == "__main__":
    # 支持命令行参数指定机器人ID
    bot_id = sys.argv[1] if len(sys.argv) > 1 else None
    bot = WeChatBot(bot_id)
    bot.run()
