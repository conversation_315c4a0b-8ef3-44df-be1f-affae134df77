# YBA监控管理系统启动指南

## 🚀 快速启动

### 1. 环境配置

首先配置环境变量：

```bash
# 交互式配置
python setup_env.py

# 或者直接创建配置
python setup_env.py create
```

### 2. 启动系统

```bash
# 统一启动脚本（推荐）
python main.py
```

## 📁 启动文件说明

### 新的统一启动系统

- **`main.py`** - 统一启动脚本（推荐使用）
  - 自动加载.env环境变量
  - 启动Web服务
  - 检查登录状态
  - 定期更新数据库登录状态
  - 优雅关闭处理

- **`setup_env.py`** - 环境配置工具
  - 交互式创建.env文件
  - 验证配置
  - 测试数据库连接

### 旧的启动文件（已整合）

以下文件功能已整合到`main.py`中：

- ~~`run.py`~~ - 基础Flask启动
- ~~`start.py`~~ - 带环境检查的启动
- ~~`quick_start.py`~~ - 快速启动脚本
- ~~`check_login_status.py`~~ - 登录状态检查工具

## ⚙️ 配置选项

### 环境变量配置

在`.env`文件中配置以下选项：

```bash
# Flask配置
SECRET_KEY=your-secret-key-change-this-in-production
FLASK_ENV=development
FLASK_DEBUG=true
FLASK_HOST=0.0.0.0
FLASK_PORT=5000

# 数据库配置
MYSQL_HOST=localhost
MYSQL_PORT=3306
MYSQL_USER=your-mysql-user
MYSQL_PASSWORD=your-mysql-password
MYSQL_DB=yba_monitor
MYSQL_CHARSET=utf8mb4

# 管理员配置
ADMIN_TOKEN=your-admin-token

# 系统配置
LOGIN_CHECK_INTERVAL=300        # 登录检查间隔（秒）
ENABLE_PERIODIC_CHECK=true      # 是否启用定期检查
```

## 🔧 功能特性

### 统一启动脚本功能

1. **环境变量加载**
   - 自动加载.env文件
   - 验证必需的环境变量
   - 提供友好的错误提示

2. **数据库连接测试**
   - 启动时测试数据库连接
   - 连接失败时给出明确提示

3. **Web服务启动**
   - 启动Flask Web服务
   - 支持自定义主机和端口
   - 支持调试模式

4. **登录状态检查**
   - 启动时检查所有代理的微信登录状态
   - 定期检查并更新数据库状态
   - 可配置检查间隔

5. **后台任务管理**
   - 独立线程运行定期检查
   - 优雅关闭处理
   - 信号处理支持

6. **日志记录**
   - 详细的启动日志
   - 文件和控制台双重输出
   - 错误和警告提示

## 📋 使用示例

### 首次使用

```bash
# 1. 配置环境
python setup_env.py

# 2. 启动系统
python main.py
```

### 日常使用

```bash
# 直接启动
python main.py
```

### 配置管理

```bash
# 验证配置
python setup_env.py validate

# 测试数据库
python setup_env.py test-db

# 重新配置
python setup_env.py create
```

## 🛠️ 故障排除

### 常见问题

1. **环境变量缺失**
   ```
   错误: 缺少必需的环境变量: MYSQL_USER, MYSQL_PASSWORD
   解决: 运行 python setup_env.py 配置环境变量
   ```

2. **数据库连接失败**
   ```
   错误: 数据库连接测试失败
   解决: 检查数据库配置和服务状态
   ```

3. **端口被占用**
   ```
   错误: [Errno 98] Address already in use
   解决: 修改FLASK_PORT或停止占用端口的进程
   ```

### 调试模式

启用调试模式获取更多信息：

```bash
# 在.env中设置
FLASK_DEBUG=true

# 或临时设置
FLASK_DEBUG=true python main.py
```

## 🔄 迁移指南

### 从旧启动文件迁移

如果您之前使用其他启动文件，请：

1. **备份现有配置**
   ```bash
   # 自动备份旧启动文件
   python cleanup_old_startup.py
   ```

2. **配置环境变量**
   ```bash
   python setup_env.py
   ```

3. **启动新系统**
   ```bash
   python main.py
   ```

4. **验证功能**
   - 检查Web服务是否正常启动
   - 验证登录状态检查是否工作
   - 确认VIP管理功能正常

### 配置迁移

- 现有的.env配置会自动保留
- 数据库连接无需重新配置
- 代理商设置保持不变

### 清理旧文件

运行清理脚本安全移除旧启动文件：

```bash
python cleanup_old_startup.py
```

此脚本会将旧文件移动到`backup_startup_files`目录，而不是删除它们。

## 📞 支持

如有问题，请检查：

1. 日志文件 `app.log`
2. 环境变量配置
3. 数据库连接状态
4. 网络和防火墙设置
