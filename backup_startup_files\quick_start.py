#!/usr/bin/env python3
"""
YBA监控管理系统快速启动脚本
"""
import os
import sys
import subprocess
import logging
from app.utils.env_loader import EnvLoader

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def check_python_version():
    """检查Python版本"""
    if sys.version_info < (3, 7):
        logger.error("需要Python 3.7或更高版本")
        return False
    logger.info(f"✓ Python版本: {sys.version}")
    return True

def check_dependencies():
    """检查依赖包"""
    try:
        import flask
        import pymysql
        import requests
        logger.info("✓ 主要依赖包已安装")
        return True
    except ImportError as e:
        logger.error(f"✗ 缺少依赖包: {e}")
        logger.info("请运行: pip install -r requirements.txt")
        return False

def check_env_file():
    """检查环境配置文件"""
    if os.path.exists('.env'):
        logger.info("✓ 找到.env配置文件")
        # 尝试加载环境变量
        EnvLoader.load_env_file()
        return True
    else:
        logger.warning("⚠ 未找到.env文件")
        if os.path.exists('.env.example'):
            logger.info("请复制.env.example为.env并配置数据库连接")
        else:
            logger.info("正在创建.env.example模板文件...")
            EnvLoader.create_env_example()
        return False

def run_tests():
    """运行测试"""
    logger.info("运行系统测试...")
    try:
        result = subprocess.run([sys.executable, 'test_fixes.py'], 
                              capture_output=True, text=True)
        if result.returncode == 0:
            logger.info("✓ 系统测试通过")
            return True
        else:
            logger.error("✗ 系统测试失败")
            logger.error(result.stderr)
            return False
    except Exception as e:
        logger.error(f"✗ 运行测试失败: {e}")
        return False

def check_login_status():
    """检查微信登录状态"""
    logger.info("检查微信登录状态...")
    try:
        from app.utils.startup_checker import run_startup_check
        result = run_startup_check()

        if result['success']:
            logger.info(f"✓ 登录状态检查完成: {result['message']}")
            return True
        else:
            logger.warning(f"⚠ 登录状态检查部分失败: {result['message']}")
            return True
    except Exception as e:
        logger.error(f"✗ 登录状态检查失败: {str(e)}")
        return True


def start_server():
    """启动服务器"""
    logger.info("启动YBA监控管理系统...")
    try:
        # 使用start.py启动
        subprocess.run([sys.executable, 'start.py'])
    except KeyboardInterrupt:
        logger.info("服务器已停止")
    except Exception as e:
        logger.error(f"启动失败: {e}")

def main():
    """主函数"""
    logger.info("=== YBA监控管理系统快速启动 ===")
    
    # 检查环境
    checks = [
        ("Python版本", check_python_version),
        ("依赖包", check_dependencies),
        ("配置文件", check_env_file)
    ]
    
    for check_name, check_func in checks:
        if not check_func():
            logger.error(f"环境检查失败: {check_name}")
            return False
    
    # 运行测试
    if not run_tests():
        logger.warning("测试失败，但仍将尝试启动服务器")

    # 检查登录状态
    check_login_status()

    # 启动服务器
    start_server()
    return True

if __name__ == '__main__':
    main()
