# YBA监控管理系统部署指南

## 快速开始

### 1. 环境准备

确保您的系统已安装：
- Python 3.7+
- MySQL 5.7+
- Git

### 2. 项目部署

```bash
# 1. 进入项目目录
cd unified-admin-system

# 2. 创建虚拟环境（推荐）
python -m venv venv
source venv/bin/activate  # Linux/Mac
# 或
venv\Scripts\activate     # Windows

# 3. 安装依赖
pip install -r requirements.txt

# 4. 配置环境变量
cp .env.example .env
# 编辑 .env 文件，设置您的数据库配置
```

### 3. 数据库配置

在 `.env` 文件中设置数据库连接：

```env
MYSQL_HOST=your-mysql-host
MYSQL_PORT=3306
MYSQL_USER=your-mysql-user
MYSQL_PASSWORD=your-mysql-password
MYSQL_DB=your-database-name
ADMIN_TOKEN=your-admin-token
```

### 4. 启动应用

```bash
# 使用启动脚本（推荐）
python start.py

# 或使用Flask命令
python run.py
```

### 5. 访问系统

打开浏览器访问：`http://localhost:5000`

使用您在 `.env` 中设置的 `ADMIN_TOKEN` 登录系统。

## 生产环境部署

### 使用 Gunicorn

```bash
# 安装 Gunicorn
pip install gunicorn

# 启动应用
gunicorn -w 4 -b 0.0.0.0:5000 "app:create_app()"
```

### 使用 Nginx 反向代理

```nginx
server {
    listen 80;
    server_name your-domain.com;

    location / {
        proxy_pass http://127.0.0.1:5000;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }

    location /static {
        alias /path/to/your/app/static;
        expires 1y;
        add_header Cache-Control "public, immutable";
    }
}
```

### 使用 Systemd 服务

创建 `/etc/systemd/system/unified-admin.service`：

```ini
[Unit]
Description=Unified Admin System
After=network.target

[Service]
User=www-data
Group=www-data
WorkingDirectory=/path/to/your/app
Environment=PATH=/path/to/your/venv/bin
ExecStart=/path/to/your/venv/bin/gunicorn -w 4 -b 127.0.0.1:5000 "app:create_app()"
Restart=always

[Install]
WantedBy=multi-user.target
```

启动服务：
```bash
sudo systemctl daemon-reload
sudo systemctl enable unified-admin
sudo systemctl start unified-admin
```

## 功能验证

### 1. 登录测试
- 访问系统首页
- 使用管理员Token登录
- 验证仪表盘显示正常

### 2. 微信功能测试
- 进入微信登录管理
- 测试二维码生成
- 验证状态检查功能

### 3. VIP功能测试
- 进入VIP会员管理
- 测试创建VIP卡
- 验证用户绑定功能

### 4. 管理员功能测试
- 测试代理商创建
- 验证权限控制
- 测试Token重置功能

## 故障排除

### 常见问题

1. **数据库连接失败**
   ```bash
   # 检查数据库服务状态
   sudo systemctl status mysql
   
   # 测试数据库连接
   mysql -h your-host -u your-user -p your-database
   ```

2. **端口被占用**
   ```bash
   # 查看端口占用
   netstat -tlnp | grep :5000
   
   # 杀死占用进程
   sudo kill -9 <PID>
   ```

3. **权限问题**
   ```bash
   # 设置正确的文件权限
   chmod +x start.py
   chown -R www-data:www-data /path/to/your/app
   ```

### 日志查看

```bash
# 查看应用日志
tail -f app.log

# 查看系统服务日志
sudo journalctl -u unified-admin -f
```

## 安全建议

1. **更改默认配置**
   - 设置强密码的管理员Token
   - 使用HTTPS协议
   - 定期更新依赖包

2. **防火墙配置**
   ```bash
   # 只允许必要端口
   sudo ufw allow 22    # SSH
   sudo ufw allow 80    # HTTP
   sudo ufw allow 443   # HTTPS
   sudo ufw enable
   ```

3. **数据库安全**
   - 使用专用数据库用户
   - 限制数据库访问权限
   - 定期备份数据

## 维护操作

### 备份数据库
```bash
mysqldump -h your-host -u your-user -p your-database > backup.sql
```

### 更新应用
```bash
git pull origin main
pip install -r requirements.txt
sudo systemctl restart unified-admin
```

### 监控系统
```bash
# 检查服务状态
sudo systemctl status unified-admin

# 检查资源使用
htop
df -h
```

## 联系支持

如遇到部署问题，请检查：
1. 系统日志文件
2. 数据库连接状态
3. 网络配置
4. 权限设置

提供详细的错误信息以便快速解决问题。
