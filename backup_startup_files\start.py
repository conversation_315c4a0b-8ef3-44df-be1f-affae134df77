#!/usr/bin/env python3
"""
YBA监控管理系统启动脚本
"""
import os
import sys
import logging
from app import create_app
from app.utils.env_loader import EnvLoader

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('app.log'),
        logging.StreamHandler(sys.stdout)
    ]
)

logger = logging.getLogger(__name__)

def check_environment():
    """检查环境配置"""
    required_vars = [
        'MYSQL_HOST',
        'MYSQL_USER', 
        'MYSQL_PASSWORD',
        'MYSQL_DB'
    ]
    
    missing_vars = []
    for var in required_vars:
        if not os.environ.get(var):
            missing_vars.append(var)
    
    if missing_vars:
        logger.error(f"缺少必要的环境变量: {', '.join(missing_vars)}")
        logger.info("请创建 .env 文件并设置必要的环境变量")
        return False
    
    return True

def test_database_connection():
    """测试数据库连接"""
    try:
        from app.models import get_db_connection
        connection = get_db_connection()
        connection.close()
        logger.info("数据库连接测试成功")
        return True
    except Exception as e:
        logger.error(f"数据库连接测试失败: {str(e)}")
        return False


def check_login_status():
    """检查所有代理商的微信登录状态"""
    logger.info("检查微信登录状态...")

    try:
        from app.utils.startup_checker import run_startup_check

        # 运行启动检查
        result = run_startup_check()

        if result['success']:
            logger.info(f"登录状态检查完成: {result['message']}")
            return True
        else:
            logger.warning(f"登录状态检查部分失败: {result['message']}")
            return True  # 不阻止系统启动

    except Exception as e:
        logger.error(f"登录状态检查失败: {str(e)}")
        logger.warning("将跳过登录状态检查，继续启动系统")
        return True  # 不阻止系统启动


def main():
    """主函数"""
    print("启动YBA监控管理系统...")

    # 加载环境变量
    EnvLoader.load_env_file()
    print("环境变量加载完成")

    # 检查环境配置
    if not check_environment():
        sys.exit(1)

    # 测试数据库连接
    if not test_database_connection():
        print("数据库连接失败，但系统仍将启动")

    # 检查微信登录状态（仅在主进程中执行）
    if os.environ.get('WERKZEUG_RUN_MAIN') != 'true':
        print("检查微信登录状态...")
        check_login_status()

    # 创建Flask应用
    try:
        app = create_app()
        print("Flask应用创建成功")
    except Exception as e:
        print(f"Flask应用创建失败: {str(e)}")
        sys.exit(1)

    # 启动应用
    try:
        host = os.environ.get('FLASK_HOST', '0.0.0.0')
        port = int(os.environ.get('FLASK_PORT', 5000))
        debug = os.environ.get('FLASK_DEBUG', 'False').lower() == 'true'

        print(f"启动服务器: http://{host}:{port}")
        print("按 Ctrl+C 停止服务器")

        app.run(host=host, port=port, debug=debug)

    except KeyboardInterrupt:
        print("服务器已停止")
    except Exception as e:
        print(f"服务器启动失败: {str(e)}")
        sys.exit(1)

if __name__ == '__main__':
    main()
