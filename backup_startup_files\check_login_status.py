#!/usr/bin/env python3
"""
微信登录状态检查工具
可以手动运行来检查所有代理商的微信登录状态
"""
import os
import sys
import logging
import argparse
from app.utils.env_loader import EnvLoader

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


def check_all_status():
    """检查所有代理商的登录状态"""
    logger.info("=== 微信登录状态检查工具 ===")
    
    try:
        # 加载环境变量
        EnvLoader.load_env_file()
        
        # 导入启动检查器
        from app.utils.startup_checker import run_startup_check
        
        # 运行检查
        result = run_startup_check()
        
        # 显示结果
        print("\n" + "="*50)
        print("检查结果总结")
        print("="*50)
        print(f"总计代理商: {result['total']}")
        print(f"在线代理商: {result['online']}")
        print(f"离线代理商: {result['offline']}")
        if result['errors'] > 0:
            print(f"检查错误: {result['errors']}")
        print(f"状态: {result['message']}")
        print("="*50)
        
        return result['success']
        
    except Exception as e:
        logger.error(f"检查失败: {str(e)}")
        return False


def quick_status():
    """快速状态查看"""
    logger.info("=== 快速状态查看 ===")
    
    try:
        # 加载环境变量
        EnvLoader.load_env_file()
        
        # 导入快速检查函数
        from app.utils.startup_checker import quick_status_check
        
        # 运行快速检查
        result = quick_status_check()
        
        # 显示结果
        print("\n" + "="*30)
        print("当前状态")
        print("="*30)
        print(f"总计: {result['total']}")
        print(f"在线: {result['online']}")
        print(f"离线: {result['offline']}")
        print(f"状态: {result['message']}")
        print("="*30)
        
        return result['success']
        
    except Exception as e:
        logger.error(f"状态查看失败: {str(e)}")
        return False


def show_agent_details():
    """显示代理商详细信息"""
    logger.info("=== 代理商详细信息 ===")
    
    try:
        # 加载环境变量
        EnvLoader.load_env_file()
        
        from app.models import get_db_connection
        
        connection = get_db_connection()
        with connection.cursor() as cursor:
            cursor.execute("""
                SELECT id, name, wechat_base_url, wechat_login_status, 
                       wechat_is_active, last_check_time
                FROM agents 
                WHERE wechat_is_active = 1
                ORDER BY id
            """)
            
            agents = cursor.fetchall()
            
            if not agents:
                print("没有找到激活的微信代理商")
                return True
            
            print(f"\n找到 {len(agents)} 个激活的微信代理商:")
            print("-" * 80)
            print(f"{'ID':<4} {'名称':<15} {'状态':<8} {'API地址':<30} {'最后检查':<20}")
            print("-" * 80)
            
            for agent in agents:
                agent_id, name, url, status, active, last_check = agent
                status_text = "在线" if status == 1 else "离线"
                last_check_text = str(last_check) if last_check else "未检查"
                
                print(f"{agent_id:<4} {name:<15} {status_text:<8} {url:<30} {last_check_text:<20}")
            
            print("-" * 80)
        
        connection.close()
        return True
        
    except Exception as e:
        logger.error(f"获取代理商信息失败: {str(e)}")
        return False


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='微信登录状态检查工具')
    parser.add_argument('--check', '-c', action='store_true', 
                       help='检查所有代理商的登录状态')
    parser.add_argument('--quick', '-q', action='store_true', 
                       help='快速查看当前状态')
    parser.add_argument('--details', '-d', action='store_true', 
                       help='显示代理商详细信息')
    parser.add_argument('--all', '-a', action='store_true', 
                       help='执行所有检查')
    
    args = parser.parse_args()
    
    # 如果没有指定参数，默认执行快速检查
    if not any([args.check, args.quick, args.details, args.all]):
        args.quick = True
    
    success = True
    
    try:
        if args.all:
            # 执行所有检查
            logger.info("执行完整检查...")
            success &= show_agent_details()
            success &= check_all_status()
            
        else:
            # 执行指定的检查
            if args.details:
                success &= show_agent_details()
            
            if args.quick:
                success &= quick_status()
            
            if args.check:
                success &= check_all_status()
        
        if success:
            logger.info("✓ 检查完成")
        else:
            logger.warning("⚠ 检查过程中出现问题")
            
    except KeyboardInterrupt:
        logger.info("检查被用户中断")
        success = False
    except Exception as e:
        logger.error(f"检查过程中发生异常: {str(e)}")
        success = False
    
    sys.exit(0 if success else 1)


if __name__ == '__main__':
    main()
