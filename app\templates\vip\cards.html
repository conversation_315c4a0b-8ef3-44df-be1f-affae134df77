{% extends "base.html" %}

{% block title %}VIP卡管理 - YBA监控管理系统{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- 页面标题 -->
    <div class="page-header mb-4">
        <div class="row align-items-center">
            <div class="col">
                <h1 class="page-title">
                    <i class="bi bi-credit-card-2-front me-2"></i>VIP卡管理
                </h1>
                <p class="text-muted">管理和查看VIP会员卡信息</p>
            </div>
            <div class="col-auto">
                <a href="{{ url_for('vip.create_card') }}" class="btn btn-primary">
                    <i class="bi bi-plus-circle me-2"></i>创建VIP卡
                </a>
            </div>
        </div>
    </div>

    <!-- 搜索和筛选 -->
    <div class="card mb-4">
        <div class="card-body">
            <form method="GET" class="row g-3">
                <div class="col-md-4">
                    <label for="search" class="form-label">搜索</label>
                    <input type="text" class="form-control" id="search" name="search" 
                           value="{{ search }}" placeholder="卡号或用户ID">
                </div>
                <div class="col-md-3">
                    <label for="status" class="form-label">状态</label>
                    <select class="form-select" id="status" name="status">
                        <option value="all" {% if status == 'all' %}selected{% endif %}>全部</option>
                        <option value="bound" {% if status == 'bound' %}selected{% endif %}>已绑定</option>
                        <option value="unbound" {% if status == 'unbound' %}selected{% endif %}>未绑定</option>
                        <option value="active" {% if status == 'active' %}selected{% endif %}>已激活</option>
                        <option value="inactive" {% if status == 'inactive' %}selected{% endif %}>未激活</option>
                        <option value="expired" {% if status == 'expired' %}selected{% endif %}>已过期</option>
                    </select>
                </div>
                <div class="col-md-2">
                    <label class="form-label">&nbsp;</label>
                    <div>
                        <button type="submit" class="btn btn-outline-primary">
                            <i class="bi bi-search me-1"></i>搜索
                        </button>
                    </div>
                </div>
            </form>
        </div>
    </div>

    <!-- VIP卡列表 -->
    <div class="card">
        <div class="card-header d-flex justify-content-between align-items-center">
            <div>
                <h5 class="card-title mb-0">
                    VIP卡列表
                    <span class="badge bg-secondary ms-2">共 {{ total }} 张</span>
                </h5>
            </div>
            <div class="d-flex gap-2">
                <button class="btn btn-outline-primary btn-sm" onclick="toggleBatchMode()">
                    <i class="bi bi-check-square me-1"></i>批量操作
                </button>
                <a href="{{ url_for('vip.create_card') }}" class="btn btn-primary btn-sm">
                    <i class="bi bi-plus me-1"></i>创建VIP卡
                </a>
            </div>
        </div>

        <!-- 批量操作工具栏 -->
        <div class="batch-toolbar" id="batchToolbar" style="display: none;">
            <div class="d-flex justify-content-between align-items-center p-3 bg-light border-bottom">
                <div class="d-flex align-items-center gap-3">
                    <span class="text-muted">已选择 <span id="selectedCount">0</span> 张卡</span>
                    <button class="btn btn-link btn-sm p-0" onclick="selectAll()">全选</button>
                    <button class="btn btn-link btn-sm p-0" onclick="clearSelection()">清空</button>
                </div>
                <div class="btn-group">
                    <button class="btn btn-outline-success btn-sm" onclick="batchActivate()">
                        <i class="bi bi-check-circle me-1"></i>批量激活
                    </button>
                    <button class="btn btn-outline-warning btn-sm" onclick="batchDeactivate()">
                        <i class="bi bi-pause-circle me-1"></i>批量停用
                    </button>
                    <button class="btn btn-outline-info btn-sm" onclick="batchExtend()">
                        <i class="bi bi-calendar-plus me-1"></i>批量延期
                    </button>
                    <button class="btn btn-outline-primary btn-sm" onclick="batchUpdatePoints()">
                        <i class="bi bi-gem me-1"></i>批量积分
                    </button>
                    <button class="btn btn-outline-danger btn-sm" onclick="batchDelete()">
                        <i class="bi bi-trash me-1"></i>批量删除
                    </button>
                </div>
            </div>
        </div>

        <div class="card-body p-0">
            {% if cards %}
            <div class="table-responsive">
                <table class="table table-hover mb-0">
                    <thead class="table-light">
                        <tr>
                            <th class="batch-column" style="display: none;">
                                <input type="checkbox" id="selectAllCheckbox" onchange="toggleSelectAll()">
                            </th>
                            <th>卡号</th>
                            <th>用户ID</th>
                            <th>积分</th>
                            <th>状态</th>
                            <th>代理商</th>
                            <th>创建时间</th>
                            <th>过期时间</th>
                            <th>操作</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for card in cards %}
                        <tr data-card-id="{{ card.id }}" data-card-number="{{ card.card_number }}">
                            <td class="batch-column" style="display: none;">
                                <input type="checkbox" class="card-checkbox" value="{{ card.id }}" onchange="updateSelectedCount()">
                            </td>
                            <td>
                                <code class="text-primary">{{ card.card_number }}</code>
                            </td>
                            <td>
                                {% if card.user_id %}
                                    <span class="text-success">{{ card.user_id }}</span>
                                {% else %}
                                    <span class="text-muted">未绑定</span>
                                {% endif %}
                            </td>
                            <td>
                                <span class="badge bg-warning text-dark">{{ card.points or 0 }}</span>
                            </td>
                            <td>
                                {% if card.user_id %}
                                    {% if card.is_active %}
                                        <span class="badge bg-success">已激活</span>
                                    {% else %}
                                        <span class="badge bg-secondary">未激活</span>
                                    {% endif %}
                                {% else %}
                                    <span class="badge bg-light text-dark">未绑定</span>
                                {% endif %}
                            </td>
                            <td>
                                <small class="text-muted">
                                    {{ card.agent_name or '未知' }}
                                    {% if card.agent_group %}
                                        <br><span class="badge bg-light text-dark">{{ card.agent_group }}</span>
                                    {% endif %}
                                </small>
                            </td>
                            <td>
                                <small class="text-muted">
                                    {{ card.created_at.strftime('%Y-%m-%d %H:%M') if card.created_at else '未知' }}
                                </small>
                            </td>
                            <td>
                                {% if card.expired_at %}
                                    <small class="text-muted">
                                        {{ card.expired_at.strftime('%Y-%m-%d') }}
                                    </small>
                                {% else %}
                                    <small class="text-muted">永久有效</small>
                                {% endif %}
                            </td>
                            <td>
                                <div class="btn-group btn-group-sm">
                                    <a href="{{ url_for('vip.card_detail', card_number=card.card_number) }}"
                                       class="btn btn-outline-primary btn-sm" title="查看详情">
                                        <i class="bi bi-eye"></i>
                                    </a>
                                    {% if card.user_id %}
                                        <button type="button" class="btn btn-outline-warning btn-sm"
                                                onclick="quickUnbind('{{ card.card_number }}')" title="解绑用户">
                                            <i class="bi bi-link-45deg"></i>
                                        </button>
                                    {% else %}
                                        <button type="button" class="btn btn-outline-success btn-sm"
                                                onclick="quickBind('{{ card.card_number }}')" title="绑定用户">
                                            <i class="bi bi-plus-circle"></i>
                                        </button>
                                    {% endif %}
                                    <button type="button" class="btn btn-outline-danger btn-sm"
                                            onclick="quickDelete('{{ card.card_number }}')" title="删除">
                                        <i class="bi bi-trash"></i>
                                    </button>
                                </div>
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
            {% else %}
            <div class="text-center py-5">
                <i class="bi bi-credit-card-2-front text-muted" style="font-size: 3rem;"></i>
                <h5 class="text-muted mt-3">暂无VIP卡</h5>
                <p class="text-muted">
                    {% if search %}
                        没有找到匹配的VIP卡
                    {% else %}
                        还没有创建任何VIP卡
                    {% endif %}
                </p>
                <a href="{{ url_for('vip.create_card') }}" class="btn btn-primary">
                    <i class="bi bi-plus-circle me-2"></i>创建第一张VIP卡
                </a>
            </div>
            {% endif %}
        </div>
        
        <!-- 分页 -->
        {% if total_pages > 1 %}
        <div class="card-footer">
            <nav aria-label="VIP卡分页">
                <ul class="pagination justify-content-center mb-0">
                    {% if has_prev %}
                        <li class="page-item">
                            <a class="page-link" href="{{ url_for('vip.cards', page=page-1, search=search, status=status) }}">
                                <i class="bi bi-chevron-left"></i>
                            </a>
                        </li>
                    {% endif %}
                    
                    {% for p in range(1, total_pages + 1) %}
                        {% if p == page %}
                            <li class="page-item active">
                                <span class="page-link">{{ p }}</span>
                            </li>
                        {% elif p <= 3 or p >= total_pages - 2 or (p >= page - 1 and p <= page + 1) %}
                            <li class="page-item">
                                <a class="page-link" href="{{ url_for('vip.cards', page=p, search=search, status=status) }}">{{ p }}</a>
                            </li>
                        {% elif p == 4 and page > 6 %}
                            <li class="page-item disabled">
                                <span class="page-link">...</span>
                            </li>
                        {% elif p == total_pages - 3 and page < total_pages - 5 %}
                            <li class="page-item disabled">
                                <span class="page-link">...</span>
                            </li>
                        {% endif %}
                    {% endfor %}
                    
                    {% if has_next %}
                        <li class="page-item">
                            <a class="page-link" href="{{ url_for('vip.cards', page=page+1, search=search, status=status) }}">
                                <i class="bi bi-chevron-right"></i>
                            </a>
                        </li>
                    {% endif %}
                </ul>
            </nav>
        </div>
        {% endif %}
    </div>
</div>

<!-- 卡片详情模态框 -->
<div class="modal fade" id="cardModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">VIP卡详情</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body" id="cardModalBody">
                <!-- 卡片详情内容 -->
            </div>
        </div>
    </div>
</div>

<!-- 批量操作模态框 -->
<!-- 批量过期时间设置模态框 -->
<div class="modal fade" id="batchExtendModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">批量设置过期时间</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="batchExtendForm">
                    <div class="mb-3">
                        <label for="batchExpiryOperation" class="form-label">操作类型</label>
                        <select class="form-select" id="batchExpiryOperation" onchange="toggleBatchExpiryInputs()">
                            <option value="add_days">增加天数</option>
                            <option value="subtract_days">减少天数</option>
                            <option value="set_date">设置具体日期</option>
                            <option value="permanent">设为永久</option>
                        </select>
                    </div>

                    <!-- 天数设置 -->
                    <div class="mb-3" id="batchDaysGroup">
                        <label class="form-label">快捷选择</label>
                        <div class="btn-group w-100 mb-2" role="group">
                            <button type="button" class="btn btn-outline-primary" onclick="setBatchDays(7)">7天</button>
                            <button type="button" class="btn btn-outline-primary" onclick="setBatchDays(30)">30天</button>
                            <button type="button" class="btn btn-outline-primary" onclick="setBatchDays(90)">90天</button>
                            <button type="button" class="btn btn-outline-primary" onclick="setBatchDays(365)">1年</button>
                        </div>
                        <label for="batchDays" class="form-label">天数</label>
                        <input type="number" class="form-control" id="batchDays" min="1" placeholder="请输入天数">
                        <div class="form-text" id="batchDaysHelpText">在当前过期时间基础上增加天数</div>
                    </div>

                    <!-- 具体日期设置 -->
                    <div class="mb-3" id="batchDateGroup" style="display: none;">
                        <label class="form-label">快捷设置</label>
                        <div class="btn-group w-100 mb-2" role="group">
                            <button type="button" class="btn btn-outline-primary" onclick="setBatchQuickDate(30)">30天后</button>
                            <button type="button" class="btn btn-outline-primary" onclick="setBatchQuickDate(90)">90天后</button>
                            <button type="button" class="btn btn-outline-primary" onclick="setBatchQuickDate(365)">1年后</button>
                        </div>
                        <label for="batchExpiryDate" class="form-label">过期时间</label>
                        <input type="datetime-local" class="form-control" id="batchExpiryDate">
                        <div class="form-text">设置所有选中VIP卡的过期时间</div>
                    </div>

                    <!-- 永久有效提示 -->
                    <div class="mb-3" id="batchPermanentGroup" style="display: none;">
                        <div class="alert alert-info">
                            <i class="bi bi-info-circle me-2"></i>
                            将所有选中的VIP卡设置为永久有效，不会过期。
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                <button type="button" class="btn btn-primary" onclick="saveBatchExtend()">确定操作</button>
            </div>
        </div>
    </div>
</div>

<!-- 批量积分模态框 -->
<div class="modal fade" id="batchPointsModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">批量积分操作</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="batchPointsForm">
                    <div class="mb-3">
                        <label for="batchPointsOperation" class="form-label">操作类型</label>
                        <select class="form-select" id="batchPointsOperation">
                            <option value="set">设置为</option>
                            <option value="add">增加</option>
                            <option value="subtract">减少</option>
                        </select>
                    </div>
                    <div class="mb-3">
                        <label for="batchPointsValue" class="form-label">积分数量</label>
                        <input type="number" class="form-control" id="batchPointsValue" min="0" required>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                <button type="button" class="btn btn-primary" onclick="saveBatchPoints()">确定操作</button>
            </div>
        </div>
    </div>
</div>

<!-- 快速绑定模态框 -->
<div class="modal fade" id="quickBindModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">绑定用户</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="quickBindForm">
                    <div class="mb-3">
                        <label for="quickBindUserId" class="form-label">用户ID</label>
                        <input type="text" class="form-control" id="quickBindUserId" placeholder="请输入用户ID" required>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                <button type="button" class="btn btn-primary" onclick="saveQuickBind()">绑定</button>
            </div>
        </div>
    </div>
</div>

<style>
.batch-toolbar {
    border-top: 1px solid var(--border-color);
}

.batch-column {
    width: 40px;
}

.card-checkbox {
    cursor: pointer;
}

.table tbody tr:hover {
    background-color: rgba(0, 0, 0, 0.02);
}
</style>

<script>
let batchMode = false;
let currentQuickBindCard = null;

function toggleBatchMode() {
    batchMode = !batchMode;
    const toolbar = document.getElementById('batchToolbar');
    const batchColumns = document.querySelectorAll('.batch-column');

    if (batchMode) {
        toolbar.style.display = 'block';
        batchColumns.forEach(col => col.style.display = 'table-cell');
    } else {
        toolbar.style.display = 'none';
        batchColumns.forEach(col => col.style.display = 'none');
        clearSelection();
    }
}

function updateSelectedCount() {
    const checkboxes = document.querySelectorAll('.card-checkbox:checked');
    document.getElementById('selectedCount').textContent = checkboxes.length;
}

function selectAll() {
    const checkboxes = document.querySelectorAll('.card-checkbox');
    checkboxes.forEach(cb => cb.checked = true);
    document.getElementById('selectAllCheckbox').checked = true;
    updateSelectedCount();
}

function clearSelection() {
    const checkboxes = document.querySelectorAll('.card-checkbox');
    checkboxes.forEach(cb => cb.checked = false);
    document.getElementById('selectAllCheckbox').checked = false;
    updateSelectedCount();
}

function toggleSelectAll() {
    const selectAllCheckbox = document.getElementById('selectAllCheckbox');
    const checkboxes = document.querySelectorAll('.card-checkbox');

    checkboxes.forEach(cb => cb.checked = selectAllCheckbox.checked);
    updateSelectedCount();
}

function getSelectedCards() {
    const checkboxes = document.querySelectorAll('.card-checkbox:checked');
    return Array.from(checkboxes).map(cb => {
        const row = cb.closest('tr');
        return {
            id: cb.value,
            card_number: row.dataset.cardNumber
        };
    });
}

function batchActivate() {
    const selectedCards = getSelectedCards();
    if (selectedCards.length === 0) {
        showError('请选择要激活的VIP卡');
        return;
    }

    if (confirm(`确定要激活选中的 ${selectedCards.length} 张VIP卡吗？`)) {
        batchUpdateStatus(selectedCards, true);
    }
}

function batchDeactivate() {
    const selectedCards = getSelectedCards();
    if (selectedCards.length === 0) {
        showError('请选择要停用的VIP卡');
        return;
    }

    if (confirm(`确定要停用选中的 ${selectedCards.length} 张VIP卡吗？`)) {
        batchUpdateStatus(selectedCards, false);
    }
}

function batchUpdateStatus(cards, isActive) {
    const promises = cards.map(card =>
        fetch(`/vip/cards/${card.card_number}/status`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({is_active: isActive})
        })
    );

    Promise.all(promises)
        .then(responses => Promise.all(responses.map(r => r.json())))
        .then(results => {
            const successCount = results.filter(r => r.success).length;
            const failCount = results.length - successCount;

            if (failCount === 0) {
                showSuccess(`批量操作成功，共处理 ${successCount} 张卡`);
                setTimeout(() => location.reload(), 1000);
            } else {
                showWarning(`操作完成，成功 ${successCount} 张，失败 ${failCount} 张`);
                setTimeout(() => location.reload(), 2000);
            }
        })
        .catch(error => {
            console.error('Error:', error);
            showError('批量操作失败');
        });
}

function batchExtend() {
    const selectedCards = getSelectedCards();
    if (selectedCards.length === 0) {
        showError('请选择要设置过期时间的VIP卡');
        return;
    }

    new bootstrap.Modal(document.getElementById('batchExtendModal')).show();
}

function toggleBatchExpiryInputs() {
    const operation = document.getElementById('batchExpiryOperation').value;
    const daysGroup = document.getElementById('batchDaysGroup');
    const dateGroup = document.getElementById('batchDateGroup');
    const permanentGroup = document.getElementById('batchPermanentGroup');
    const daysHelpText = document.getElementById('batchDaysHelpText');

    // 隐藏所有输入组
    daysGroup.style.display = 'none';
    dateGroup.style.display = 'none';
    permanentGroup.style.display = 'none';

    // 根据操作类型显示相应的输入组
    switch(operation) {
        case 'add_days':
            daysGroup.style.display = 'block';
            daysHelpText.textContent = '在当前过期时间基础上增加天数';
            break;
        case 'subtract_days':
            daysGroup.style.display = 'block';
            daysHelpText.textContent = '在当前过期时间基础上减少天数';
            break;
        case 'set_date':
            dateGroup.style.display = 'block';
            break;
        case 'permanent':
            permanentGroup.style.display = 'block';
            break;
    }
}

function setBatchDays(days) {
    document.getElementById('batchDays').value = days;
}

function setBatchQuickDate(days) {
    const date = new Date();
    date.setDate(date.getDate() + days);
    document.getElementById('batchExpiryDate').value = date.toISOString().slice(0, 16);
}

function saveBatchExtend() {
    const operation = document.getElementById('batchExpiryOperation').value;
    const selectedCards = getSelectedCards();
    let requestData = { operation: operation };

    switch(operation) {
        case 'add_days':
        case 'subtract_days':
            const days = parseInt(document.getElementById('batchDays').value);
            if (!days || days <= 0) {
                showError('请输入有效的天数');
                return;
            }
            requestData.days = days;
            break;

        case 'set_date':
            const expiry = document.getElementById('batchExpiryDate').value;
            if (!expiry) {
                showError('请选择过期时间');
                return;
            }
            requestData.expired_at = expiry;
            break;

        case 'permanent':
            requestData.expired_at = null;
            break;
    }

    const promises = selectedCards.map(card =>
        fetch(`/vip/cards/${card.card_number}/expiry`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify(requestData)
        })
    );

    Promise.all(promises)
        .then(responses => Promise.all(responses.map(r => r.json())))
        .then(results => {
            const successCount = results.filter(r => r.success).length;
            const failCount = results.length - successCount;

            const operationText = {
                'add_days': '延期',
                'subtract_days': '缩短有效期',
                'set_date': '设置过期时间',
                'permanent': '设置永久有效'
            }[operation];

            if (failCount === 0) {
                showSuccess(`批量${operationText}成功，共处理 ${successCount} 张卡`);
                setTimeout(() => location.reload(), 1000);
            } else {
                showWarning(`${operationText}完成，成功 ${successCount} 张，失败 ${failCount} 张`);
                setTimeout(() => location.reload(), 2000);
            }
        })
        .catch(error => {
            console.error('Error:', error);
            showError('批量操作失败');
        });

    bootstrap.Modal.getInstance(document.getElementById('batchExtendModal')).hide();
}

function batchUpdatePoints() {
    const selectedCards = getSelectedCards();
    if (selectedCards.length === 0) {
        showError('请选择要修改积分的VIP卡');
        return;
    }

    new bootstrap.Modal(document.getElementById('batchPointsModal')).show();
}

function saveBatchPoints() {
    const operation = document.getElementById('batchPointsOperation').value;
    const points = parseInt(document.getElementById('batchPointsValue').value);

    if (isNaN(points) || points < 0) {
        showError('请输入有效的积分数量');
        return;
    }

    const selectedCards = getSelectedCards();

    const promises = selectedCards.map(card =>
        fetch(`/vip/cards/${card.card_number}/points`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                operation: operation,
                points: points
            })
        })
    );

    Promise.all(promises)
        .then(responses => Promise.all(responses.map(r => r.json())))
        .then(results => {
            const successCount = results.filter(r => r.success).length;
            const failCount = results.length - successCount;

            if (failCount === 0) {
                showSuccess(`批量积分操作成功，共处理 ${successCount} 张卡`);
                setTimeout(() => location.reload(), 1000);
            } else {
                showWarning(`积分操作完成，成功 ${successCount} 张，失败 ${failCount} 张`);
                setTimeout(() => location.reload(), 2000);
            }
        })
        .catch(error => {
            console.error('Error:', error);
            showError('批量积分操作失败');
        });

    bootstrap.Modal.getInstance(document.getElementById('batchPointsModal')).hide();
}

function batchDelete() {
    const selectedCards = getSelectedCards();
    if (selectedCards.length === 0) {
        showError('请选择要删除的VIP卡');
        return;
    }

    if (confirm(`确定要删除选中的 ${selectedCards.length} 张VIP卡吗？此操作不可恢复！`)) {
        const promises = selectedCards.map(card =>
            fetch(`/vip/cards/${card.card_number}/delete`, {
                method: 'DELETE',
                headers: {
                    'Content-Type': 'application/json',
                }
            })
        );

        Promise.all(promises)
            .then(responses => Promise.all(responses.map(r => r.json())))
            .then(results => {
                const successCount = results.filter(r => r.success).length;
                const failCount = results.length - successCount;

                if (failCount === 0) {
                    showSuccess(`批量删除成功，共删除 ${successCount} 张卡`);
                    setTimeout(() => location.reload(), 1000);
                } else {
                    showWarning(`删除完成，成功 ${successCount} 张，失败 ${failCount} 张`);
                    setTimeout(() => location.reload(), 2000);
                }
            })
            .catch(error => {
                console.error('Error:', error);
                showError('批量删除失败');
            });
    }
}

function quickBind(cardNumber) {
    currentQuickBindCard = cardNumber;
    new bootstrap.Modal(document.getElementById('quickBindModal')).show();
}

function saveQuickBind() {
    const userId = document.getElementById('quickBindUserId').value.trim();

    if (!userId) {
        showError('请输入用户ID');
        return;
    }

    fetch(`/vip/cards/${currentQuickBindCard}/bind`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({user_id: userId})
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showSuccess(data.message);
            setTimeout(() => location.reload(), 1000);
        } else {
            showError(data.message);
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showError('绑定失败');
    });

    bootstrap.Modal.getInstance(document.getElementById('quickBindModal')).hide();
}

function quickUnbind(cardNumber) {
    if (confirm('确定要解绑此VIP卡的用户吗？')) {
        fetch(`/vip/cards/${cardNumber}/unbind`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showSuccess(data.message);
                setTimeout(() => location.reload(), 1000);
            } else {
                showError(data.message);
            }
        })
        .catch(error => {
            console.error('Error:', error);
            showError('解绑失败');
        });
    }
}

function quickDelete(cardNumber) {
    if (confirm('确定要删除此VIP卡吗？此操作不可恢复！')) {
        fetch(`/vip/cards/${cardNumber}/delete`, {
            method: 'DELETE',
            headers: {
                'Content-Type': 'application/json',
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showSuccess(data.message);
                setTimeout(() => location.reload(), 1000);
            } else {
                showError(data.message);
            }
        })
        .catch(error => {
            console.error('Error:', error);
            showError('删除失败');
        });
    }
}

function unbindCard(cardId) {
    if (confirm('确定要解绑这张VIP卡吗？')) {
        fetch(`/vip/cards/${cardId}/unbind`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                location.reload();
            } else {
                alert('解绑失败: ' + data.message);
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('解绑失败');
        });
    }
}

function deleteCard(cardId) {
    if (confirm('确定要删除这张VIP卡吗？此操作不可恢复！')) {
        fetch(`/vip/cards/${cardId}/delete`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                location.reload();
            } else {
                alert('删除失败: ' + data.message);
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('删除失败');
        });
    }
}
</script>
{% endblock %}
