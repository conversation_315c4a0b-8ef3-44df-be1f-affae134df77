{% extends "base.html" %}

{% block title %}V2车载扫码登录{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row justify-content-center">
        <div class="col-md-8 col-lg-6">
            <!-- V2车载扫码登录卡片 -->
            <div class="qr-login-card">
                <!-- 简化的标题区域 -->
                <div class="card-header">
                    <h4 class="title">
                        <i class="bi bi-car-front me-2"></i>
                        车载扫码登录
                    </h4>
                    <p class="subtitle">使用微信扫描二维码登录</p>
                </div>

                <!-- 简化的二维码显示区 -->
                <div class="qrcode-display" id="qrcodeDisplay">
                    <!-- 加载状态 -->
                    <div class="qrcode-loading" id="qrcodeLoading">
                        <div class="spinner-border" role="status">
                            <span class="visually-hidden">正在获取二维码...</span>
                        </div>
                        <p class="mt-3">正在获取二维码...</p>
                    </div>

                    <!-- 二维码内容 -->
                    <div class="qrcode-content" id="qrcodeContent" style="display: none;">
                        <div class="qrcode-wrapper">
                            <img src="" alt="车载登录二维码" class="qrcode-image" id="qrcodeImage">
                            <div class="qrcode-overlay" id="qrcodeOverlay" style="display: none;">
                                <div class="overlay-content">
                                    <i class="bi bi-clock-history"></i>
                                    <p>二维码已过期</p>
                                    <button class="btn btn-primary btn-sm" onclick="refreshQrcode()">
                                        重新获取
                                    </button>
                                </div>
                            </div>
                        </div>

                        <!-- 简化的倒计时 -->
                        <div class="countdown-info">
                            <span class="countdown-time" id="countdownTime">120</span>秒后过期
                        </div>

                        <!-- 状态提示 -->
                        <div class="status-text" id="statusText">
                            请使用微信扫描二维码
                        </div>
                    </div>

                    <!-- 错误状态 -->
                    <div class="qrcode-error" id="qrcodeError" style="display: none;">
                        <i class="bi bi-exclamation-triangle text-warning"></i>
                        <p id="errorMessage">获取二维码失败</p>
                        <button class="btn btn-primary btn-sm" onclick="refreshQrcode()">
                            重新获取
                        </button>
                    </div>
                </div>

                <!-- 简化的登录成功信息 -->
                <div class="login-success" id="loginSuccess" style="display: none;">
                    <i class="bi bi-check-circle-fill text-success"></i>
                    <h5>登录成功！</h5>
                    <p>正在开启自动心跳...</p>
                </div>

                <!-- 简化的操作按钮 -->
                <div class="actions">
                    <button class="btn btn-outline-primary btn-sm" onclick="refreshQrcode()" id="refreshBtn">
                        <i class="bi bi-arrow-clockwise me-1"></i>重新获取
                    </button>
                    <a href="{{ url_for('wechat.index') }}" class="btn btn-outline-secondary btn-sm">
                        <i class="bi bi-arrow-left me-1"></i>返回
                    </a>
                </div>

                <!-- 调试信息（仅开发环境显示） -->
                {% if debug_info %}
                <div class="debug-info mt-4">
                    <details>
                        <summary>调试信息</summary>
                        <div class="debug-content">
                            <p><strong>用户ID:</strong> {{ debug_info.user_id }}</p>
                            <p><strong>用户名:</strong> {{ debug_info.name }}</p>
                            <p><strong>API地址:</strong> {{ debug_info.base_url }}</p>
                            <p><strong>WXID:</strong> {{ debug_info.wxid }}</p>
                            <p><strong>设备ID:</strong> {{ debug_info.device_id or '未设置' }}</p>
                            <p><strong>代理配置:</strong> {{ debug_info.proxy or '未设置' }}</p>
                            {% if debug_info.proxy_parsed %}
                            <p><strong>解析后代理:</strong></p>
                            <ul>
                                <li>代理地址: {{ debug_info.proxy_parsed.ProxyIp }}</li>
                                <li>用户名: {{ debug_info.proxy_parsed.ProxyUser or '无' }}</li>
                                <li>密码: {{ '***' if debug_info.proxy_parsed.ProxyPassword else '无' }}</li>
                            </ul>
                            {% endif %}
                        </div>
                    </details>
                </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>

<style>
/* 简化的车载扫码登录样式 */
.qr-login-card {
    background: white;
    border-radius: 12px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    padding: 2rem;
    margin: 2rem auto;
    max-width: 500px;
    border: 1px solid #e9ecef;
}

.card-header {
    text-align: center;
    margin-bottom: 2rem;
    padding-bottom: 1rem;
    border-bottom: 1px solid #e9ecef;
}

.card-header .title {
    font-weight: 600;
    margin-bottom: 0.5rem;
    font-size: 1.4rem;
    color: #2c3e50;
}

.card-header .subtitle {
    color: #6c757d;
    font-size: 0.95rem;
    margin: 0;
}

/* 简化的二维码显示区 */
.qrcode-display {
    background: #f8f9fa;
    border-radius: 8px;
    padding: 2rem;
    margin-bottom: 1.5rem;
    text-align: center;
    min-height: 280px;
    display: flex;
    align-items: center;
    justify-content: center;
    border: 1px solid #e9ecef;
}

.qrcode-content {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 1rem;
}

.qrcode-wrapper {
    position: relative;
    display: inline-block;
}

.qrcode-image {
    width: 180px;
    height: 180px;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    border: 1px solid #dee2e6;
}

.qrcode-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(255, 255, 255, 0.95);
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.overlay-content {
    text-align: center;
}

.overlay-content i {
    font-size: 1.5rem;
    margin-bottom: 0.5rem;
}

/* 简化的倒计时和状态 */
.countdown-info {
    font-size: 0.9rem;
    color: #6c757d;
    margin-bottom: 0.5rem;
}

.countdown-time {
    font-weight: 600;
    color: #007bff;
}

.status-text {
    font-size: 0.95rem;
    color: #495057;
    font-weight: 500;
}

/* 简化的登录成功样式 */
.login-success {
    text-align: center;
    padding: 1.5rem;
    background: #d4edda;
    border: 1px solid #c3e6cb;
    border-radius: 8px;
    margin-bottom: 1.5rem;
}

.login-success i {
    font-size: 2.5rem;
    margin-bottom: 1rem;
}

.login-success h5 {
    color: #155724;
    margin-bottom: 0.5rem;
}

.login-success p {
    color: #155724;
    margin: 0;
    font-size: 0.9rem;
}

/* 简化的错误显示 */
.qrcode-error {
    text-align: center;
    color: #6c757d;
}

.qrcode-error i {
    font-size: 2rem;
    margin-bottom: 1rem;
}

/* 简化的操作按钮 */
.actions {
    display: flex;
    gap: 0.75rem;
    justify-content: center;
}

.actions .btn {
    border-radius: 6px;
    font-weight: 500;
}

/* 调试信息 */
.debug-info {
    background: rgba(0, 0, 0, 0.1);
    border-radius: 8px;
    padding: 1rem;
    font-size: 0.85rem;
}

.debug-info summary {
    cursor: pointer;
    font-weight: 500;
    margin-bottom: 0.5rem;
}

.debug-content {
    margin-top: 1rem;
    opacity: 0.9;
}

.debug-content p, .debug-content li {
    margin-bottom: 0.25rem;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .v2-car-qrcode-card {
        margin: 1rem;
        padding: 1.5rem;
    }
    
    .status-indicator {
        flex-wrap: wrap;
        gap: 1rem;
    }
    
    .status-item {
        flex: 1;
        min-width: 120px;
    }
    
    .qrcode-image-v2 {
        width: 160px;
        height: 160px;
    }
    
    .actions-v2 {
        flex-direction: column;
    }
}
</style>

<script>
// 简化的车载扫码登录JavaScript
let qrCheckInterval = null;
let countdownInterval = null;

// 页面加载完成后自动获取二维码
document.addEventListener('DOMContentLoaded', function() {
    getCarQrcode();
});

// 更新状态文本
function updateStatusText(text) {
    const statusElement = document.getElementById('statusText');
    if (statusElement) {
        statusElement.textContent = text;
    }
}

// 获取车载二维码
function getCarQrcode() {
    updateStatusText('正在获取二维码...');

    // 显示加载状态
    document.getElementById('qrcodeLoading').style.display = 'block';
    document.getElementById('qrcodeContent').style.display = 'none';
    document.getElementById('qrcodeError').style.display = 'none';
    document.getElementById('loginSuccess').style.display = 'none';
    
    fetch('{{ url_for("wechat.v2_get_car_qrcode") }}')
        .then(response => response.json())
        .then(data => {
            // 适配正确的返回数据格式：Code=1表示成功
            if (data.Code === 1 && data.Data) {
                // 二维码获取成功
                const qrData = data.Data;
                const qrcode = qrData.QrBase64 || qrData.qrCodeBase64 || qrData.qrcode;

                if (qrcode) {
                    // 显示二维码
                    const qrcodeImage = document.getElementById('qrcodeImage');
                    if (qrcode.startsWith('data:')) {
                        qrcodeImage.src = qrcode;
                    } else {
                        qrcodeImage.src = qrcode; // QrBase64已经包含完整的data URL
                    }

                    document.getElementById('qrcodeLoading').style.display = 'none';
                    document.getElementById('qrcodeContent').style.display = 'block';

                    updateStatusText('请使用微信扫描二维码');

                    // 开始倒计时 - 从ExpiredTime字段解析
                    let expiredTime = 120; // 默认120秒
                    if (qrData.ExpiredTime) {
                        // 如果是时间字符串，计算剩余秒数
                        try {
                            const expiredDate = new Date(qrData.ExpiredTime);
                            const currentDate = new Date();
                            const remainingSeconds = Math.floor((expiredDate - currentDate) / 1000);
                            expiredTime = Math.max(remainingSeconds, 30); // 至少30秒
                        } catch (e) {
                            console.warn('解析过期时间失败，使用默认值:', e);
                        }
                    }
                    startCountdown(expiredTime);

                    // 开始检查二维码状态
                    startQrCheck();
                } else {
                    showError('二维码数据格式错误');
                }
            } else {
                showError(data.Message || data.Text || '获取二维码失败');
            }
        })
        .catch(error => {
            console.error('获取二维码失败:', error);
            showError('网络错误，请检查连接');
        });
}

// 简化的倒计时
function startCountdown(seconds) {
    const countdownElement = document.getElementById('countdownTime');
    let remaining = seconds;

    countdownElement.textContent = remaining;

    countdownInterval = setInterval(() => {
        remaining--;
        countdownElement.textContent = remaining;

        if (remaining <= 0) {
            clearInterval(countdownInterval);
            showExpired();
        }
    }, 1000);
}

// 开始检查二维码状态
function startQrCheck() {
    qrCheckInterval = setInterval(() => {
        checkQrStatus();
    }, 2000); // 每2秒检查一次
}

// 检查二维码状态
function checkQrStatus() {
    fetch('{{ url_for("wechat.v2_check_car_qrcode") }}')
        .then(response => response.json())
        .then(data => {
            // V2登录成功检测：Code=0, Success=true, Message="登录成功"
            if (data.Code === 0 && data.Success && data.Message === '登录成功' && data.Data) {
                // V2登录成功
                clearInterval(qrCheckInterval);
                clearInterval(countdownInterval);

                updateStatusText('登录成功！正在开启自动心跳...');
                showLoginSuccess();

                // 显示心跳开启结果
                setTimeout(() => {
                    if (data.heartbeat_result) {
                        if (data.heartbeat_result.Code === 200 || data.heartbeat_result.Code === 0) {
                            showComplete();
                        } else {
                            showHeartbeatError(data.heartbeat_result);
                        }
                    } else {
                        // 如果没有心跳结果，显示警告但仍然算作成功
                        showCompleteWithWarning();
                    }
                }, 2000);
            } else if (data.Code === 200 && data.Data) {
                // 兼容其他可能的响应格式
                const dataObj = data.Data;
                const status = dataObj.status;
                const state = dataObj.state;

                if (status === 'SCAN_SUCC' || state === 2 || dataObj.loginState === 1) {
                    // 登录成功
                    clearInterval(qrCheckInterval);
                    clearInterval(countdownInterval);

                    updateStatusText('登录成功！正在开启自动心跳...');
                    showLoginSuccess();

                    setTimeout(() => {
                        if (data.heartbeat_result) {
                            if (data.heartbeat_result.Code === 200 || data.heartbeat_result.Code === 0) {
                                showComplete();
                            } else {
                                showHeartbeatError(data.heartbeat_result);
                            }
                        } else {
                            showCompleteWithWarning();
                        }
                    }, 2000);
                } else if (status === 'EXPIRED') {
                    clearInterval(qrCheckInterval);
                    clearInterval(countdownInterval);
                    showExpired();
                } else if (status === 'SCANNED') {
                    updateStatusText('已扫码，请在手机上确认登录');
                }
            }
        })
        .catch(error => {
            console.error('检查二维码状态失败:', error);
        });
}

// 显示登录成功
function showLoginSuccess() {
    document.getElementById('qrcodeContent').style.display = 'none';
    document.getElementById('loginSuccess').style.display = 'block';
}

// 显示完成状态
function showComplete() {
    const successDiv = document.getElementById('loginSuccess');
    successDiv.innerHTML = `
        <div class="success-icon">
            <i class="bi bi-check-circle-fill"></i>
        </div>
        <h5>登录完成！</h5>
        <p>自动心跳已开启，您可以返回主页查看登录状态</p>
        <a href="{{ url_for('wechat.index') }}" class="btn btn-success">
            <i class="bi bi-house me-1"></i>返回主页
        </a>
    `;
}

// 显示心跳错误
function showHeartbeatError(heartbeatResult) {
    const successDiv = document.getElementById('loginSuccess');
    successDiv.innerHTML = `
        <div class="success-icon">
            <i class="bi bi-exclamation-triangle-fill" style="color: #f39c12;"></i>
        </div>
        <h5>登录成功，但自动心跳开启失败</h5>
        <p>错误信息: ${heartbeatResult ? (heartbeatResult.Text || heartbeatResult.Message) : '未知错误'}</p>
        <a href="{{ url_for('wechat.index') }}" class="btn btn-warning">
            <i class="bi bi-house me-1"></i>返回主页
        </a>
    `;
}

// 显示完成但有警告
function showCompleteWithWarning() {
    const successDiv = document.getElementById('loginSuccess');
    successDiv.innerHTML = `
        <div class="success-icon">
            <i class="bi bi-check-circle-fill" style="color: #28a745;"></i>
        </div>
        <h5>登录完成！</h5>
        <p>登录成功，但未收到自动心跳开启确认</p>
        <a href="{{ url_for('wechat.index') }}" class="btn btn-success">
            <i class="bi bi-house me-1"></i>返回主页
        </a>
    `;
}

// 显示过期状态
function showExpired() {
    document.getElementById('qrcodeOverlay').style.display = 'flex';
}

// 显示错误
function showError(message) {
    document.getElementById('qrcodeLoading').style.display = 'none';
    document.getElementById('qrcodeContent').style.display = 'none';
    document.getElementById('qrcodeError').style.display = 'block';
    document.getElementById('errorMessage').textContent = message;
}

// 刷新二维码
function refreshQrcode() {
    // 清除定时器
    if (qrCheckInterval) {
        clearInterval(qrCheckInterval);
        qrCheckInterval = null;
    }
    if (countdownInterval) {
        clearInterval(countdownInterval);
        countdownInterval = null;
    }
    
    // 重新获取二维码
    getCarQrcode();
}

// 页面卸载时清除定时器
window.addEventListener('beforeunload', function() {
    if (qrCheckInterval) {
        clearInterval(qrCheckInterval);
    }
    if (countdownInterval) {
        clearInterval(countdownInterval);
    }
});
</script>
{% endblock %}
