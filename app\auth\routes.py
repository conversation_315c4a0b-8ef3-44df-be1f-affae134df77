"""
认证路由
"""
from flask import render_template, request, redirect, url_for, flash, session
from app.auth import bp
from app.auth.forms import LoginForm
from app.auth.decorators import login_required, admin_required
from app.models import Agent
import logging

logger = logging.getLogger(__name__)


@bp.route('/login', methods=['GET', 'POST'])
def login():
    """登录页面（支持双API系统）"""
    if session.get('logged_in'):
        return redirect(url_for('auth.dashboard'))

    form = LoginForm()
    error = None

    if request.method == 'POST':
        login_method = request.form.get('login_method', 'v1')

        if login_method == 'v1':
            # V1登录（Token认证）
            if form.validate_on_submit():
                token = form.token.data.strip()

                if not token:
                    error = 'Token不能为空！'
                else:
                    # 验证Token
                    agent = Agent.get_by_token(token)
                    if agent:
                        # 登录成功
                        session['logged_in'] = True
                        session['agent_id'] = agent.id
                        session['agent_name'] = agent.name
                        session['agent_prefix'] = agent.group
                        session['is_admin'] = agent.is_admin
                        session['api_version'] = 'v1'
                        session['auth_method'] = 'token'

                        logger.info(f"V1用户登录成功: {agent.name} (ID: {agent.id})")

                        if agent.is_admin:
                            flash('欢迎回来，管理员！', 'success')
                        else:
                            flash(f'欢迎回来，{agent.name}！', 'success')

                        return redirect(url_for('auth.dashboard'))
                    else:
                        error = '无效的Token或代理商未激活'
                        logger.warning(f"V1登录失败: 无效Token {token}")

        elif login_method == 'v2':
            # V2登录（WXID认证）
            wxid = request.form.get('wxid', '').strip()

            if not wxid:
                error = '微信ID不能为空！'
            else:
                # 验证WXID
                agent = Agent.get_by_wxid(wxid)
                if agent:
                    # 登录成功
                    session['logged_in'] = True
                    session['agent_id'] = agent.id
                    session['agent_name'] = agent.name
                    session['agent_prefix'] = agent.group
                    session['is_admin'] = agent.is_admin
                    session['api_version'] = 'v2'
                    session['auth_method'] = 'wxid'
                    session['wxid'] = wxid

                    logger.info(f"V2用户登录成功: {agent.name} (ID: {agent.id}, WXID: {wxid})")

                    if agent.is_admin:
                        flash('欢迎回来，管理员！（V2系统）', 'success')
                    else:
                        flash(f'欢迎回来，{agent.name}！（V2系统）', 'success')

                    return redirect(url_for('auth.dashboard'))
                else:
                    error = '无效的微信ID或代理商未激活'
                    logger.warning(f"V2登录失败: 无效WXID {wxid}")

        elif login_method == 'v3':
            # V3登录（GeWeChat API）
            app_id = request.form.get('app_id', '').strip()

            # V3登录需要通过Token验证，但可以设置app_id
            if form.validate_on_submit():
                token = form.token.data.strip()

                if not token:
                    error = 'Token不能为空！'
                else:
                    # 验证Token
                    agent = Agent.get_by_token(token)
                    if agent:
                        # 登录成功
                        session['logged_in'] = True
                        session['agent_id'] = agent.id
                        session['agent_name'] = agent.name
                        session['agent_prefix'] = agent.group
                        session['is_admin'] = agent.is_admin
                        session['api_version'] = 'v3'
                        session['auth_method'] = 'app_id'

                        # 如果提供了app_id，保存到session中
                        if app_id:
                            session['app_id'] = app_id
                            # 可以考虑将app_id保存到数据库中
                            try:
                                from app.models import get_db_connection
                                connection = get_db_connection()
                                try:
                                    with connection.cursor() as cursor:
                                        cursor.execute("UPDATE agents SET app_id = %s WHERE id = %s", (app_id, agent.id))
                                        connection.commit()
                                        logger.info(f"更新代理商app_id: {agent.id} -> {app_id}")
                                except Exception as e:
                                    logger.error(f"更新app_id失败: {str(e)}")
                                finally:
                                    connection.close()
                            except Exception as e:
                                logger.error(f"数据库操作失败: {str(e)}")

                        logger.info(f"V3用户登录成功: {agent.name} (ID: {agent.id}, App ID: {app_id or '未设置'})")

                        if agent.is_admin:
                            flash('欢迎回来，管理员！（V3系统）', 'success')
                        else:
                            flash(f'欢迎回来，{agent.name}！（V3系统）', 'success')

                        return redirect(url_for('auth.dashboard'))
                    else:
                        error = '无效的Token或代理商未激活'
                        logger.warning(f"V3登录失败: 无效Token {token}")
            else:
                error = '请填写有效的Token'
    
    return render_template('auth/login.html', form=form, error=error)


@bp.route('/logout')
def logout():
    """退出登录"""
    user_name = session.get('agent_name', '未知用户')
    session.clear()
    flash(f'{user_name} 已退出登录', 'info')
    logger.info(f"用户退出登录: {user_name}")
    return redirect(url_for('auth.login'))


@bp.route('/dashboard')
@login_required
def dashboard():
    """仪表盘（支持双API系统）"""
    agent_id = session.get('agent_id')
    is_admin = session.get('is_admin', False)
    api_version = session.get('api_version', 'v1')
    auth_method = session.get('auth_method', 'token')

    # 获取代理商信息
    from app.models import Agent, get_db_connection
    agent = Agent.get_by_id(agent_id)

    if not agent:
        flash('用户信息获取失败', 'danger')
        return redirect(url_for('auth.login'))

    # 获取统计数据
    stats = {}

    connection = get_db_connection()
    try:
        with connection.cursor() as cursor:
            # 微信登录状态（当前用户或全局）
            if is_admin:
                # 管理员查看系统整体微信状态（有任何一个代理商在线就显示在线）
                cursor.execute("SELECT COUNT(*) FROM agents WHERE wechat_login_status = 1")
                online_count = cursor.fetchone()[0]
                stats['wechat_status'] = 1 if online_count > 0 else 0

                # 获取API版本统计
                cursor.execute("""
                    SELECT
                        COALESCE(api_version, 'v1') as version,
                        COUNT(*) as total,
                        SUM(CASE WHEN wechat_login_status = 1 THEN 1 ELSE 0 END) as online
                    FROM agents
                    WHERE wechat_is_active = 1
                    GROUP BY COALESCE(api_version, 'v1')
                """)
                api_stats = cursor.fetchall()
                stats['api_stats'] = {}
                for row in api_stats:
                    version = row[0]
                    stats['api_stats'][version] = {
                        'total': row[1],
                        'online': row[2]
                    }
            else:
                # 代理商查看自己的微信状态
                cursor.execute("SELECT wechat_login_status FROM agents WHERE id = %s", (agent_id,))
                result = cursor.fetchone()
                stats['wechat_status'] = result[0] if result else 0

            # 已激活VIP数量
            if is_admin:
                # 管理员查看全局已激活VIP数量
                cursor.execute("SELECT COUNT(*) FROM vip_cards WHERE user_id IS NOT NULL AND user_id != ''")
                stats['active_vip_count'] = cursor.fetchone()[0]
            else:
                # 代理商查看同组的已激活VIP数量
                cursor.execute("""
                    SELECT COUNT(*)
                    FROM vip_cards
                    WHERE user_id IS NOT NULL AND user_id != '' AND agent_group = %s
                """, (agent.group,))
                stats['active_vip_count'] = cursor.fetchone()[0]

            # 剩余积分
            if is_admin:
                # 管理员查看全局剩余积分总和
                cursor.execute("SELECT COALESCE(SUM(points), 0) FROM vip_cards WHERE user_id IS NOT NULL AND user_id != ''")
                stats['remaining_points'] = cursor.fetchone()[0]
            else:
                # 代理商查看同组的VIP卡剩余积分总和
                cursor.execute("""
                    SELECT COALESCE(SUM(points), 0)
                    FROM vip_cards
                    WHERE user_id IS NOT NULL AND user_id != '' AND agent_group = %s
                """, (agent.group,))
                stats['remaining_points'] = cursor.fetchone()[0]

    finally:
        connection.close()

    # 添加API版本信息
    stats['api_version'] = api_version
    stats['auth_method'] = auth_method
    stats['api_info'] = {
        'version': api_version,
        'method': auth_method,
        'display_name': 'V3 API (GeWeChat)' if api_version == 'v3' else ('V2 API (WXID认证)' if api_version == 'v2' else 'V1 API (Token认证)')
    }

    return render_template('auth/dashboard.html', stats=stats)
