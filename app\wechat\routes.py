"""
微信登录管理路由
"""
from flask import render_template, request, redirect, url_for, flash, session, jsonify
from app.wechat import bp
from app.auth.decorators import login_required
from app.wechat.utils import WeChatClient
from app.models import get_agent_api_key, get_db_connection
import logging
import time
import json

logger = logging.getLogger(__name__)


@bp.route('/')
@login_required
def index():
    """微信登录管理首页（支持双API系统）"""
    agent_id = session.get('agent_id')
    is_admin = session.get('is_admin', False)
    api_version = session.get('api_version', 'v1')
    auth_method = session.get('auth_method', 'token')

    # 获取代理商信息
    from app.models import Agent
    agent = Agent.get_by_id(agent_id)

    if not agent:
        flash('用户信息获取失败', 'danger')
        return redirect(url_for('auth.dashboard'))

    # 检查微信功能是否激活
    if not is_admin and not agent.wechat_is_active:
        flash('微信功能未激活，请联系管理员', 'warning')
        return redirect(url_for('auth.dashboard'))

    # 检查是否设置了微信API基础URL
    if not agent.wechat_base_url:
        flash('未设置微信API基础URL，请联系管理员配置', 'danger')
        return render_template('wechat/index.html',
                              login_status=None,
                              user_info=None,
                              api_error=True,
                              api_version=api_version,
                              debug_info={
                                  'user_id': agent.id,
                                  'name': agent.name,
                                  'base_url': agent.wechat_base_url,
                                  'proxy': agent.proxy,
                                  'is_admin': is_admin,
                                  'api_version': api_version
                              })

    # 根据API版本创建对应的客户端
    try:
        if api_version == 'v3':
            # V3 API - 使用API管理器
            from app.wechat.api_manager import APIManager

            # 检查token和app_id
            token = get_agent_api_key(agent.id)
            app_id = getattr(agent, 'app_id', None)

            if not token:
                flash('V3 API需要设置token，请联系管理员配置', 'danger')
                return render_template('wechat/index.html',
                                      login_status=None,
                                      user_info=None,
                                      api_error=True,
                                      api_version=api_version,
                                      debug_info={
                                          'user_id': agent.id,
                                          'name': agent.name,
                                          'base_url': agent.wechat_base_url,
                                          'proxy': agent.proxy,
                                          'token': 'Yes' if token else 'No',
                                          'app_id': app_id,
                                          'api_version': api_version,
                                          'error': 'Token未设置',
                                          'is_admin': is_admin
                                      })

            # 创建API管理器配置
            agent_config = {
                'id': agent.id,
                'name': agent.name,
                'wechat_base_url': agent.wechat_base_url,
                'proxy': agent.proxy,
                'token': token,
                'app_id': app_id,
                'wechat_is_active': agent.wechat_is_active
            }

            api_manager = APIManager(agent_config, api_version)

            # 获取登录状态
            login_status = api_manager.get_login_status()
            logger.info(f"V3登录状态响应: {json.dumps(login_status, ensure_ascii=False)}")

            # 获取用户信息
            user_info = None
            if login_status.get('Code') == 200 and login_status.get('Data', {}).get('loginState') == 1:
                user_info = api_manager.get_user_info()
                logger.info(f"V3用户信息响应: {json.dumps(user_info, ensure_ascii=False)}")

                # 更新数据库中的登录状态
                update_agent_login_status(agent.id, 1)
            else:
                # 更新数据库中的登录状态为未登录
                update_agent_login_status(agent.id, 0)

        elif api_version == 'v2':
            # V2 API - 使用API管理器
            from app.wechat.api_manager import APIManager

            # 检查WXID
            if not agent.wxid:
                flash('V2 API需要设置WXID，请联系管理员配置', 'danger')
                return render_template('wechat/index.html',
                                      login_status=None,
                                      user_info=None,
                                      api_error=True,
                                      api_version=api_version,
                                      debug_info={
                                          'user_id': agent.id,
                                          'name': agent.name,
                                          'base_url': agent.wechat_base_url,
                                          'proxy': agent.proxy,
                                          'wxid': agent.wxid,
                                          'api_version': api_version,
                                          'error': 'WXID未设置',
                                          'is_admin': is_admin
                                      })

            # 创建API管理器配置
            agent_config = {
                'id': agent.id,
                'name': agent.name,
                'wechat_base_url': agent.wechat_base_url,
                'proxy': agent.proxy,
                'wxid': agent.wxid,
                'device_id': agent.device_id,
                'wechat_is_active': agent.wechat_is_active
            }

            api_manager = APIManager(agent_config, api_version)

            # 获取登录状态
            login_status = api_manager.get_login_status()
            logger.info(f"V2登录状态响应: {json.dumps(login_status, ensure_ascii=False)}")

            # 获取用户信息
            user_info = None
            if login_status.get('Code') == 200 and login_status.get('Data', {}).get('loginState') == 1:
                user_info = api_manager.get_user_info()
                logger.info(f"V2用户信息响应: {json.dumps(user_info, ensure_ascii=False)}")

                # 更新数据库中的登录状态
                update_agent_login_status(agent.id, 1)
            else:
                # 更新数据库中的登录状态为未登录
                update_agent_login_status(agent.id, 0)

        else:
            # V1 API - 使用原有客户端
            # 获取API密钥
            api_key = get_agent_api_key(agent.id)

            # 检查API密钥是否存在
            if not api_key:
                flash('未设置API密钥，请联系管理员配置', 'danger')
                return render_template('wechat/index.html',
                                      login_status=None,
                                      user_info=None,
                                      api_error=True,
                                      api_version=api_version,
                                      debug_info={
                                          'user_id': agent.id,
                                          'name': agent.name,
                                          'base_url': agent.wechat_base_url,
                                          'proxy': agent.proxy,
                                          'api_key_provided': 'No',
                                          'api_version': api_version,
                                          'is_admin': is_admin
                                      })

            # 创建微信API客户端
            wechat_client = WeChatClient(agent.wechat_base_url, api_key, agent.proxy)

            # 获取登录状态
            login_status = wechat_client.get_login_status()
            logger.info(f"V1登录状态响应: {json.dumps(login_status, ensure_ascii=False)}")

            # 获取用户信息
            user_info = None
            if login_status.get('Code') == 200 and login_status.get('Data', {}).get('loginState') == 1:
                user_info = wechat_client.get_user_info()
                logger.info(f"V1用户信息响应: {json.dumps(user_info, ensure_ascii=False)}")

                # 更新数据库中的登录状态
                update_agent_login_status(agent.id, 1)
            else:
                # 更新数据库中的登录状态为未登录
                update_agent_login_status(agent.id, 0)

    except ValueError as e:
        logger.error(f"创建API客户端失败: {str(e)}")
        flash(f'创建微信客户端失败: {str(e)}', 'danger')
        return render_template('wechat/index.html',
                              login_status=None,
                              user_info=None,
                              api_error=True,
                              api_version=api_version,
                              debug_info={
                                  'user_id': agent.id,
                                  'name': agent.name,
                                  'base_url': agent.wechat_base_url,
                                  'proxy': agent.proxy,
                                  'api_key_provided': 'Yes' if api_version == 'v1' else 'N/A',
                                  'wxid': agent.wxid if api_version == 'v2' else 'N/A',
                                  'api_version': api_version,
                                  'error': str(e),
                                  'is_admin': is_admin
                              })

    debug_info = {
        'user_id': agent.id,
        'name': agent.name,
        'base_url': agent.wechat_base_url,
        'proxy': agent.proxy,
        'api_key_provided': 'Yes' if api_version == 'v1' else 'N/A',
        'wxid': agent.wxid if api_version == 'v2' else 'N/A',
        'api_version': api_version,
        'auth_method': auth_method,
        'is_admin': is_admin
    }

    # 处理登录状态数据，提取详细信息用于界面显示
    detailed_status = None
    if login_status and login_status.get('Code') == 200 and login_status.get('Data'):
        detailed_status = login_status.get('Data', {})

        # 如果有自动刷新令牌信息，计算剩余时间
        if 'autoRefreshToken' in detailed_status:
            auto_refresh = detailed_status['autoRefreshToken']
            if auto_refresh.get('minutes_until_next_refresh'):
                # 转换为更友好的显示格式
                minutes = auto_refresh.get('minutes_until_next_refresh', 0)
                hours = minutes // 60
                mins = minutes % 60
                auto_refresh['display_time'] = f"{hours}小时{mins}分钟" if hours > 0 else f"{mins}分钟"

    return render_template('wechat/index.html',
                          login_status=login_status,
                          user_info=user_info,
                          detailed_status=detailed_status,
                          api_error=False,
                          api_version=api_version,
                          debug_info=debug_info)


@bp.route('/qrcode')
@login_required
def qrcode():
    """获取登录二维码（支持双API系统）"""
    agent_id = session.get('agent_id')
    api_version = session.get('api_version', 'v1')

    # 获取代理商信息
    from app.models import Agent
    agent = Agent.get_by_id(agent_id)

    if not agent:
        flash('用户信息获取失败', 'danger')
        return redirect(url_for('wechat.index'))

    # 检查是否设置了微信API基础URL
    if not agent.wechat_base_url:
        flash('未设置微信API基础URL，请联系管理员配置', 'danger')
        return redirect(url_for('wechat.index'))

    try:
        if api_version == 'v3':
            # V3 API - 使用API管理器
            from app.wechat.api_manager import APIManager

            # 检查token
            token = get_agent_api_key(agent.id)
            if not token:
                flash('V3 API需要设置token，请联系管理员配置', 'danger')
                return redirect(url_for('wechat.index'))

            # 创建API管理器配置
            agent_config = {
                'id': agent.id,
                'name': agent.name,
                'wechat_base_url': agent.wechat_base_url,
                'proxy': agent.proxy,
                'token': token,
                'app_id': getattr(agent, 'app_id', None),
                'wechat_is_active': agent.wechat_is_active
            }

            api_manager = APIManager(agent_config, api_version)

            # 获取登录二维码
            qrcode_result = api_manager.get_qr_code()
            logger.info(f"V3二维码响应: Code={qrcode_result.get('Code')}")

        elif api_version == 'v2':
            # V2 API - 使用API管理器
            from app.wechat.api_manager import APIManager

            # 检查WXID
            if not agent.wxid:
                flash('V2 API需要设置WXID，请联系管理员配置', 'danger')
                return redirect(url_for('wechat.index'))

            # 创建API管理器配置
            agent_config = {
                'id': agent.id,
                'name': agent.name,
                'wechat_base_url': agent.wechat_base_url,
                'proxy': agent.proxy,
                'wxid': agent.wxid,
                'device_id': agent.device_id,
                'wechat_is_active': agent.wechat_is_active
            }

            api_manager = APIManager(agent_config, api_version)

            # 获取登录二维码
            qrcode_result = api_manager.get_qr_code()
            logger.info(f"V2二维码响应: Code={qrcode_result.get('Code')}")

        else:
            # V1 API - 使用原有客户端
            # 获取API密钥
            api_key = get_agent_api_key(agent.id)

            # 检查API密钥是否存在
            if not api_key:
                flash('未设置API密钥，请联系管理员配置', 'danger')
                return redirect(url_for('wechat.index'))

            # 创建微信API客户端
            wechat_client = WeChatClient(agent.wechat_base_url, api_key, agent.proxy)

            # 获取登录二维码
            qrcode_result = wechat_client.get_login_qrcode()
            logger.info(f"V1二维码响应: Code={qrcode_result.get('Code')}")

    except ValueError as e:
        logger.error(f"创建API客户端失败: {str(e)}")
        flash(f'创建微信客户端失败: {str(e)}', 'danger')
        return redirect(url_for('wechat.index'))

    # 检查二维码获取结果 - 适配不同的成功状态码
    success_codes = [200, 1]  # V1 API使用200，V2 API使用1
    qrcode_data = qrcode_result.get('Data', {})

    # 检查是否获取成功
    if (qrcode_result.get('Code') not in success_codes or
        not (qrcode_data.get('qrcode') or qrcode_data.get('QrBase64') or qrcode_data.get('qrCodeBase64'))):
        error_msg = qrcode_result.get("Text") or qrcode_result.get("Message") or "获取二维码失败"
        logger.warning(f"获取二维码失败: {error_msg}")
        flash(f'获取二维码失败: {error_msg}', 'danger')
        return redirect(url_for('wechat.index'))

    # 存储二维码信息到会话，包括UUID
    uuid = qrcode_data.get('Uuid') or qrcode_data.get('uuid') or qrcode_data.get('UUID')

    # 支持新的API响应格式，优先使用expiredTime字段
    expired_time = (qrcode_data.get('expiredTime') or
                   qrcode_data.get('expired_time') or
                   120)

    session['qrcode_created_time'] = time.time()
    session['qrcode_expired_time'] = expired_time
    session['qrcode_api_version'] = api_version

    # 存储UUID用于状态检查
    if uuid:
        session['qrcode_uuid'] = uuid
        logger.info(f"标准二维码UUID存储到会话: {uuid}")
    else:
        logger.warning("标准二维码响应中缺少UUID")

    # 处理二维码数据格式 - 支持多种字段名
    qrcode_image_data = None
    qrcode_base64 = (qrcode_data.get('QrBase64') or
                    qrcode_data.get('qrCodeBase64') or
                    qrcode_data.get('qrcode'))

    if qrcode_base64:
        if qrcode_base64.startswith('data:'):
            qrcode_image_data = qrcode_base64
        else:
            qrcode_image_data = f"data:image/png;base64,{qrcode_base64}"

    return render_template('wechat/qrcode_simple.html',
                          qrcode_data=qrcode_image_data,
                          expired_time=expired_time,
                          api_version=api_version)


@bp.route('/check_qrcode')
@login_required
def check_qrcode():
    """检查二维码登录状态（支持双API系统）"""
    agent_id = session.get('agent_id')
    api_version = session.get('qrcode_api_version', session.get('api_version', 'v1'))

    # 检查二维码是否过期
    created_time = session.get('qrcode_created_time', 0)
    expired_time = session.get('qrcode_expired_time', 120)

    if created_time > 0 and time.time() - created_time > expired_time:
        logger.warning(f"二维码已过期: user_id={agent_id}")
        session.pop('qrcode_created_time', None)
        session.pop('qrcode_expired_time', None)
        session.pop('qrcode_api_version', None)
        return jsonify({
            'Code': 200,
            'Data': {
                'status': 'EXPIRED',
                'message': '二维码已过期，请重新获取'
            }
        })

    # 获取代理商信息
    from app.models import Agent
    agent = Agent.get_by_id(agent_id)

    if not agent or not agent.wechat_base_url:
        return jsonify({
            'success': False,
            'message': '配置错误',
            'Code': 500,
            'Data': {
                'status': 'ERROR',
                'message': '配置错误'
            }
        })

    # 获取会话中的UUID
    uuid = session.get('qrcode_uuid')
    if not uuid:
        return jsonify({
            'Code': -1,
            'Data': {
                'status': 'ERROR',
                'message': '未找到二维码UUID，请重新获取二维码'
            }
        })

    try:
        if api_version == 'v3':
            # V3 API - 使用API管理器检查二维码状态
            from app.wechat.api_manager import APIManager

            # 检查token
            token = get_agent_api_key(agent.id)
            if not token:
                return jsonify({
                    'success': False,
                    'message': 'token未设置',
                    'Code': 403,
                    'Data': {
                        'status': 'ERROR',
                        'message': 'token未设置'
                    }
                })

            # 创建API管理器配置
            agent_config = {
                'id': agent.id,
                'name': agent.name,
                'wechat_base_url': agent.wechat_base_url,
                'proxy': agent.proxy,
                'token': token,
                'app_id': getattr(agent, 'app_id', None),
                'wechat_is_active': agent.wechat_is_active
            }

            api_manager = APIManager(agent_config, api_version)

            # 使用UUID检查二维码状态
            result = api_manager.check_qr_status(uuid)
            logger.info(f"V3二维码状态检查结果: Code={result.get('Code')}, Data={result.get('Data')}")

        elif api_version == 'v2':
            # V2 API - 使用API管理器检查二维码状态
            from app.wechat.api_manager import APIManager

            # 检查WXID
            if not agent.wxid:
                return jsonify({
                    'success': False,
                    'message': 'WXID未设置',
                    'Code': 403,
                    'Data': {
                        'status': 'ERROR',
                        'message': 'WXID未设置'
                    }
                })

            # 创建API管理器配置
            agent_config = {
                'id': agent.id,
                'name': agent.name,
                'wechat_base_url': agent.wechat_base_url,
                'proxy': agent.proxy,
                'wxid': agent.wxid,
                'device_id': agent.device_id,
                'wechat_is_active': agent.wechat_is_active
            }

            api_manager = APIManager(agent_config, api_version)

            # 使用UUID检查二维码状态
            result = api_manager.check_qr_status(uuid)
            logger.info(f"V2二维码状态检查结果: Code={result.get('Code')}, Data={result.get('Data')}")

        else:
            # V1 API - 使用原有客户端检查二维码状态
            # 获取API密钥
            api_key = get_agent_api_key(agent.id)

            if not api_key:
                return jsonify({
                    'success': False,
                    'message': '未设置API密钥',
                    'Code': 403,
                    'Data': {
                        'status': 'ERROR',
                        'message': '未设置API密钥'
                    }
                })

            # 创建微信API客户端
            wechat_client = WeChatClient(agent.wechat_base_url, api_key, agent.proxy)

            # V1 API使用check_login_qrcode方法
            result = wechat_client.check_login_qrcode()
            logger.info(f"V1二维码状态检查结果: Code={result.get('Code')}, Data={result.get('Data')}")

    except ValueError as e:
        logger.error(f"创建API客户端失败: {str(e)}")
        return jsonify({
            'success': False,
            'message': f'创建微信客户端失败: {str(e)}',
            'Code': 500,
            'Data': {
                'status': 'ERROR',
                'message': str(e)
            }
        })
    
    # 处理新的API响应格式
    if result.get('success') and result.get('data'):
        # 新API格式的响应处理
        data = result.get('data', {})

        # 更新剩余时间（如果API返回了effective_time）
        if 'effective_time' in data:
            effective_time = data['effective_time']
            session['qrcode_effective_time'] = effective_time

            # 构造标准化响应
            standardized_result = {
                'Code': 200,
                'success': True,
                'data': {
                    'uuid': data.get('uuid', ''),
                    'state': data.get('state', 0),
                    'effective_time': effective_time,
                    'push_login_url_expired_time': data.get('push_login_url_expired_time', 0)
                },
                'message': result.get('message', '扫码状态')
            }

            # 检查登录状态
            if data.get('state') == 2:  # 登录成功
                logger.info(f"{api_version.upper()}登录成功: user_id={agent.id}")

                # V2 API需要开启自动心跳自动二次登录
                if api_version == 'v2':
                    try:
                        from app.wechat.client_v2 import WeChatClientV2
                        v2_client = WeChatClientV2(base_url=agent.wechat_base_url, wxid=agent.wxid)
                        heartbeat_result = v2_client.enable_auto_heartbeat()
                        logger.info(f"V2标准登录自动心跳开启结果: {heartbeat_result}")

                        # 将心跳结果添加到响应中
                        standardized_result['heartbeat_result'] = heartbeat_result
                    except Exception as e:
                        logger.error(f"V2标准登录开启自动心跳失败: {str(e)}")

                # 更新数据库登录状态
                update_agent_login_status(agent.id, 1)
                logger.info(f"{api_version.upper()}更新登录状态: user_id={agent.id}")

                # 清除会话中的二维码信息
                session.pop('qrcode_created_time', None)
                session.pop('qrcode_expired_time', None)
                session.pop('qrcode_api_version', None)
                session.pop('qrcode_effective_time', None)
                session.pop('qrcode_uuid', None)

            return jsonify(standardized_result)

    # 检查登录成功 - 适配V2正确的返回格式
    login_success = False

    if api_version == 'v2':
        # V2 API登录成功检测：Code=0, Success=true, 有用户信息
        if (result.get('Code') == 0 and result.get('Success') and
            result.get('Data') and result.get('Message') == '登录成功'):

            data = result.get('Data', {})
            acct_sect_resp = data.get('acctSectResp', {})

            if acct_sect_resp and acct_sect_resp.get('userName') and acct_sect_resp.get('nickName'):
                login_success = True
                logger.info(f"V2标准登录成功检测: userName={acct_sect_resp.get('userName')}, nickName={acct_sect_resp.get('nickName')}")
    else:
        # V1 API登录成功检测
        if result.get('Code') == 200 and result.get('Data'):
            data = result.get('Data', {})
            if (data.get('status') == 'SCAN_SUCC' or
                data.get('loginState') == 1 or
                data.get('state') == 2 or
                (data.get('wxid') and (data.get('nick_name') or data.get('nickname')))):
                login_success = True
                logger.info(f"V1标准登录成功检测")

    if login_success:
        logger.info(f"{api_version.upper()}登录成功: user_id={agent.id}")

        # V2 API需要开启自动心跳自动二次登录
        if api_version == 'v2':
            try:
                from app.wechat.client_v2 import WeChatClientV2
                v2_client = WeChatClientV2(base_url=agent.wechat_base_url, wxid=agent.wxid)
                heartbeat_result = v2_client.enable_auto_heartbeat()
                logger.info(f"V2标准登录自动心跳开启结果: {heartbeat_result}")

                # 将心跳结果添加到响应中
                result['heartbeat_result'] = heartbeat_result
            except Exception as e:
                logger.error(f"V2标准登录开启自动心跳失败: {str(e)}")

        # 更新数据库登录状态
        update_agent_login_status(agent.id, 1)
        logger.info(f"{api_version.upper()}更新登录状态: user_id={agent.id}")

        # 清除会话中的二维码信息
        session.pop('qrcode_created_time', None)
        session.pop('qrcode_expired_time', None)
        session.pop('qrcode_api_version', None)
        session.pop('qrcode_uuid', None)

    return jsonify(result)


@bp.route('/qrcode-simple')
def qrcode_simple():
    """简洁的二维码登录页面（演示）"""
    # 模拟二维码数据
    qrcode_data = "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg=="
    expired_time = 290  # 模拟API返回的过期时间

    return render_template('wechat/qrcode_simple.html',
                          qrcode_data=qrcode_data,
                          expired_time=expired_time,
                          api_error=False)

@bp.route('/qrcode-demo')
def qrcode_demo():
    """演示二维码登录页面效果（无需登录）"""
    # 模拟二维码数据
    qrcode_data = "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg=="
    expired_time = 120  # 2分钟

    return render_template('wechat/qrcode.html',
                          qrcode_data=qrcode_data,
                          expired_time=expired_time,
                          api_error=False)

@bp.route('/demo')
@login_required
def demo():
    """演示优化后的界面效果"""
    # 模拟用户提供的JSON数据
    demo_login_status = {
        "Code": 200,
        "Data": {
            "autoRefreshToken": {
                "auto_refresh_enabled": True,
                "last_refresh_time": 1753909467,
                "minutes_until_next_refresh": 30,
                "needs_refresh": False,
                "time_since_last_refresh": 1749,
                "display_time": "30分钟"
            },
            "expiryTime": "2026-07-21",
            "loginErrMsg": "账号在线状态良好！",
            "loginJournal": {
                "count": 2,
                "logs": [
                    {"time": "2025-07-30 20:51:20", "message": "用户登录成功"},
                    {"time": "2025-07-30 19:30:15", "message": "自动刷新令牌"}
                ]
            },
            "loginState": 1,
            "loginTime": "2025-07-30 20:51:20",
            "onlineDays": 0,
            "onlineTime": "本次在线: 0天0时42分",
            "proxyUrl": "socks5://rhpl21t1:rhpl21t1@**************:1666",
            "targetIp": "*************:8080",
            "totalOnline": "总计在线: 9天20时28分"
        },
        "Text": ""
    }

    demo_user_info = {
        "Code": 200,
        "Data": {
            "wxid": "wxid_demo123456",
            "nick_name": "演示用户",
            "head_img": "https://via.placeholder.com/80x80/28a745/ffffff?text=Demo",
            "mobile": "138****8888"
        }
    }

    # 处理详细状态信息
    detailed_status = demo_login_status.get('Data', {})
    if 'autoRefreshToken' in detailed_status:
        auto_refresh = detailed_status['autoRefreshToken']
        if auto_refresh.get('minutes_until_next_refresh'):
            minutes = auto_refresh.get('minutes_until_next_refresh', 0)
            hours = minutes // 60
            mins = minutes % 60
            auto_refresh['display_time'] = f"{hours}小时{mins}分钟" if hours > 0 else f"{mins}分钟"

    debug_info = {
        'user_id': 'demo',
        'name': '演示代理商',
        'base_url': 'http://demo.example.com:8080',
        'proxy': 'socks5://demo:<EMAIL>:1666',
        'api_version': 'v1',
        'is_admin': False
    }

    return render_template('wechat/index.html',
                          login_status=demo_login_status,
                          user_info=demo_user_info,
                          detailed_status=detailed_status,
                          api_error=False,
                          api_version='v1',
                          debug_info=debug_info)


@bp.route('/logout')
@login_required
def logout():
    """退出微信登录（支持双API系统）"""
    agent_id = session.get('agent_id')
    api_version = session.get('api_version', 'v1')

    # 获取代理商信息
    from app.models import Agent
    agent = Agent.get_by_id(agent_id)

    if not agent or not agent.wechat_base_url:
        flash('配置错误', 'danger')
        return redirect(url_for('wechat.index'))

    try:
        if api_version == 'v3':
            # V3 API - 使用API管理器
            from app.wechat.api_manager import APIManager

            # 检查token
            token = get_agent_api_key(agent.id)
            if not token:
                flash('V3 API需要设置token，请联系管理员配置', 'danger')
                return redirect(url_for('wechat.index'))

            # 创建API管理器配置
            agent_config = {
                'id': agent.id,
                'name': agent.name,
                'wechat_base_url': agent.wechat_base_url,
                'proxy': agent.proxy,
                'token': token,
                'app_id': getattr(agent, 'app_id', None),
                'wechat_is_active': agent.wechat_is_active
            }

            api_manager = APIManager(agent_config, api_version)

            # 退出登录
            result = api_manager.logout()
            logger.info(f"V3退出登录响应: {json.dumps(result, ensure_ascii=False)}")

        elif api_version == 'v2':
            # V2 API - 使用API管理器
            from app.wechat.api_manager import APIManager

            # 检查WXID
            if not agent.wxid:
                flash('V2 API需要设置WXID，请联系管理员配置', 'danger')
                return redirect(url_for('wechat.index'))

            # 创建API管理器配置
            agent_config = {
                'id': agent.id,
                'name': agent.name,
                'wechat_base_url': agent.wechat_base_url,
                'proxy': agent.proxy,
                'wxid': agent.wxid,
                'device_id': agent.device_id,
                'wechat_is_active': agent.wechat_is_active
            }

            api_manager = APIManager(agent_config, api_version)

            # 退出登录
            result = api_manager.logout()
            logger.info(f"V2退出登录响应: {json.dumps(result, ensure_ascii=False)}")

        else:
            # V1 API - 使用原有客户端
            # 获取API密钥
            api_key = get_agent_api_key(agent.id)

            if not api_key:
                flash('未设置API密钥，请联系管理员配置', 'danger')
                return redirect(url_for('wechat.index'))

            # 创建微信API客户端
            wechat_client = WeChatClient(agent.wechat_base_url, api_key, agent.proxy)

            # 退出登录
            result = wechat_client.logout()
            logger.info(f"V1退出登录响应: {json.dumps(result, ensure_ascii=False)}")

    except ValueError as e:
        logger.error(f"创建API客户端失败: {str(e)}")
        flash(f'创建微信客户端失败: {str(e)}', 'danger')
        return redirect(url_for('wechat.index'))

    # 更新数据库中的登录状态
    update_agent_login_status(agent.id, 0)
    logger.info(f"{api_version.upper()}更新登录状态为未登录: user_id={agent.id}")

    if result.get('Code') == 200:
        flash(f'已退出微信登录 ({api_version.upper()} API)', 'success')
    else:
        error_msg = result.get("Text", "未知错误")
        logger.warning(f"{api_version.upper()}退出登录失败: {error_msg}")
        flash(f'退出登录失败: {error_msg} ({api_version.upper()} API)', 'warning')

    return redirect(url_for('wechat.index'))


# ==================== V2扫码登录专用路由 ====================

@bp.route('/v2/car-qr-login')
@login_required
def v2_car_qr_login():
    """V2车载扫码登录页面"""
    agent_id = session.get('agent_id')
    api_version = session.get('api_version', 'v1')

    # 检查是否为V2 API
    if api_version != 'v2':
        flash('此功能仅支持V2 API', 'warning')
        return redirect(url_for('wechat.index'))

    # 获取代理商信息
    from app.models import Agent
    agent = Agent.get_by_id(agent_id)

    if not agent:
        flash('用户信息获取失败', 'danger')
        return redirect(url_for('wechat.index'))

    # 检查微信功能是否激活
    if not agent.wechat_is_active:
        flash('微信功能未激活，请联系管理员', 'warning')
        return redirect(url_for('wechat.index'))

    # 检查是否设置了微信API基础URL
    if not agent.wechat_base_url:
        flash('未设置微信API基础URL，请联系管理员配置', 'danger')
        return redirect(url_for('wechat.index'))

    # 检查WXID
    if not agent.wxid:
        flash('V2 API需要设置WXID，请联系管理员配置', 'danger')
        return redirect(url_for('wechat.index'))

    # 解析代理信息
    proxy_info = agent.get_proxy_for_v2_api()

    debug_info = {
        'user_id': agent.id,
        'name': agent.name,
        'base_url': agent.wechat_base_url,
        'proxy': agent.proxy,
        'proxy_parsed': proxy_info,
        'wxid': agent.wxid,
        'device_id': agent.device_id,
        'api_version': api_version
    }

    return render_template('wechat/v2_car_qr_login.html', debug_info=debug_info)


@bp.route('/v2/get-car-qrcode')
@login_required
def v2_get_car_qrcode():
    """获取V2车载登录二维码"""
    agent_id = session.get('agent_id')
    api_version = session.get('api_version', 'v1')

    # 检查是否为V2 API
    if api_version != 'v2':
        return jsonify({
            'Code': -1,
            'Text': '此功能仅支持V2 API',
            'Data': None
        })

    # 获取代理商信息
    from app.models import Agent
    agent = Agent.get_by_id(agent_id)

    if not agent or not agent.wechat_base_url or not agent.wxid:
        return jsonify({
            'Code': -1,
            'Text': '配置错误：缺少必要的配置信息',
            'Data': None
        })

    try:
        # 创建V2客户端
        from app.wechat.client_v2 import WeChatClientV2
        client = WeChatClientV2(base_url=agent.wechat_base_url, wxid=agent.wxid)

        # 解析代理信息
        proxy_info = agent.get_proxy_for_v2_api()

        # 启动车载扫码登录流程
        qr_result = client.start_car_qr_login(
            device_id=agent.device_id,
            device_name=f"YBA_Car_{agent.name}",
            proxy=proxy_info
        )

        logger.info(f"V2车载二维码获取结果: Code={qr_result.get('Code')}")

        # 如果获取成功，存储二维码信息到会话
        # 适配正确的返回数据格式：Code=1表示成功，使用Uuid、QrBase64、ExpiredTime字段
        if qr_result.get('Code') == 1 and qr_result.get('Data'):
            qr_data = qr_result.get('Data', {})
            uuid = qr_data.get('Uuid') or qr_data.get('uuid') or qr_data.get('UUID')

            if uuid:
                session['v2_car_qr_uuid'] = uuid
                session['v2_car_qr_created_time'] = time.time()
                # 解析过期时间，如果是字符串格式则转换为秒数
                expired_time_str = qr_data.get('ExpiredTime', '')
                if expired_time_str:
                    # 如果是时间字符串，计算剩余秒数
                    try:
                        from datetime import datetime
                        expired_time = datetime.strptime(expired_time_str, '%Y-%m-%d %H:%M:%S')
                        current_time = datetime.now()
                        remaining_seconds = int((expired_time - current_time).total_seconds())
                        session['v2_car_qr_expired_time'] = max(remaining_seconds, 120)  # 至少120秒
                    except:
                        session['v2_car_qr_expired_time'] = 120  # 默认120秒
                else:
                    session['v2_car_qr_expired_time'] = 120  # 默认120秒
                logger.info(f"V2车载二维码UUID存储到会话: {uuid}")

        return jsonify(qr_result)

    except Exception as e:
        logger.error(f"获取V2车载二维码失败: {str(e)}")
        return jsonify({
            'Code': 500,
            'Text': f'获取二维码失败: {str(e)}',
            'Data': None
        })


@bp.route('/v2/check-car-qrcode')
@login_required
def v2_check_car_qrcode():
    """检查V2车载二维码扫描状态"""
    agent_id = session.get('agent_id')
    api_version = session.get('api_version', 'v1')

    # 检查是否为V2 API
    if api_version != 'v2':
        return jsonify({
            'Code': -1,
            'Text': '此功能仅支持V2 API',
            'Data': None
        })

    # 检查会话中是否有二维码UUID
    uuid = session.get('v2_car_qr_uuid')
    if not uuid:
        return jsonify({
            'Code': -1,
            'Text': '未找到二维码UUID，请重新获取二维码',
            'Data': None
        })

    # 检查二维码是否过期
    created_time = session.get('v2_car_qr_created_time', 0)
    expired_time = session.get('v2_car_qr_expired_time', 120)

    if created_time > 0 and time.time() - created_time > expired_time:
        logger.warning(f"V2车载二维码已过期: user_id={agent_id}, uuid={uuid}")
        session.pop('v2_car_qr_uuid', None)
        session.pop('v2_car_qr_created_time', None)
        session.pop('v2_car_qr_expired_time', None)
        return jsonify({
            'Code': 200,
            'Data': {
                'status': 'EXPIRED',
                'message': '二维码已过期，请重新获取'
            }
        })

    # 获取代理商信息
    from app.models import Agent
    agent = Agent.get_by_id(agent_id)

    if not agent or not agent.wechat_base_url or not agent.wxid:
        return jsonify({
            'Code': -1,
            'Text': '配置错误：缺少必要的配置信息',
            'Data': None
        })

    try:
        # 创建V2客户端
        from app.wechat.client_v2 import WeChatClientV2
        client = WeChatClientV2(base_url=agent.wechat_base_url, wxid=agent.wxid)

        # 检查二维码状态
        result = client.check_car_qr_status(uuid)
        logger.info(f"V2车载二维码状态检查结果: Code={result.get('Code')}, Data={result.get('Data')}")

        # 检查是否登录成功 - 适配V2正确的返回格式
        if result.get('Code') == 0 and result.get('Success') and result.get('Data'):
            data = result.get('Data', {})

            # V2登录成功的判断逻辑
            login_success = False

            # 检查是否有用户账户信息
            acct_sect_resp = data.get('acctSectResp', {})
            if acct_sect_resp:
                user_name = acct_sect_resp.get('userName', '')
                nick_name = acct_sect_resp.get('nickName', '')

                # 如果有用户名和昵称，说明登录成功
                if user_name and nick_name:
                    login_success = True
                    logger.info(f"V2车载登录成功检测: userName={user_name}, nickName={nick_name}")

            # 兼容其他可能的登录成功格式
            if not login_success:
                if (data.get('status') == 'SCAN_SUCC' or
                    data.get('loginState') == 1 or
                    data.get('state') == 2 or
                    result.get('Message') == '登录成功'):
                    login_success = True

            if login_success:
                logger.info(f"V2车载扫码登录成功: user_id={agent_id}, wxid={agent.wxid}")

                # 完成登录流程：开启自动心跳自动二次登录
                heartbeat_result = client.enable_auto_heartbeat()
                logger.info(f"V2自动心跳开启结果: {heartbeat_result}")

                # 更新数据库中的登录状态
                update_agent_login_status(agent.id, 1)

                # 清除会话中的二维码信息
                session.pop('v2_car_qr_uuid', None)
                session.pop('v2_car_qr_created_time', None)
                session.pop('v2_car_qr_expired_time', None)

                # 在响应中添加心跳开启结果
                result['heartbeat_result'] = heartbeat_result

        return jsonify(result)

    except Exception as e:
        logger.error(f"检查V2车载二维码状态失败: {str(e)}")
        return jsonify({
            'Code': 500,
            'Text': f'检查二维码状态失败: {str(e)}',
            'Data': None
        })


# ==================== V3 API专用路由 ====================

@bp.route('/v3/login')
@login_required
def v3_login():
    """V3 API专用登录页面"""
    agent_id = session.get('agent_id')
    api_version = session.get('api_version', 'v1')

    # 检查是否为V3 API
    if api_version != 'v3':
        flash('此功能仅支持V3 API', 'warning')
        return redirect(url_for('wechat.index'))

    # 获取代理商信息
    from app.models import Agent
    agent = Agent.get_by_id(agent_id)

    if not agent:
        flash('用户信息获取失败', 'danger')
        return redirect(url_for('wechat.index'))

    # 检查微信功能是否激活
    if not agent.wechat_is_active:
        flash('微信功能未激活，请联系管理员', 'warning')
        return redirect(url_for('wechat.index'))

    # 检查是否设置了微信API基础URL
    if not agent.wechat_base_url:
        flash('未设置微信API基础URL，请联系管理员配置', 'danger')
        return redirect(url_for('wechat.index'))

    # 检查token
    token = get_agent_api_key(agent.id)
    if not token:
        flash('V3 API需要设置token，请联系管理员配置', 'danger')
        return redirect(url_for('wechat.index'))

    debug_info = {
        'user_id': agent.id,
        'name': agent.name,
        'base_url': agent.wechat_base_url,
        'proxy': agent.proxy,
        'token': 'Yes' if token else 'No',
        'app_id': getattr(agent, 'app_id', None),
        'api_version': api_version
    }

    return render_template('wechat/v3_login.html', debug_info=debug_info)


@bp.route('/v3/get-token')
@login_required
def v3_get_token():
    """获取V3 API token"""
    agent_id = session.get('agent_id')
    api_version = session.get('api_version', 'v1')

    # 检查是否为V3 API
    if api_version != 'v3':
        return jsonify({
            'ret': -1,
            'msg': '此功能仅支持V3 API',
            'data': None
        })

    # 获取代理商信息
    from app.models import Agent
    agent = Agent.get_by_id(agent_id)

    if not agent or not agent.wechat_base_url:
        return jsonify({
            'ret': -1,
            'msg': '配置错误：缺少必要的配置信息',
            'data': None
        })

    try:
        # 创建V3客户端
        from app.wechat.client_v3 import WeChatClientV3
        client = WeChatClientV3(base_url=agent.wechat_base_url)

        # 获取token
        result = client.get_token()
        logger.info(f"V3获取token结果: ret={result.get('ret')}")

        return jsonify(result)

    except Exception as e:
        logger.error(f"获取V3 token失败: {str(e)}")
        return jsonify({
            'ret': 500,
            'msg': f'获取token失败: {str(e)}',
            'data': None
        })


@bp.route('/v3/set-callback')
@login_required
def v3_set_callback():
    """设置V3 API回调地址"""
    agent_id = session.get('agent_id')
    api_version = session.get('api_version', 'v1')
    callback_url = request.args.get('callback_url')

    # 检查是否为V3 API
    if api_version != 'v3':
        return jsonify({
            'ret': -1,
            'msg': '此功能仅支持V3 API',
            'data': None
        })

    if not callback_url:
        return jsonify({
            'ret': -1,
            'msg': '缺少callback_url参数',
            'data': None
        })

    # 获取代理商信息
    from app.models import Agent
    agent = Agent.get_by_id(agent_id)

    if not agent or not agent.wechat_base_url:
        return jsonify({
            'ret': -1,
            'msg': '配置错误：缺少必要的配置信息',
            'data': None
        })

    try:
        # 获取token
        token = get_agent_api_key(agent.id)
        if not token:
            return jsonify({
                'ret': -1,
                'msg': '未设置token',
                'data': None
            })

        # 创建V3客户端
        from app.wechat.client_v3 import WeChatClientV3
        client = WeChatClientV3(base_url=agent.wechat_base_url, token=token)

        # 设置回调地址
        result = client.set_callback(callback_url)
        logger.info(f"V3设置回调地址结果: ret={result.get('ret')}")

        return jsonify(result)

    except Exception as e:
        logger.error(f"设置V3回调地址失败: {str(e)}")
        return jsonify({
            'ret': 500,
            'msg': f'设置回调地址失败: {str(e)}',
            'data': None
        })


def update_agent_login_status(agent_id, status):
    """更新代理商微信登录状态"""
    connection = get_db_connection()
    try:
        with connection.cursor() as cursor:
            sql = "UPDATE agents SET wechat_login_status = %s WHERE id = %s"
            cursor.execute(sql, (status, agent_id))
            connection.commit()
    except Exception as e:
        logger.error(f"更新登录状态失败: {str(e)}")
    finally:
        connection.close()
