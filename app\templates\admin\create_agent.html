{% extends "base.html" %}

{% block title %}新增代理商 - YBA监控管理系统{% endblock %}

{% block page_title %}
新增代理商
<span class="badge bg-danger ms-2">管理员</span>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- 返回按钮 -->
    <div class="mb-3">
        <a href="{{ url_for('admin.agents') }}" class="btn btn-outline-secondary">
            <i class="bi bi-arrow-left me-1"></i>返回代理商列表
        </a>
    </div>

    <!-- 创建表单 -->
    <div class="row justify-content-center">
        <div class="col-lg-8">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="bi bi-person-plus me-2"></i>新增代理商
                    </h5>
                </div>
                <div class="card-body">
                    <form method="POST" id="createAgentForm">
                        <!-- 基本信息 -->
                        <div class="section-header">
                            <h6 class="text-primary">
                                <i class="bi bi-info-circle me-1"></i>基本信息
                            </h6>
                            <hr>
                        </div>

                        <div class="row mb-3">
                            <div class="col-md-6">
                                <label for="name" class="form-label">代理商名称 <span class="text-danger">*</span></label>
                                <input type="text" class="form-control" id="name" name="name" required>
                                <div class="form-text">用于系统显示的代理商名称</div>
                            </div>
                            <div class="col-md-6">
                                <label for="group" class="form-label">分组</label>
                                <input type="text" class="form-control" id="group" name="group" placeholder="可选">
                                <div class="form-text">用于分类管理代理商</div>
                            </div>
                        </div>

                        <div class="row mb-3">
                            <div class="col-md-6">
                                <label for="contact" class="form-label">联系方式</label>
                                <input type="text" class="form-control" id="contact" name="contact" placeholder="电话/邮箱/微信等">
                            </div>
                            <div class="col-md-6">
                                <label for="api_version" class="form-label">API版本 <span class="text-danger">*</span></label>
                                <select class="form-select" id="api_version" name="api_version" required>
                                    <option value="">请选择API版本</option>
                                    <option value="v1">V1 API (传统版本)</option>
                                    <option value="v2">V2 API (新版本)</option>
                                    <option value="v3">V3 API (GeWeChat)</option>
                                </select>
                                <div class="form-text">选择API版本后将显示对应的配置选项</div>
                            </div>
                        </div>

                        <!-- API配置信息 -->
                        <div class="section-header">
                            <h6 class="text-primary">
                                <i class="bi bi-gear me-1"></i>API配置信息
                            </h6>
                            <div class="form-text mb-2">请根据选择的API版本填写对应的配置信息</div>
                            <hr>
                        </div>

                        <!-- V1 API 配置 -->
                        <div class="api-config mb-4">
                            <div class="section-header">
                                <h6 class="text-info">
                                    <i class="bi bi-gear me-1"></i>V1 API 配置
                                </h6>
                                <div class="form-text mb-2">当选择V1 API版本时，Token和微信API地址为必填项</div>
                                <hr>
                            </div>

                            <div class="row mb-3">
                                <div class="col-md-6">
                                    <label for="token" class="form-label">Token</label>
                                    <input type="text" class="form-control" id="token" name="token" placeholder="V1 API认证令牌">
                                    <div class="form-text">V1 API认证令牌（选择V1时必填）</div>
                                </div>
                                <div class="col-md-6">
                                    <label for="wechat_base_url" class="form-label">V1微信API地址</label>
                                    <input type="url" class="form-control" id="wechat_base_url" name="wechat_base_url"
                                           placeholder="http://example.com:8080">
                                    <div class="form-text">V1微信API服务器地址（选择V1时必填）</div>
                                </div>
                            </div>

                            <div class="row mb-3">
                                <div class="col-md-6">
                                    <label for="proxy" class="form-label">V1代理设置</label>
                                    <input type="text" class="form-control" id="proxy" name="proxy"
                                           placeholder="http://proxy:port (可选)">
                                    <div class="form-text">V1 API代理服务器设置（可选）</div>
                                </div>
                            </div>
                        </div>

                        <!-- V2 API 配置 -->
                        <div class="api-config mb-4">
                            <div class="section-header">
                                <h6 class="text-success">
                                    <i class="bi bi-qr-code me-1"></i>V2 API 配置
                                </h6>
                                <div class="form-text mb-2">当选择V2 API版本时，微信ID和微信API地址为必填项，设备ID为可选</div>
                                <hr>
                            </div>

                            <div class="row mb-3">
                                <div class="col-md-6">
                                    <label for="wxid" class="form-label">微信ID (WXID)</label>
                                    <input type="text" class="form-control" id="wxid" name="wxid"
                                           placeholder="wxid_xxxxxxxxxx">
                                    <div class="form-text">微信账号的唯一标识符（选择V2时必填）</div>
                                </div>
                                <div class="col-md-6">
                                    <label for="device_id" class="form-label">设备ID</label>
                                    <input type="text" class="form-control" id="device_id" name="device_id" placeholder="设备唯一标识符">
                                    <div class="form-text">设备的唯一标识符（可选）</div>
                                </div>
                            </div>

                            <div class="row mb-3">
                                <div class="col-md-6">
                                    <label for="wechat_base_url_v2" class="form-label">V2微信API地址</label>
                                    <input type="url" class="form-control" id="wechat_base_url_v2" name="wechat_base_url_v2"
                                           placeholder="http://example.com:8060">
                                    <div class="form-text">V2微信API服务器地址（选择V2时必填）</div>
                                </div>
                                <div class="col-md-6">
                                    <label for="proxy_v2" class="form-label">V2代理设置</label>
                                    <input type="text" class="form-control" id="proxy_v2" name="proxy_v2"
                                           placeholder="http://proxy:port (可选)">
                                    <div class="form-text">V2 API代理服务器设置（可选）</div>
                                </div>
                            </div>
                        </div>

                        <!-- V3 API 配置 -->
                        <div class="api-config mb-4">
                            <div class="section-header">
                                <h6 class="text-warning">
                                    <i class="bi bi-gear me-1"></i>V3 API 配置 (GeWeChat)
                                </h6>
                                <div class="form-text mb-2">当选择V3 API版本时，Token和微信API地址为必填项，应用ID为可选</div>
                                <hr>
                            </div>

                            <div class="row mb-3">
                                <div class="col-md-6">
                                    <label for="token_v3" class="form-label">V3 Token</label>
                                    <input type="text" class="form-control" id="token_v3" name="token_v3"
                                           placeholder="V3 API认证令牌">
                                    <div class="form-text">V3 API认证令牌（选择V3时必填）</div>
                                </div>
                                <div class="col-md-6">
                                    <label for="app_id" class="form-label">应用ID (App ID)</label>
                                    <input type="text" class="form-control" id="app_id" name="app_id"
                                           placeholder="应用唯一标识符（可选）">
                                    <div class="form-text">GeWeChat应用ID（可选，登录时自动获取）</div>
                                </div>
                            </div>

                            <div class="row mb-3">
                                <div class="col-md-6">
                                    <label for="wechat_base_url_v3" class="form-label">V3微信API地址</label>
                                    <input type="url" class="form-control" id="wechat_base_url_v3" name="wechat_base_url_v3"
                                           placeholder="https://www.geweapi.com/gewe/v2/api">
                                    <div class="form-text">V3微信API服务器地址（选择V3时必填）</div>
                                </div>
                                <div class="col-md-6">
                                    <label for="proxy_v3" class="form-label">V3代理设置</label>
                                    <input type="text" class="form-control" id="proxy_v3" name="proxy_v3"
                                           placeholder="http://proxy:port (可选)">
                                    <div class="form-text">V3 API代理服务器设置（可选）</div>
                                </div>
                            </div>
                        </div>

                        <!-- 状态设置 -->
                        <div class="section-header">
                            <h6 class="text-warning">
                                <i class="bi bi-toggles me-1"></i>状态设置
                            </h6>
                            <hr>
                        </div>

                        <div class="row mb-4">
                            <div class="col-md-6">
                                <div class="form-check form-switch">
                                    <input class="form-check-input" type="checkbox" id="vip_is_active" name="vip_is_active" value="1">
                                    <label class="form-check-label" for="vip_is_active">
                                        VIP功能激活
                                    </label>
                                    <div class="form-text">是否启用VIP相关功能</div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-check form-switch">
                                    <input class="form-check-input" type="checkbox" id="wechat_is_active" name="wechat_is_active" value="1" checked>
                                    <label class="form-check-label" for="wechat_is_active">
                                        微信功能激活
                                    </label>
                                    <div class="form-text">是否启用微信相关功能</div>
                                </div>
                            </div>
                        </div>

                        <!-- 提交按钮 -->
                        <div class="d-flex justify-content-end gap-2">
                            <a href="{{ url_for('admin.agents') }}" class="btn btn-secondary">取消</a>
                            <button type="submit" class="btn btn-primary">
                                <i class="bi bi-check-circle me-1"></i>创建代理商
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_css %}
<style>
.section-header {
    margin-top: 2rem;
    margin-bottom: 1rem;
}

.section-header:first-child {
    margin-top: 0;
}

.section-header h6 {
    margin-bottom: 0.5rem;
    font-weight: 600;
}

.section-header hr {
    margin-top: 0.5rem;
    margin-bottom: 1rem;
    opacity: 0.3;
}

.api-config {
    background: rgba(var(--bs-light-rgb), 0.5);
    border-radius: var(--bs-border-radius);
    padding: 1.5rem;
    margin-bottom: 1rem;
    border: 1px solid var(--bs-border-color);
}

.form-text {
    font-size: 0.875rem;
    color: var(--bs-secondary);
}

.form-check-label {
    font-weight: 500;
}

.card {
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
    border: 1px solid rgba(0, 0, 0, 0.125);
}

.btn-group .btn {
    border-radius: 0;
}

.btn-group .btn:first-child {
    border-top-left-radius: var(--bs-border-radius);
    border-bottom-left-radius: var(--bs-border-radius);
}

.btn-group .btn:last-child {
    border-top-right-radius: var(--bs-border-radius);
    border-bottom-right-radius: var(--bs-border-radius);
}

/* 确保API配置区域的显示/隐藏正常工作 */
.api-config[style*="display: none"] {
    display: none !important;
}

.api-config[style*="display: block"] {
    display: block !important;
}
</style>
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // 简单的表单提交验证
    document.getElementById('createAgentForm').addEventListener('submit', function(e) {
        const apiVersion = document.getElementById('api_version').value;

        if (!apiVersion) {
            e.preventDefault();
            alert('请选择API版本');
            document.getElementById('api_version').focus();
            return;
        }

        // 根据API版本验证相应字段
        if (apiVersion === 'v1') {
            const token = document.getElementById('token').value.trim();
            const baseUrl = document.getElementById('wechat_base_url').value.trim();

            if (!token || !baseUrl) {
                e.preventDefault();
                alert('请填写完整的V1 API配置信息（Token和微信API地址为必填项）');
                return;
            }
        } else if (apiVersion === 'v2') {
            const wxid = document.getElementById('wxid').value.trim();
            const baseUrl = document.getElementById('wechat_base_url_v2').value.trim();

            if (!wxid || !baseUrl) {
                e.preventDefault();
                alert('请填写完整的V2 API配置信息（微信ID和微信API地址为必填项）');
                return;
            }
        } else if (apiVersion === 'v3') {
            const tokenV3 = document.getElementById('token_v3').value.trim();
            const baseUrlV3 = document.getElementById('wechat_base_url_v3').value.trim();

            if (!tokenV3 || !baseUrlV3) {
                e.preventDefault();
                alert('请填写完整的V3 API配置信息（Token和微信API地址为必填项）');
                return;
            }
        }
    });
});
</script>
{% endblock %}
