{% extends "base.html" %}

{% block title %}仪表盘 - YBA监控管理系统{% endblock %}

{% block page_title %}仪表盘{% endblock %}

{% block content %}
<div class="dashboard-container">
    <!-- 页面标题 -->
    <div class="page-header mb-4">
        <h1 class="page-title">
            系统概览
            {% if is_admin %}
            <span class="badge bg-danger ms-2">管理员</span>
            {% else %}
            <span class="badge bg-primary ms-2">{{ current_user_prefix }}</span>
            {% endif %}
            {% if stats.api_info %}
            <span class="badge {% if stats.api_version == 'v2' %}bg-success{% else %}bg-info{% endif %} ms-2">
                {{ stats.api_info.display_name }}
            </span>
            {% endif %}
        </h1>
    </div>

    <!-- 系统概览 -->
    <div class="stats-grid mb-5">
        <div class="row g-4">
            <!-- 微信登录状态 -->
            <div class="col-xl-4 col-md-6">
                <div class="stat-card">
                    <div class="stat-icon {% if stats.wechat_status == 1 %}bg-success{% else %}bg-secondary{% endif %}">
                        <i class="bi bi-wechat"></i>
                    </div>
                    <div class="stat-content">
                        <h3 class="stat-number">
                            {% if stats.wechat_status == 1 %}
                            <span class="text-success">在线</span>
                            {% else %}
                            <span class="text-danger">离线</span>
                            {% endif %}
                        </h3>
                        <p class="stat-label">
                            微信登录状态
                            {% if stats.api_info %}
                            <br><small class="text-muted">{{ stats.api_info.display_name }}</small>
                            {% endif %}
                        </p>
                    </div>
                </div>
            </div>

            <!-- 已激活VIP数量 -->
            <div class="col-xl-4 col-md-6">
                <div class="stat-card">
                    <div class="stat-icon bg-primary">
                        <i class="bi bi-credit-card-2-front"></i>
                    </div>
                    <div class="stat-content">
                        <h3 class="stat-number">{{ stats.active_vip_count or 0 }}</h3>
                        <p class="stat-label">已激活VIP数量</p>
                    </div>
                </div>
            </div>

            <!-- 剩余积分 -->
            <div class="col-xl-4 col-md-6">
                <div class="stat-card">
                    <div class="stat-icon bg-warning">
                        <i class="bi bi-star-fill"></i>
                    </div>
                    <div class="stat-content">
                        <h3 class="stat-number">{{ stats.remaining_points or 0 }}</h3>
                        <p class="stat-label">剩余积分</p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 管理员API统计信息 -->
    {% if is_admin and stats.api_stats %}
    <div class="api-stats-section mb-5">
        <h3 class="section-title">
            <i class="bi bi-diagram-3 me-2"></i>API系统统计
        </h3>
        <div class="row g-4">
            {% for version, data in stats.api_stats.items() %}
            <div class="col-xl-6 col-md-6">
                <div class="api-stat-card">
                    <div class="api-stat-header">
                        <div class="api-version-badge {% if version == 'v2' %}bg-success{% else %}bg-info{% endif %}">
                            {{ version.upper() }}
                        </div>
                        <h5 class="api-stat-title">
                            {% if version == 'v2' %}
                            V2 API (WXID认证)
                            {% else %}
                            V1 API (Token认证)
                            {% endif %}
                        </h5>
                    </div>
                    <div class="api-stat-content">
                        <div class="api-stat-item">
                            <span class="api-stat-label">总代理商</span>
                            <span class="api-stat-value">{{ data.total }}</span>
                        </div>
                        <div class="api-stat-item">
                            <span class="api-stat-label">在线数量</span>
                            <span class="api-stat-value text-success">{{ data.online }}</span>
                        </div>
                        <div class="api-stat-item">
                            <span class="api-stat-label">离线数量</span>
                            <span class="api-stat-value text-danger">{{ data.total - data.online }}</span>
                        </div>
                        <div class="api-stat-progress">
                            <div class="progress">
                                <div class="progress-bar {% if version == 'v2' %}bg-success{% else %}bg-info{% endif %}"
                                     style="width: {% if data.total > 0 %}{{ (data.online / data.total * 100)|round(1) }}{% else %}0{% endif %}%">
                                </div>
                            </div>
                            <small class="text-muted">
                                在线率: {% if data.total > 0 %}{{ (data.online / data.total * 100)|round(1) }}%{% else %}0%{% endif %}
                            </small>
                        </div>
                    </div>
                </div>
            </div>
            {% endfor %}
        </div>
    </div>
    {% endif %}

</div>

<style>
.page-header {
    border-bottom: 1px solid var(--border-color);
    padding-bottom: 1rem;
}

.page-title {
    font-size: 1.75rem;
    font-weight: 600;
    color: var(--text-primary);
    margin: 0;
}

.section-title {
    color: var(--text-primary);
    font-weight: 600;
}

.stat-card {
    background: var(--bg-secondary);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    padding: 1.5rem;
    text-align: center;
}

.stat-icon {
    width: 48px;
    height: 48px;
    border-radius: var(--border-radius);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.25rem;
    color: white;
    margin: 0 auto 1rem;
}

.stat-number {
    font-size: 1.75rem;
    font-weight: 600;
    margin: 0 0 0.5rem;
    color: var(--text-primary);
}

.stat-label {
    margin: 0;
    color: var(--text-secondary);
    font-size: 0.875rem;
}

.section-title {
    color: var(--text-primary);
    font-weight: 600;
    margin-bottom: 1.5rem;
    display: flex;
    align-items: center;
}

.action-card {
    background: var(--bg-secondary);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    padding: 1.5rem;
    display: flex;
    align-items: center;
    gap: 1rem;
    text-decoration: none;
    color: inherit;
    transition: var(--transition);
    box-shadow: var(--shadow-sm);
}

.action-card:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-md);
    color: inherit;
    text-decoration: none;
}

.action-icon {
    width: 50px;
    height: 50px;
    border-radius: 10px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.25rem;
    color: white;
}

.action-content h6 {
    margin: 0 0 0.25rem;
    color: var(--text-primary);
    font-weight: 600;
}

.action-content p {
    margin: 0;
    color: var(--text-secondary);
    font-size: 0.85rem;
}

.activity-card {
    background: var(--bg-secondary);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    box-shadow: var(--shadow-sm);
}

.activity-list {
    padding: 1.5rem;
}

.activity-item {
    display: flex;
    align-items: flex-start;
    gap: 1rem;
    padding: 1rem 0;
    border-bottom: 1px solid var(--border-color);
}

.activity-item:last-child {
    border-bottom: none;
    padding-bottom: 0;
}

.activity-icon {
    width: 40px;
    height: 40px;
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1rem;
    color: white;
    flex-shrink: 0;
}

.activity-content h6 {
    margin: 0 0 0.25rem;
    color: var(--text-primary);
    font-weight: 600;
    font-size: 0.95rem;
}

.activity-content p {
    margin: 0 0 0.25rem;
    font-size: 0.85rem;
}

/* API统计样式 */
.api-stats-section {
    border-top: 1px solid var(--border-color);
    padding-top: 2rem;
}

.api-stat-card {
    background: var(--bg-secondary);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    padding: 1.5rem;
    box-shadow: var(--shadow-sm);
    transition: var(--transition);
}

.api-stat-card:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-md);
}

.api-stat-header {
    display: flex;
    align-items: center;
    gap: 1rem;
    margin-bottom: 1.5rem;
}

.api-version-badge {
    width: 50px;
    height: 50px;
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-weight: 700;
    font-size: 0.9rem;
}

.api-stat-title {
    margin: 0;
    color: var(--text-primary);
    font-weight: 600;
    font-size: 1.1rem;
}

.api-stat-content {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.api-stat-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.5rem 0;
    border-bottom: 1px solid var(--border-color);
}

.api-stat-item:last-child {
    border-bottom: none;
}

.api-stat-label {
    color: var(--text-secondary);
    font-size: 0.9rem;
}

.api-stat-value {
    font-weight: 600;
    font-size: 1.1rem;
    color: var(--text-primary);
}

.api-stat-progress {
    margin-top: 0.5rem;
}

.api-stat-progress .progress {
    height: 8px;
    background-color: var(--border-color);
    border-radius: 4px;
    margin-bottom: 0.5rem;
}

.api-stat-progress .progress-bar {
    border-radius: 4px;
}
</style>

<script>
// 更新当前时间
function updateTime() {
    const now = new Date();
    const timeString = now.toLocaleTimeString('zh-CN', {
        hour12: false,
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit'
    });
    document.getElementById('currentTime').textContent = timeString;
}

// 每秒更新时间
setInterval(updateTime, 1000);
updateTime();

// 统计数字动画
$(document).ready(function() {
    $('.stat-number').each(function() {
        const $this = $(this);
        const countTo = parseInt($this.text()) || 0;
        
        if (countTo > 0) {
            $({ countNum: 0 }).animate({
                countNum: countTo
            }, {
                duration: 2000,
                easing: 'swing',
                step: function() {
                    $this.text(Math.floor(this.countNum));
                },
                complete: function() {
                    $this.text(countTo);
                }
            });
        }
    });
});
</script>
{% endblock %}
