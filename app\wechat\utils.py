"""
微信API工具类
"""
import requests
import base64
import time
import logging

logger = logging.getLogger(__name__)


class WeChatClient:
    """微信API客户端"""
    
    def __init__(self, base_url, api_key, proxy=None):
        """
        初始化微信API客户端
        :param base_url: API基础URL
        :param api_key: API密钥
        :param proxy: 代理服务器地址（可选）
        """
        # 规范化base_url
        if base_url and not (base_url.startswith('http://') or base_url.startswith('https://')):
            base_url = 'http://' + base_url
        
        # 验证API密钥
        if not api_key:
            logger.error("API密钥不能为空")
            raise ValueError("API密钥不能为空")
        
        self.base_url = base_url.rstrip('/')
        self.api_key = api_key
        self.proxy = proxy
        self.timeout = 30
        
        # 存储二维码相关信息
        self.qrcode_key = None
        self.qrcode_expired_time = 0
        self.qrcode_created_time = 0
        
        logger.info(f"初始化WeChatClient: base_url={base_url}, proxy={proxy}")
    
    def _make_request(self, method, endpoint, data=None, use_key_param=True):
        """
        发送HTTP请求

        Args:
            method: HTTP方法
            endpoint: API端点
            data: 请求数据
            use_key_param: 是否使用key参数而不是Authorization头
        """
        # 根据Swagger文档，微信API使用key查询参数而不是Authorization头
        if use_key_param:
            # 构建带key参数的URL
            separator = '&' if '?' in endpoint else '?'
            url = f"{self.base_url}{endpoint}{separator}key={self.api_key}"
            headers = {'Content-Type': 'application/json'}
        else:
            # 使用传统的Authorization头
            url = f"{self.base_url}{endpoint}"
            headers = {
                'Authorization': f'Bearer {self.api_key}',
                'Content-Type': 'application/json'
            }

        logger.debug(f"发送请求: {method} {url}")

        try:
            if method.upper() == 'GET':
                response = requests.get(url, headers=headers, timeout=self.timeout)
            else:
                response = requests.post(url, json=data, headers=headers, timeout=self.timeout)

            logger.debug(f"响应状态码: {response.status_code}")

            # 检查HTTP状态码
            if response.status_code == 404:
                logger.warning(f"API端点不存在: {url}")
                return {
                    'Code': -2,
                    'Text': '该链接不存在！',
                    'Data': None
                }

            response.raise_for_status()

            # 尝试解析JSON响应
            try:
                json_response = response.json()
                logger.debug(f"API响应: {json_response}")
                return json_response
            except ValueError:
                # 如果不是JSON响应，返回文本内容
                logger.warning(f"非JSON响应: {response.text[:200]}")
                return {
                    'Code': 500,
                    'Text': f'非JSON响应: {response.text[:100]}',
                    'Data': None
                }

        except requests.exceptions.ConnectionError as e:
            logger.error(f"连接失败: {str(e)}")
            return {
                'Code': -1,
                'Text': f'连接失败: 无法连接到API服务器 {self.base_url}',
                'Data': None
            }
        except requests.exceptions.Timeout as e:
            logger.error(f"请求超时: {str(e)}")
            return {
                'Code': -1,
                'Text': f'请求超时: API服务器响应超时',
                'Data': None
            }
        except requests.exceptions.RequestException as e:
            logger.error(f"请求失败: {str(e)}")
            return {
                'Code': 500,
                'Text': f'请求失败: {str(e)}',
                'Data': None
            }
        except Exception as e:
            logger.error(f"未知错误: {str(e)}")
            return {
                'Code': 500,
                'Text': f'未知错误: {str(e)}',
                'Data': None
            }
    
    def get_login_status(self):
        """
        获取登录状态
        根据Swagger文档，使用GET /login/GetLoginStatus?key={api_key}
        """
        logger.info("获取登录状态")

        # 根据Swagger文档，使用正确的API调用方式
        result = self._make_request('GET', '/login/GetLoginStatus', use_key_param=True)

        # 如果主要端点失败，尝试其他登录相关端点
        if result.get('Code') == -2 and "该链接不存在" in result.get('Text', ''):
            logger.warning("主要API端点不可用，尝试备用端点...")

            # 根据Swagger文档的其他登录相关端点
            backup_endpoints = [
                '/login/CheckLoginStatus',  # 检测扫码状态
                '/login/GetInItStatus',     # 初始化状态
                '/login/CheckCanSetAlias',  # 检测微信登录环境
            ]

            for endpoint in backup_endpoints:
                logger.info(f"尝试备用端点: {endpoint}")
                backup_result = self._make_request('GET', endpoint, use_key_param=True)

                if backup_result.get('Code') != -2:
                    logger.info(f"备用端点 {endpoint} 可用")
                    return backup_result

            # 尝试使用传统的Authorization头方式
            logger.info("尝试使用Authorization头方式...")
            auth_result = self._make_request('GET', '/login/GetLoginStatus', use_key_param=False)

            if auth_result.get('Code') != -2:
                logger.info("Authorization头方式可用")
                return auth_result

            # 如果所有端点都不可用，返回模拟的离线状态
            logger.warning("所有API端点都不可用，返回模拟离线状态")
            return {
                'Code': 200,
                'Text': 'API服务不可用，显示离线状态',
                'Data': {
                    'loginState': 0,
                    'status': 'offline',
                    'message': 'API服务不可用'
                }
            }

        return result
    
    def get_user_info(self):
        """
        获取用户信息
        根据Swagger文档，使用正确的key参数方式
        """
        logger.info("获取用户信息")
        return self._make_request('GET', '/user/GetProfile', use_key_param=True)
    
    def get_login_qrcode(self):
        """获取登录二维码"""
        logger.info(f"获取登录二维码, proxy={self.proxy}")
        
        # 调用API获取二维码
        data = {
            "Proxy": self.proxy or "",
            "Check": True
        }
        result = self._make_request('POST', '/login/GetLoginQrCodeNewX', data, use_key_param=True)
        logger.info(f"二维码响应: Code={result.get('Code')}")
        
        # 检查是否需要授权码
        if result.get('Code') == -1 and "请提供有效的授权码" in result.get('Text', ''):
            logger.error("获取二维码失败: 需要有效的授权码")
            return {
                'Code': 403,
                'Text': "获取二维码失败: 请提供有效的授权码",
                'Data': None
            }
        
        # 处理未登录状态，直接重新获取二维码
        if result.get('Code') == 300 and "loginState == MMLoginStateNoLogin" in result.get('Text', ''):
            logger.info("检测到未登录状态，重新获取二维码")
            data['Check'] = False
            result = self._make_request('POST', '/login/GetLoginQrCodeNewX', data, use_key_param=True)
            logger.info(f"重新获取二维码响应: Code={result.get('Code')}")
        
        # 处理成功响应
        if result.get('Code') == 200 and result.get('Data'):
            data = result.get('Data', {})
            
            # 提取二维码信息
            qrcode_url = data.get('QrCodeUrl', '')
            qrcode_base64 = data.get('qrCodeBase64', '')
            key = data.get('Key', '')
            expired_time = data.get('expiredTime', 120)
            
            # 如果没有base64编码的二维码，但有URL，则获取图片并转换为base64
            if not qrcode_base64 and qrcode_url:
                try:
                    response = requests.get(qrcode_url, timeout=10)
                    if response.status_code == 200:
                        qrcode_base64 = base64.b64encode(response.content).decode('utf-8')
                except Exception as e:
                    logger.error(f"获取二维码图片失败: {str(e)}")
            
            # 确保二维码数据不包含前缀
            if qrcode_base64 and qrcode_base64.startswith('data:'):
                qrcode_base64 = qrcode_base64.split(',', 1)[1]
            
            # 存储二维码信息
            self.qrcode_key = key
            self.qrcode_expired_time = expired_time
            self.qrcode_created_time = time.time()
            
            return {
                'Code': 200,
                'Text': '获取二维码成功',
                'Data': {
                    'qrcode': qrcode_base64,
                    'uuid': key,
                    'expired_time': expired_time
                }
            }
        
        # 如果仍然是未登录状态，尝试使用另一个API
        if result.get('Code') == 300:
            logger.info("尝试使用备用API获取二维码")
            backup_result = self._make_request('GET', '/login/ShowQrCode')
            
            if backup_result.get('Code') == 200:
                qrcode_base64 = backup_result.get('Data', {}).get('qrcode', '')
                if qrcode_base64:
                    if qrcode_base64.startswith('data:'):
                        qrcode_base64 = qrcode_base64.split(',', 1)[1]
                    
                    return {
                        'Code': 200,
                        'Text': '获取二维码成功',
                        'Data': {
                            'qrcode': qrcode_base64,
                            'uuid': '',
                            'expired_time': 120
                        }
                    }
        
        return result
    
    def check_login_qrcode(self, uuid=None):
        """检查二维码登录状态"""
        logger.info("检查二维码登录状态")
        
        # 检查二维码是否过期
        if self.qrcode_created_time > 0:
            elapsed_time = time.time() - self.qrcode_created_time
            if elapsed_time > self.qrcode_expired_time:
                logger.info(f"二维码已过期: elapsed_time={elapsed_time}s, expired_time={self.qrcode_expired_time}s")
                return {
                    'Code': 200,
                    'Data': {
                        'status': 'EXPIRED',
                        'message': '二维码已过期，请重新获取'
                    },
                    'Text': '二维码已过期'
                }
        
        # 使用检测扫码状态API，根据Swagger文档使用key参数
        result = self._make_request('GET', '/login/CheckLoginStatus', use_key_param=True)
        logger.info(f"检查登录状态响应: {result}")
        
        # 处理特殊的未登录响应
        if result.get('Code') == -2:
            logger.info("检测到特殊未登录响应，返回等待扫描状态")
            return {
                'Code': 200,
                'Data': {
                    'status': 'WAITING_SCAN',
                    'message': '等待扫描'
                },
                'Text': ''
            }
        
        return result
    
    def logout(self):
        """
        退出登录
        根据Swagger文档，使用GET /login/LogOut?key={api_key}
        """
        logger.info("退出微信登录")
        return self._make_request('GET', '/login/LogOut', use_key_param=True)
