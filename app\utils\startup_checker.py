"""
系统启动检查器
在系统启动时检查所有代理商的微信登录状态
"""
import logging
import asyncio
import concurrent.futures
from typing import List, Dict, Tuple
from app.models import get_db_connection
from app.wechat.utils import WeChatClient

logger = logging.getLogger(__name__)


class StartupChecker:
    """启动检查器类"""
    
    def __init__(self):
        self.timeout = 10  # 每个API请求的超时时间
        self.max_workers = 5  # 最大并发检查数
    
    def get_active_agents(self) -> List[Dict]:
        """获取所有激活的微信代理商（支持双API系统）"""
        connection = get_db_connection()
        try:
            with connection.cursor() as cursor:
                # 查询所有激活的代理商（V1和V2）
                cursor.execute("""
                    SELECT id, name, api_version,
                           wechat_base_url, token, wechat_login_status,
                           wxid, device_id
                    FROM agents
                    WHERE wechat_is_active = 1
                    AND wechat_base_url IS NOT NULL
                    AND wechat_base_url != ''
                    AND (
                        (api_version = 'v1' AND token IS NOT NULL AND token != '')
                        OR
                        (api_version = 'v2' AND wxid IS NOT NULL AND wxid != '')
                    )
                    ORDER BY api_version, id
                """)

                agents = []
                for row in cursor.fetchall():
                    agent = {
                        'id': row[0],
                        'name': row[1],
                        'api_version': row[2] or 'v1',  # 默认为v1
                        'wechat_base_url': row[3],
                        'token': row[4],
                        'wechat_login_status': row[5],
                        'wxid': row[6],
                        'device_id': row[7]
                    }

                    # 设置当前状态（V1和V2都使用wechat_login_status字段）
                    agent['current_status'] = agent['wechat_login_status']

                    agents.append(agent)

                return agents
        finally:
            connection.close()
    
    def check_single_agent_status(self, agent: Dict) -> Tuple[int, int, str]:
        """
        检查单个代理商的登录状态（支持双API系统）

        Returns:
            Tuple[agent_id, login_status, message]
        """
        agent_id = agent['id']
        agent_name = agent['name']
        api_version = agent.get('api_version', 'v1')

        try:
            # 根据API版本创建对应的客户端
            if api_version == 'v2':
                # 第二套API客户端
                from app.wechat.api_manager import APIManager

                api_manager = APIManager(agent, api_version)
                result = api_manager.get_login_status()

                message_prefix = f"[V2] {agent_name}"

            else:
                # 第一套API客户端
                from app.wechat.utils import WeChatClient

                client = WeChatClient(
                    base_url=agent['wechat_base_url'],
                    api_key=agent['token']
                )

                # 设置超时时间
                client.timeout = self.timeout

                # 获取登录状态
                result = client.get_login_status()

                message_prefix = f"[V1] {agent_name}"

            # 解析响应
            if result.get('Code') == 200:
                # 成功获取状态
                data = result.get('Data', {})
                login_state = data.get('loginState', 0) if data else 0

                # 转换为数据库状态值 (1=在线, 0=离线)
                db_status = 1 if login_state in [1, 2, 3] else 0

                message = f"[OK] {message_prefix}: {'在线' if db_status else '离线'}"
                logger.info(message)

                return agent_id, db_status, message

            elif result.get('Code') == -1:
                # 连接失败
                message = f"[WARN] {message_prefix}: 连接失败"
                logger.warning(message)
                return agent_id, 0, message

            elif result.get('Code') == -2:
                # API端点不存在
                message = f"[WARN] {message_prefix}: API端点不可用"
                logger.warning(message)
                return agent_id, 0, message

            else:
                # 其他错误
                message = f"[WARN] {message_prefix}: API错误 (Code: {result.get('Code')})"
                logger.warning(message)
                return agent_id, 0, message

        except Exception as e:
            message = f"[ERROR] [{api_version.upper()}] {agent_name}: 检查异常 - {str(e)}"
            logger.error(message)
            return agent_id, 0, message
    
    def update_agent_status(self, agent_id: int, login_status: int, api_version: str = 'v1') -> bool:
        """更新代理商的登录状态（V1和V2都使用同一个状态字段）"""
        connection = get_db_connection()
        try:
            with connection.cursor() as cursor:
                # V1和V2都使用wechat_login_status字段
                cursor.execute("""
                    UPDATE agents
                    SET wechat_login_status = %s,
                        last_check_time = NOW()
                    WHERE id = %s
                """, (login_status, agent_id))

                connection.commit()
                return True
        except Exception as e:
            logger.error(f"更新代理商 {agent_id} ({api_version}) 状态失败: {str(e)}")
            connection.rollback()
            return False
        finally:
            connection.close()
    
    def check_all_agents_parallel(self, agents: List[Dict]) -> Dict:
        """并行检查所有代理商的状态"""
        results = {
            'total': len(agents),
            'online': 0,
            'offline': 0,
            'errors': 0,
            'details': []
        }
        
        if not agents:
            logger.info("没有找到需要检查的代理商")
            return results
        
        logger.info(f"开始并行检查 {len(agents)} 个代理商的登录状态...")
        
        # 使用线程池并行检查
        with concurrent.futures.ThreadPoolExecutor(max_workers=self.max_workers) as executor:
            # 提交所有检查任务
            future_to_agent = {
                executor.submit(self.check_single_agent_status, agent): agent 
                for agent in agents
            }
            
            # 收集结果
            for future in concurrent.futures.as_completed(future_to_agent, timeout=30):
                agent = future_to_agent[future]
                try:
                    agent_id, login_status, message = future.result()
                    
                    # 更新数据库状态
                    api_version = agent.get('api_version', 'v1')
                    if self.update_agent_status(agent_id, login_status, api_version):
                        if login_status == 1:
                            results['online'] += 1
                        else:
                            results['offline'] += 1
                    else:
                        results['errors'] += 1
                    
                    results['details'].append({
                        'agent_id': agent_id,
                        'agent_name': agent['name'],
                        'status': login_status,
                        'message': message
                    })
                    
                except Exception as e:
                    logger.error(f"检查代理商 {agent['name']} 时发生异常: {str(e)}")
                    results['errors'] += 1
                    results['details'].append({
                        'agent_id': agent['id'],
                        'agent_name': agent['name'],
                        'status': 0,
                        'message': f"检查异常: {str(e)}"
                    })
        
        return results
    
    def run_startup_check(self) -> Dict:
        """运行启动检查"""
        logger.info("=== 启动时微信登录状态检查 ===")
        
        try:
            # 获取所有激活的代理商
            agents = self.get_active_agents()
            
            if not agents:
                logger.info("没有找到激活的微信代理商")
                return {
                    'success': True,
                    'total': 0,
                    'online': 0,
                    'offline': 0,
                    'errors': 0,
                    'message': '没有需要检查的代理商'
                }
            
            # 并行检查所有代理商
            results = self.check_all_agents_parallel(agents)
            
            # 输出总结
            logger.info("=== 检查结果总结 ===")
            logger.info(f"总计: {results['total']} 个代理商")
            logger.info(f"在线: {results['online']} 个")
            logger.info(f"离线: {results['offline']} 个")
            if results['errors'] > 0:
                logger.warning(f"错误: {results['errors']} 个")
            
            # 显示详细结果
            if logger.isEnabledFor(logging.INFO):
                logger.info("详细状态:")
                for detail in results['details']:
                    logger.info(f"  {detail['message']}")
            
            results['success'] = True
            results['message'] = f"检查完成: {results['online']}在线, {results['offline']}离线"
            
            return results
            
        except Exception as e:
            error_msg = f"启动检查失败: {str(e)}"
            logger.error(error_msg)
            return {
                'success': False,
                'total': 0,
                'online': 0,
                'offline': 0,
                'errors': 1,
                'message': error_msg
            }


def run_startup_check() -> Dict:
    """启动检查的便捷函数"""
    checker = StartupChecker()
    return checker.run_startup_check()


def quick_status_check() -> Dict:
    """快速状态检查（用于健康检查，支持双API系统）"""
    try:
        connection = get_db_connection()
        with connection.cursor() as cursor:
            # 统计当前状态（包括V1和V2）
            cursor.execute("""
                SELECT
                    COUNT(*) as total,
                    SUM(CASE WHEN wechat_login_status = 1 THEN 1 ELSE 0 END) as online,
                    SUM(CASE WHEN wechat_login_status = 0 THEN 1 ELSE 0 END) as offline,
                    SUM(CASE WHEN api_version = 'v1' OR api_version IS NULL THEN 1 ELSE 0 END) as v1_count,
                    SUM(CASE WHEN api_version = 'v2' THEN 1 ELSE 0 END) as v2_count
                FROM agents
                WHERE wechat_is_active = 1
            """)

            result = cursor.fetchone()
            total, online, offline, v1_count, v2_count = result if result else (0, 0, 0, 0, 0)

            return {
                'success': True,
                'total': total or 0,
                'online': online or 0,
                'offline': offline or 0,
                'v1_count': v1_count or 0,
                'v2_count': v2_count or 0,
                'message': f"当前状态: {online}在线, {offline}离线 (V1:{v1_count}, V2:{v2_count})"
            }

    except Exception as e:
        return {
            'success': False,
            'total': 0,
            'online': 0,
            'offline': 0,
            'v1_count': 0,
            'v2_count': 0,
            'message': f"状态查询失败: {str(e)}"
        }
    finally:
        if 'connection' in locals():
            connection.close()
