{% extends "base.html" %}

{% block title %}代理商管理 - YBA监控管理系统{% endblock %}

{% block page_title %}
代理商管理
<span class="badge bg-danger ms-2">管理员</span>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- 操作栏 -->
    <div class="action-section mb-4">
        <div class="d-flex justify-content-between align-items-center">
            <div>
                <h5 class="mb-0">代理商管理</h5>
                <small class="text-muted">管理系统中的所有代理商账户</small>
            </div>
            <div>
                <a href="{{ url_for('admin.create_agent') }}" class="btn btn-success">
                    <i class="bi bi-plus-circle me-1"></i>新增代理商
                </a>
            </div>
        </div>
    </div>

    <!-- 统计信息 -->
    <div class="stats-section mb-4">
        <div class="row g-3">
            <div class="col-md-3">
                <div class="stat-card">
                    <div class="stat-icon bg-primary">
                        <i class="bi bi-people"></i>
                    </div>
                    <div class="stat-content">
                        <h3 class="stat-number">{{ total }}</h3>
                        <p class="stat-label">总代理商数</p>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="stat-card">
                    <div class="stat-icon bg-info">
                        <i class="bi bi-shield-check"></i>
                    </div>
                    <div class="stat-content">
                        <h3 class="stat-number">
                            {% set v1_count = agents|selectattr('api_version', 'equalto', 'v1')|list|length %}
                            {% set v1_null_count = agents|selectattr('api_version', 'none')|list|length %}
                            {{ v1_count + v1_null_count }}
                        </h3>
                        <p class="stat-label">V1 API代理商</p>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="stat-card">
                    <div class="stat-icon bg-success">
                        <i class="bi bi-qr-code"></i>
                    </div>
                    <div class="stat-content">
                        <h3 class="stat-number">
                            {{ agents|selectattr('api_version', 'equalto', 'v2')|list|length }}
                        </h3>
                        <p class="stat-label">V2 API代理商</p>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="stat-card">
                    <div class="stat-icon bg-warning">
                        <i class="bi bi-wifi"></i>
                    </div>
                    <div class="stat-content">
                        <h3 class="stat-number">
                            {{ agents|selectattr('wechat_login_status', 'equalto', 1)|list|length }}
                        </h3>
                        <p class="stat-label">在线代理商</p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 代理商列表 -->
    <div class="agents-section">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="bi bi-list me-2"></i>代理商列表
                    <span class="badge bg-secondary ms-2">共 {{ total }} 个代理商</span>
                </h5>
            </div>
            <div class="card-body p-0">
                {% if agents %}
                <div class="table-responsive">
                    <table class="table table-hover mb-0">
                        <thead class="table-light">
                            <tr>
                                <th>ID</th>
                                <th>代理商名称</th>
                                <th>API版本</th>
                                <th>认证信息</th>
                                <th>分组</th>
                                <th>微信状态</th>
                                <th>VIP状态</th>
                                <th>联系方式</th>
                                <th>创建时间</th>
                                <th>操作</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for agent in agents %}
                            <tr>
                                <td>{{ agent[0] }}</td>
                                <td>
                                    <strong>{{ agent[1] }}</strong>
                                    {% if agent[0] == 0 %}
                                    <span class="badge bg-danger ms-1">管理员</span>
                                    {% endif %}
                                </td>
                                <td>
                                    {% set api_version = agent[12] or 'v1' %}
                                    <span class="badge {% if api_version == 'v2' %}bg-success{% else %}bg-info{% endif %}">
                                        {% if api_version == 'v2' %}
                                        V2 API
                                        {% else %}
                                        V1 API
                                        {% endif %}
                                    </span>
                                </td>
                                <td>
                                    {% if api_version == 'v2' %}
                                    <small class="text-muted">
                                        WXID: {{ agent[13] or '未设置' }}<br>
                                        设备ID: {{ agent[14][:8] + '...' if agent[14] else '未设置' }}
                                    </small>
                                    {% else %}
                                    <small class="text-muted">
                                        Token: {{ agent[2][:8] + '...' if agent[2] else '未设置' }}
                                    </small>
                                    {% endif %}
                                </td>
                                <td>
                                    {% if agent[6] %}
                                    <span class="badge bg-secondary">{{ agent[6] }}</span>
                                    {% else %}
                                    <span class="text-muted">未分组</span>
                                    {% endif %}
                                </td>
                                <td>
                                    {% if agent[4] == 1 %}
                                    <span class="badge bg-success">
                                        <i class="bi bi-wifi me-1"></i>在线
                                    </span>
                                    {% else %}
                                    <span class="badge bg-danger">
                                        <i class="bi bi-wifi-off me-1"></i>离线
                                    </span>
                                    {% endif %}
                                </td>
                                <td>
                                    {% if agent[7] == 1 %}
                                    <span class="badge bg-success">
                                        <i class="bi bi-check-circle me-1"></i>激活
                                    </span>
                                    {% else %}
                                    <span class="badge bg-secondary">
                                        <i class="bi bi-x-circle me-1"></i>未激活
                                    </span>
                                    {% endif %}
                                </td>
                                <td>
                                    {% if agent[9] %}
                                    <small>{{ agent[9] }}</small>
                                    {% else %}
                                    <span class="text-muted">未设置</span>
                                    {% endif %}
                                </td>
                                <td>
                                    {% if agent[10] %}
                                    <small>{{ agent[10].strftime('%Y-%m-%d %H:%M') }}</small>
                                    {% else %}
                                    <span class="text-muted">未知</span>
                                    {% endif %}
                                </td>
                                <td>
                                    <div class="btn-group btn-group-sm">
                                        <a href="{{ url_for('admin.edit_agent', agent_id=agent[0]) }}" 
                                           class="btn btn-outline-primary" title="编辑">
                                            <i class="bi bi-pencil"></i>
                                        </a>
                                        {% if agent[0] != 0 %}
                                        <button type="button" class="btn btn-outline-danger" 
                                                onclick="deleteAgent({{ agent[0] }}, '{{ agent[1] }}')" title="删除">
                                            <i class="bi bi-trash"></i>
                                        </button>
                                        {% endif %}
                                    </div>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
                {% else %}
                <div class="text-center py-5">
                    <i class="bi bi-inbox display-1 text-muted"></i>
                    <h5 class="mt-3 text-muted">暂无代理商数据</h5>
                    <p class="text-muted">
                        点击上方"新增代理商"按钮添加第一个代理商
                    </p>
                </div>
                {% endif %}
            </div>
        </div>
    </div>

    <!-- 分页 -->
    {% if total_pages > 1 %}
    <div class="pagination-section mt-4">
        <nav aria-label="代理商列表分页">
            <ul class="pagination justify-content-center">
                {% if has_prev %}
                <li class="page-item">
                    <a class="page-link" href="{{ url_for('admin.agents', page=page-1) }}">
                        <i class="bi bi-chevron-left"></i>
                    </a>
                </li>
                {% endif %}
                
                {% for p in range(1, total_pages + 1) %}
                {% if p == page %}
                <li class="page-item active">
                    <span class="page-link">{{ p }}</span>
                </li>
                {% elif p <= 3 or p >= total_pages - 2 or (p >= page - 2 and p <= page + 2) %}
                <li class="page-item">
                    <a class="page-link" href="{{ url_for('admin.agents', page=p) }}">{{ p }}</a>
                </li>
                {% elif p == 4 or p == total_pages - 3 %}
                <li class="page-item disabled">
                    <span class="page-link">...</span>
                </li>
                {% endif %}
                {% endfor %}
                
                {% if has_next %}
                <li class="page-item">
                    <a class="page-link" href="{{ url_for('admin.agents', page=page+1) }}">
                        <i class="bi bi-chevron-right"></i>
                    </a>
                </li>
                {% endif %}
            </ul>
        </nav>
    </div>
    {% endif %}
</div>

<!-- 删除确认模态框 -->
<div class="modal fade" id="deleteModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">确认删除</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <p>确定要删除代理商 <strong id="deleteAgentName"></strong> 吗？</p>
                <p class="text-danger">
                    <i class="bi bi-exclamation-triangle me-1"></i>
                    此操作不可撤销，将同时删除该代理商的所有相关数据。
                </p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                <button type="button" class="btn btn-danger" id="confirmDelete">确认删除</button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_css %}
<style>
/* 操作区域样式 */
.action-section {
    padding: 1rem 0;
}

/* 统计卡片样式 */
.stat-card {
    background: var(--bg-secondary);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    padding: 1.5rem;
    display: flex;
    align-items: center;
    gap: 1rem;
    box-shadow: var(--shadow-sm);
    transition: var(--transition);
}

.stat-card:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-md);
}

.stat-icon {
    width: 60px;
    height: 60px;
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1.5rem;
}

.stat-content {
    flex: 1;
}

.stat-number {
    font-size: 2rem;
    font-weight: 700;
    margin: 0;
    color: var(--text-primary);
}

.stat-label {
    margin: 0;
    color: var(--text-secondary);
    font-size: 0.9rem;
}

/* 表格样式 */
.table {
    font-size: 0.9rem;
}

.table th {
    background: var(--bg-secondary);
    border-bottom: 2px solid var(--border-color);
    font-weight: 600;
    color: var(--text-primary);
    white-space: nowrap;
}

.table td {
    vertical-align: middle;
    border-bottom: 1px solid var(--border-color);
}

.table tbody tr:hover {
    background: rgba(var(--primary-rgb), 0.05);
}

/* 徽章样式 */
.badge {
    font-size: 0.75rem;
    padding: 0.35em 0.65em;
}

/* 按钮组样式 */
.btn-group-sm .btn {
    padding: 0.25rem 0.5rem;
    font-size: 0.75rem;
}

/* 分页样式 */
.pagination {
    margin: 0;
}

.page-link {
    color: var(--primary);
    border-color: var(--border-color);
}

.page-link:hover {
    color: var(--primary-dark);
    background-color: rgba(var(--primary-rgb), 0.1);
    border-color: var(--primary);
}

.page-item.active .page-link {
    background-color: var(--primary);
    border-color: var(--primary);
}

/* 响应式调整 */
@media (max-width: 768px) {
    .stat-card {
        flex-direction: column;
        text-align: center;
    }

    .stat-icon {
        width: 50px;
        height: 50px;
        font-size: 1.25rem;
    }

    .stat-number {
        font-size: 1.5rem;
    }

    .table-responsive {
        font-size: 0.8rem;
    }

    .btn-group-sm .btn {
        padding: 0.2rem 0.4rem;
        font-size: 0.7rem;
    }
}

/* 空状态样式 */
.text-center.py-5 {
    padding: 3rem 1rem !important;
}

.text-center.py-5 .display-1 {
    font-size: 4rem;
    opacity: 0.3;
}
</style>
{% endblock %}

{% block extra_js %}
<script>
// 删除代理商功能
let deleteAgentId = null;

function deleteAgent(agentId, agentName) {
    deleteAgentId = agentId;
    document.getElementById('deleteAgentName').textContent = agentName;

    const modal = new bootstrap.Modal(document.getElementById('deleteModal'));
    modal.show();
}

document.getElementById('confirmDelete').addEventListener('click', function() {
    if (deleteAgentId) {
        // 发送删除请求
        fetch(`/admin/agents/${deleteAgentId}/delete`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRFToken': '{{ csrf_token() }}'
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                // 删除成功，刷新页面
                location.reload();
            } else {
                alert('删除失败: ' + data.message);
            }
        })
        .catch(error => {
            console.error('删除请求失败:', error);
            alert('删除请求失败，请重试');
        });

        // 关闭模态框
        const modal = bootstrap.Modal.getInstance(document.getElementById('deleteModal'));
        modal.hide();
    }
});

// 页面功能增强
document.addEventListener('DOMContentLoaded', function() {
    console.log('代理商管理页面已加载');
});

// 表格行点击效果
document.querySelectorAll('.table tbody tr').forEach(row => {
    row.addEventListener('click', function(e) {
        // 如果点击的是按钮，不触发行点击
        if (e.target.closest('.btn-group')) {
            return;
        }

        // 添加选中效果
        document.querySelectorAll('.table tbody tr').forEach(r => r.classList.remove('table-active'));
        this.classList.add('table-active');
    });
});

// 工具提示初始化
document.addEventListener('DOMContentLoaded', function() {
    const tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
    tooltipTriggerList.map(function(tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });
});
</script>
{% endblock %}
