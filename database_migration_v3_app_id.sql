-- V3 API支持：为agents表添加app_id字段
-- 执行时间：2025-01-XX
-- 描述：为支持V3 API (GeWeChat)，在agents表中添加app_id字段

-- 检查app_id字段是否已存在，如果不存在则添加
SET @column_exists = (
    SELECT COUNT(*)
    FROM INFORMATION_SCHEMA.COLUMNS
    WHERE TABLE_SCHEMA = DATABASE()
    AND TABLE_NAME = 'agents'
    AND COLUMN_NAME = 'app_id'
);

-- 如果字段不存在，则添加
SET @sql = IF(@column_exists = 0,
    'ALTER TABLE agents ADD COLUMN app_id VARCHAR(100) DEFAULT NULL COMMENT ''V3 API应用ID'' AFTER device_id',
    'SELECT ''app_id字段已存在，跳过添加'' AS message'
);

PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 检查api_version字段是否已存在，如果不存在则添加
SET @api_version_exists = (
    SELECT COUNT(*)
    FROM INFORMATION_SCHEMA.COLUMNS
    WHERE TABLE_SCHEMA = DATABASE()
    AND TABLE_NAME = 'agents'
    AND COLUMN_NAME = 'api_version'
);

-- 如果api_version字段不存在，则添加
SET @sql2 = IF(@api_version_exists = 0,
    'ALTER TABLE agents ADD COLUMN api_version VARCHAR(10) DEFAULT ''v1'' COMMENT ''API版本(v1/v2/v3)'' AFTER proxy',
    'SELECT ''api_version字段已存在，跳过添加'' AS message'
);

PREPARE stmt2 FROM @sql2;
EXECUTE stmt2;
DEALLOCATE PREPARE stmt2;

-- 检查wxid字段是否已存在，如果不存在则添加
SET @wxid_exists = (
    SELECT COUNT(*)
    FROM INFORMATION_SCHEMA.COLUMNS
    WHERE TABLE_SCHEMA = DATABASE()
    AND TABLE_NAME = 'agents'
    AND COLUMN_NAME = 'wxid'
);

-- 如果wxid字段不存在，则添加
SET @sql3 = IF(@wxid_exists = 0,
    'ALTER TABLE agents ADD COLUMN wxid VARCHAR(100) DEFAULT NULL COMMENT ''V2 API微信ID'' AFTER api_version',
    'SELECT ''wxid字段已存在，跳过添加'' AS message'
);

PREPARE stmt3 FROM @sql3;
EXECUTE stmt3;
DEALLOCATE PREPARE stmt3;

-- 检查device_id字段是否已存在，如果不存在则添加
SET @device_id_exists = (
    SELECT COUNT(*)
    FROM INFORMATION_SCHEMA.COLUMNS
    WHERE TABLE_SCHEMA = DATABASE()
    AND TABLE_NAME = 'agents'
    AND COLUMN_NAME = 'device_id'
);

-- 如果device_id字段不存在，则添加
SET @sql4 = IF(@device_id_exists = 0,
    'ALTER TABLE agents ADD COLUMN device_id VARCHAR(100) DEFAULT NULL COMMENT ''V2 API设备ID'' AFTER wxid',
    'SELECT ''device_id字段已存在，跳过添加'' AS message'
);

PREPARE stmt4 FROM @sql4;
EXECUTE stmt4;
DEALLOCATE PREPARE stmt4;

-- 显示当前agents表结构
SELECT 
    COLUMN_NAME as '字段名',
    DATA_TYPE as '数据类型',
    IS_NULLABLE as '允许空值',
    COLUMN_DEFAULT as '默认值',
    COLUMN_COMMENT as '注释'
FROM INFORMATION_SCHEMA.COLUMNS
WHERE TABLE_SCHEMA = DATABASE()
AND TABLE_NAME = 'agents'
ORDER BY ORDINAL_POSITION;

-- 显示完成信息
SELECT 'V3 API数据库迁移完成！agents表已支持app_id字段' AS '迁移结果';
