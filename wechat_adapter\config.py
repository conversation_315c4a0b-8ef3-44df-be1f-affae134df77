"""
适配器配置模块

定义了适配器的配置结构，支持不同版本的API配置。
"""

from dataclasses import dataclass, field
from typing import Optional, Dict, Any
from enum import Enum


class ApiVersion(Enum):
    """API版本枚举"""
    V1 = "v1"
    V2 = "v2"
    V3 = "v3"


@dataclass
class RabbitMQConfig:
    """RabbitMQ配置"""
    host: str = "localhost"
    port: int = 5672
    username: str = "guest"
    password: str = "guest"
    virtual_host: str = "/"
    queue_prefix: str = "wx_messages_"
    exchange: str = ""
    routing_key: str = ""
    auto_ack: bool = True
    prefetch_count: int = 1
    
    def to_dict(self) -> Dict[str, Any]:
        return {
            "host": self.host,
            "port": self.port,
            "username": self.username,
            "password": self.password,
            "virtual_host": self.virtual_host,
            "queue_prefix": self.queue_prefix,
            "exchange": self.exchange,
            "routing_key": self.routing_key,
            "auto_ack": self.auto_ack,
            "prefetch_count": self.prefetch_count
        }


@dataclass
class ApiConfig:
    """API配置"""
    base_url: str
    token: Optional[str] = None
    key: Optional[str] = None  # V1版本使用
    wxid: Optional[str] = None  # V2版本使用
    app_id: Optional[str] = None  # V3版本使用
    timeout: int = 30
    retry_count: int = 3
    retry_delay: float = 1.0
    headers: Dict[str, str] = field(default_factory=dict)
    
    def to_dict(self) -> Dict[str, Any]:
        return {
            "base_url": self.base_url,
            "token": self.token,
            "key": self.key,
            "wxid": self.wxid,
            "app_id": self.app_id,
            "timeout": self.timeout,
            "retry_count": self.retry_count,
            "retry_delay": self.retry_delay,
            "headers": self.headers
        }


@dataclass
class AdapterConfig:
    """适配器主配置"""
    version: ApiVersion
    api_config: ApiConfig
    rabbitmq_config: Optional[RabbitMQConfig] = None
    enable_message_listener: bool = False
    enable_auto_retry: bool = True
    enable_logging: bool = True
    log_level: str = "INFO"
    message_callback: Optional[callable] = None
    error_callback: Optional[callable] = None
    
    def __post_init__(self):
        """初始化后处理"""
        if isinstance(self.version, str):
            self.version = ApiVersion(self.version.lower())
    
    @classmethod
    def create_v1_config(cls, base_url: str, key: str, **kwargs) -> 'AdapterConfig':
        """创建V1版本配置"""
        api_config = ApiConfig(base_url=base_url, key=key)
        return cls(version=ApiVersion.V1, api_config=api_config, **kwargs)
    
    @classmethod
    def create_v2_config(cls, base_url: str, wxid: str, **kwargs) -> 'AdapterConfig':
        """创建V2版本配置"""
        api_config = ApiConfig(base_url=base_url, wxid=wxid)
        return cls(version=ApiVersion.V2, api_config=api_config, **kwargs)
    
    @classmethod
    def create_v3_config(cls, base_url: str, token: str, app_id: str, **kwargs) -> 'AdapterConfig':
        """创建V3版本配置"""
        api_config = ApiConfig(base_url=base_url, token=token, app_id=app_id)
        return cls(version=ApiVersion.V3, api_config=api_config, **kwargs)
    
    def to_dict(self) -> Dict[str, Any]:
        return {
            "version": self.version.value,
            "api_config": self.api_config.to_dict(),
            "rabbitmq_config": self.rabbitmq_config.to_dict() if self.rabbitmq_config else None,
            "enable_message_listener": self.enable_message_listener,
            "enable_auto_retry": self.enable_auto_retry,
            "enable_logging": self.enable_logging,
            "log_level": self.log_level
        }


# 预定义配置模板
DEFAULT_V1_CONFIG = {
    "base_url": "http://*************:8080",
    "timeout": 30,
    "retry_count": 3
}

DEFAULT_V2_CONFIG = {
    "base_url": "http://*************:8060/api",
    "timeout": 30,
    "retry_count": 3
}

DEFAULT_V3_CONFIG = {
    "base_url": "http://localhost:2531/v2/api",
    "timeout": 30,
    "retry_count": 3
}

DEFAULT_RABBITMQ_CONFIG = {
    "host": "localhost",
    "port": 5672,
    "username": "guest",
    "password": "guest",
    "queue_prefix": "wx_messages_"
}
