"""
YBA监控管理系统
整合微信登录管理和VIP会员管理功能
"""
from flask import Flask, session
from flask_session import Session
import os
from app.utils.env_loader import EnvLoader


def create_app():
    """创建Flask应用实例"""
    # 环境变量已在start.py中加载，这里不重复加载

    app = Flask(__name__)
    
    # 基础配置
    app.config['SECRET_KEY'] = os.environ.get('SECRET_KEY', 'unified-admin-system-secret-key')
    app.config['SESSION_TYPE'] = 'filesystem'
    app.config['SESSION_PERMANENT'] = False
    app.config['SESSION_USE_SIGNER'] = True
    app.config['SESSION_FILE_DIR'] = './flask_session'
    
    # 配置JSON编码器，确保中文不被转义
    app.json.ensure_ascii = False
    
    # 初始化Session
    Session(app)
    
    # 导入配置
    from app.config import Config
    app.config.from_object(Config)
    
    # 注册蓝图
    from app.auth import bp as auth_bp
    app.register_blueprint(auth_bp, url_prefix='/auth')
    
    from app.wechat import bp as wechat_bp
    app.register_blueprint(wechat_bp, url_prefix='/wechat')
    
    from app.vip import bp as vip_bp
    app.register_blueprint(vip_bp, url_prefix='/vip')
    
    from app.admin import bp as admin_bp
    app.register_blueprint(admin_bp, url_prefix='/admin')
    
    # 主页路由
    @app.route('/')
    def index():
        """主页重定向到仪表盘"""
        from flask import redirect, url_for
        if 'logged_in' in session and session['logged_in']:
            return redirect(url_for('auth.dashboard'))
        return redirect(url_for('auth.login'))
    
    # 全局模板变量
    @app.context_processor
    def inject_user():
        """注入用户信息到模板"""
        return dict(
            current_user_name=session.get('agent_name', ''),
            current_user_prefix=session.get('agent_prefix', ''),
            is_admin=session.get('is_admin', False),
            logged_in=session.get('logged_in', False)
        )

    # 添加模板过滤器
    @app.template_filter('timestamp_to_datetime')
    def timestamp_to_datetime(timestamp):
        """将时间戳转换为可读的日期时间格式"""
        if not timestamp:
            return '未知'
        try:
            from datetime import datetime
            # 如果是字符串，尝试转换为整数
            if isinstance(timestamp, str):
                timestamp = int(timestamp)
            # 转换时间戳为日期时间
            dt = datetime.fromtimestamp(timestamp)
            return dt.strftime('%Y-%m-%d %H:%M:%S')
        except (ValueError, TypeError, OSError):
            return str(timestamp)
    
    return app
