{% extends "base.html" %}

{% block title %}VIP卡详情 - {{ card.card_number }}{% endblock %}

{% block page_title %}VIP卡详情{% endblock %}

{% block content %}
<div class="card-detail-container">
    <div class="row">
        <!-- 卡片基本信息 -->
        <div class="col-lg-8">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">
                        <i class="bi bi-credit-card-2-front me-2"></i>卡片信息
                    </h5>
                    <div class="btn-group">
                        <button type="button" class="btn btn-outline-primary btn-sm" onclick="editCard()">
                            <i class="bi bi-pencil me-1"></i>编辑
                        </button>
                        <a href="{{ url_for('vip.cards') }}" class="btn btn-outline-secondary btn-sm">
                            <i class="bi bi-arrow-left me-1"></i>返回列表
                        </a>
                    </div>
                </div>
                <div class="card-body">
                    <div class="row g-4">
                        <div class="col-md-6">
                            <div class="info-item">
                                <label class="info-label">卡号</label>
                                <div class="info-value">
                                    <code class="text-primary">{{ card.card_number }}</code>
                                    <button class="btn btn-link btn-sm p-0 ms-2" onclick="copyToClipboard('{{ card.card_number }}')">
                                        <i class="bi bi-clipboard"></i>
                                    </button>
                                </div>
                            </div>
                        </div>
                        
                        <div class="col-md-6">
                            <div class="info-item">
                                <label class="info-label">绑定用户</label>
                                <div class="info-value">
                                    {% if card.user_id %}
                                        <span class="text-success">{{ card.user_id }}</span>
                                        <button class="btn btn-link btn-sm p-0 ms-2" onclick="unbindUser()">
                                            <i class="bi bi-x-circle text-danger"></i>
                                        </button>
                                    {% else %}
                                        <span class="text-muted">未绑定</span>
                                        <button class="btn btn-link btn-sm p-0 ms-2" onclick="bindUser()">
                                            <i class="bi bi-plus-circle text-success"></i>
                                        </button>
                                    {% endif %}
                                </div>
                            </div>
                        </div>
                        
                        <div class="col-md-6">
                            <div class="info-item">
                                <label class="info-label">积分</label>
                                <div class="info-value">
                                    <span class="badge bg-warning text-dark fs-6">{{ card.points or 0 }}</span>
                                    <button class="btn btn-link btn-sm p-0 ms-2" onclick="editPoints()">
                                        <i class="bi bi-pencil text-primary"></i>
                                    </button>
                                </div>
                            </div>
                        </div>
                        
                        <div class="col-md-6">
                            <div class="info-item">
                                <label class="info-label">状态</label>
                                <div class="info-value">
                                    {% if card.is_active %}
                                        <span class="badge bg-success">激活</span>
                                    {% else %}
                                        <span class="badge bg-secondary">停用</span>
                                    {% endif %}
                                </div>
                            </div>
                        </div>
                        
                        <div class="col-md-6">
                            <div class="info-item">
                                <label class="info-label">代理商</label>
                                <div class="info-value">
                                    <span class="text-info">{{ card.agent_name }}</span>
                                    <small class="text-muted">({{ card.agent_group }})</small>
                                </div>
                            </div>
                        </div>
                        
                        <div class="col-md-6">
                            <div class="info-item">
                                <label class="info-label">创建时间</label>
                                <div class="info-value">
                                    <span class="text-muted">{{ card.created_at.strftime('%Y-%m-%d %H:%M:%S') if card.created_at else '未知' }}</span>
                                </div>
                            </div>
                        </div>
                        
                        <div class="col-md-6">
                            <div class="info-item">
                                <label class="info-label">过期时间</label>
                                <div class="info-value">
                                    {% if card.expired_at %}
                                        <span class="text-warning">{{ card.expired_at.strftime('%Y-%m-%d %H:%M:%S') }}</span>
                                        <button class="btn btn-link btn-sm p-0 ms-2" onclick="extendExpiry()">
                                            <i class="bi bi-calendar-plus text-success"></i>
                                        </button>
                                    {% else %}
                                        <span class="text-muted">永久有效</span>
                                        <button class="btn btn-link btn-sm p-0 ms-2" onclick="setExpiry()">
                                            <i class="bi bi-calendar-plus text-primary"></i>
                                        </button>
                                    {% endif %}
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- 操作面板 -->
        <div class="col-lg-4">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="bi bi-gear me-2"></i>快捷操作
                    </h5>
                </div>
                <div class="card-body">
                    <div class="d-grid gap-2">
                        {% if not card.user_id %}
                        <button class="btn btn-outline-success" onclick="bindUser()">
                            <i class="bi bi-link-45deg me-2"></i>绑定用户
                        </button>
                        {% else %}
                        <button class="btn btn-outline-warning" onclick="unbindUser()">
                            <i class="bi bi-x-circle me-2"></i>解绑用户
                        </button>
                        {% endif %}
                        
                        <button class="btn btn-outline-primary" onclick="editPoints()">
                            <i class="bi bi-gem me-2"></i>修改积分
                        </button>
                        
                        <button class="btn btn-outline-info" onclick="setExpiry()">
                            <i class="bi bi-calendar-plus me-2"></i>设置有效期
                        </button>
                        
                        {% if card.is_active %}
                        <button class="btn btn-outline-secondary" onclick="toggleStatus(false)">
                            <i class="bi bi-pause-circle me-2"></i>停用卡片
                        </button>
                        {% else %}
                        <button class="btn btn-outline-success" onclick="toggleStatus(true)">
                            <i class="bi bi-play-circle me-2"></i>激活卡片
                        </button>
                        {% endif %}
                        
                        <button class="btn btn-outline-danger" onclick="deleteCard()">
                            <i class="bi bi-trash me-2"></i>删除卡片
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- 绑定历史 -->
    {% if history %}
    <div class="row mt-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="bi bi-clock-history me-2"></i>绑定历史
                    </h5>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-sm">
                            <thead>
                                <tr>
                                    <th>用户ID</th>
                                    <th>绑定时间</th>
                                    <th>解绑时间</th>
                                    <th>状态</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for h in history %}
                                <tr>
                                    <td>{{ h.user_id }}</td>
                                    <td>{{ h.bound_at.strftime('%Y-%m-%d %H:%M:%S') if h.bound_at else '-' }}</td>
                                    <td>{{ h.unbound_at.strftime('%Y-%m-%d %H:%M:%S') if h.unbound_at else '-' }}</td>
                                    <td>
                                        {% if h.unbound_at %}
                                            <span class="badge bg-secondary">已解绑</span>
                                        {% else %}
                                            <span class="badge bg-success">当前绑定</span>
                                        {% endif %}
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
    {% endif %}
</div>

<!-- 编辑卡片模态框 -->
<div class="modal fade" id="editCardModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">编辑VIP卡</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="editCardForm">
                    <div class="mb-3">
                        <label for="editUserId" class="form-label">绑定用户ID</label>
                        <input type="text" class="form-control" id="editUserId" value="{{ card.user_id or '' }}">
                    </div>
                    <div class="mb-3">
                        <label for="editPoints" class="form-label">积分</label>
                        <input type="number" class="form-control" id="editPoints" value="{{ card.points or 0 }}" min="0">
                    </div>
                    <div class="mb-3">
                        <label for="editStatus" class="form-label">状态</label>
                        <select class="form-select" id="editStatus">
                            <option value="1" {% if card.is_active %}selected{% endif %}>激活</option>
                            <option value="0" {% if not card.is_active %}selected{% endif %}>停用</option>
                        </select>
                    </div>
                    <div class="mb-3">
                        <label for="editExpiry" class="form-label">过期时间</label>
                        <input type="datetime-local" class="form-control" id="editExpiry" 
                               value="{% if card.expired_at %}{{ card.expired_at.strftime('%Y-%m-%dT%H:%M') }}{% endif %}">
                        <div class="form-text">留空表示永久有效</div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                <button type="button" class="btn btn-primary" onclick="saveCardEdit()">保存</button>
            </div>
        </div>
    </div>
</div>

<!-- 绑定用户模态框 -->
<div class="modal fade" id="bindUserModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">绑定用户</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="bindUserForm">
                    <div class="mb-3">
                        <label for="bindUserId" class="form-label">用户ID</label>
                        <input type="text" class="form-control" id="bindUserId" placeholder="请输入用户ID" required>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                <button type="button" class="btn btn-primary" onclick="saveUserBind()">绑定</button>
            </div>
        </div>
    </div>
</div>

<!-- 修改积分模态框 -->
<div class="modal fade" id="editPointsModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">修改积分</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="editPointsForm">
                    <div class="mb-3">
                        <label for="pointsOperation" class="form-label">操作类型</label>
                        <select class="form-select" id="pointsOperation">
                            <option value="set">设置为</option>
                            <option value="add">增加</option>
                            <option value="subtract">减少</option>
                        </select>
                    </div>
                    <div class="mb-3">
                        <label for="pointsValue" class="form-label">积分数量</label>
                        <input type="number" class="form-control" id="pointsValue" min="0" required>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                <button type="button" class="btn btn-primary" onclick="savePointsEdit()">保存</button>
            </div>
        </div>
    </div>
</div>

<!-- 设置有效期模态框 -->
<div class="modal fade" id="setExpiryModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">设置有效期</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="setExpiryForm">
                    <div class="mb-3">
                        <label for="expiryOperation" class="form-label">操作类型</label>
                        <select class="form-select" id="expiryOperation" onchange="toggleExpiryInputs()">
                            <option value="set_date">设置具体日期</option>
                            <option value="add_days">增加天数</option>
                            <option value="subtract_days">减少天数</option>
                            <option value="permanent">设为永久</option>
                        </select>
                    </div>

                    <!-- 具体日期设置 -->
                    <div class="mb-3" id="dateInputGroup">
                        <label class="form-label">快捷设置</label>
                        <div class="btn-group w-100 mb-2" role="group">
                            <button type="button" class="btn btn-outline-primary" onclick="setQuickExpiry(30)">30天后</button>
                            <button type="button" class="btn btn-outline-primary" onclick="setQuickExpiry(90)">90天后</button>
                            <button type="button" class="btn btn-outline-primary" onclick="setQuickExpiry(365)">1年后</button>
                        </div>
                        <label for="expiryDate" class="form-label">自定义过期时间</label>
                        <input type="datetime-local" class="form-control" id="expiryDate">
                        <div class="form-text">设置具体的过期日期和时间</div>
                    </div>

                    <!-- 天数设置 -->
                    <div class="mb-3" id="daysInputGroup" style="display: none;">
                        <label class="form-label">快捷选择</label>
                        <div class="btn-group w-100 mb-2" role="group">
                            <button type="button" class="btn btn-outline-primary" onclick="setDaysValue(7)">7天</button>
                            <button type="button" class="btn btn-outline-primary" onclick="setDaysValue(30)">30天</button>
                            <button type="button" class="btn btn-outline-primary" onclick="setDaysValue(90)">90天</button>
                            <button type="button" class="btn btn-outline-primary" onclick="setDaysValue(365)">1年</button>
                        </div>
                        <label for="expiryDays" class="form-label">天数</label>
                        <input type="number" class="form-control" id="expiryDays" min="1" placeholder="请输入天数">
                        <div class="form-text" id="daysHelpText">在当前过期时间基础上增加天数</div>
                    </div>

                    <!-- 永久有效提示 -->
                    <div class="mb-3" id="permanentGroup" style="display: none;">
                        <div class="alert alert-info">
                            <i class="bi bi-info-circle me-2"></i>
                            将此VIP卡设置为永久有效，不会过期。
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                <button type="button" class="btn btn-primary" onclick="saveExpiryEdit()">保存</button>
            </div>
        </div>
    </div>
</div>

<style>
.card-detail-container {
    animation: fadeInUp 0.6s ease-out;
}

.info-item {
    padding: 0.75rem 0;
    border-bottom: 1px solid var(--border-color);
}

.info-item:last-child {
    border-bottom: none;
}

.info-label {
    font-size: 0.875rem;
    font-weight: 600;
    color: var(--text-secondary);
    margin-bottom: 0.25rem;
    display: block;
}

.info-value {
    font-size: 0.95rem;
    color: var(--text-primary);
    display: flex;
    align-items: center;
}

.btn-link {
    text-decoration: none;
}

.btn-link:hover {
    text-decoration: none;
}
</style>

<script>
const cardNumber = '{{ card.card_number }}';

function editCard() {
    new bootstrap.Modal(document.getElementById('editCardModal')).show();
}

function bindUser() {
    new bootstrap.Modal(document.getElementById('bindUserModal')).show();
}

function editPoints() {
    new bootstrap.Modal(document.getElementById('editPointsModal')).show();
}

function setExpiry() {
    new bootstrap.Modal(document.getElementById('setExpiryModal')).show();
}

function extendExpiry() {
    setExpiry();
}

function copyToClipboard(text) {
    navigator.clipboard.writeText(text).then(() => {
        showSuccess('卡号已复制到剪贴板');
    });
}

function saveCardEdit() {
    const userId = document.getElementById('editUserId').value.trim();
    const points = parseInt(document.getElementById('editPoints').value) || 0;
    const isActive = document.getElementById('editStatus').value === '1';
    const expiry = document.getElementById('editExpiry').value;
    
    const data = {
        user_id: userId || null,
        points: points,
        is_active: isActive,
        expired_at: expiry || null
    };
    
    fetch(`/vip/cards/${cardNumber}/edit`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify(data)
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showSuccess(data.message);
            setTimeout(() => location.reload(), 1000);
        } else {
            showError(data.message);
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showError('操作失败');
    });
    
    bootstrap.Modal.getInstance(document.getElementById('editCardModal')).hide();
}

function saveUserBind() {
    const userId = document.getElementById('bindUserId').value.trim();
    
    if (!userId) {
        showError('请输入用户ID');
        return;
    }
    
    fetch(`/vip/cards/${cardNumber}/bind`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({user_id: userId})
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showSuccess(data.message);
            setTimeout(() => location.reload(), 1000);
        } else {
            showError(data.message);
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showError('绑定失败');
    });
    
    bootstrap.Modal.getInstance(document.getElementById('bindUserModal')).hide();
}

function unbindUser() {
    if (confirm('确定要解绑当前用户吗？')) {
        fetch(`/vip/cards/${cardNumber}/unbind`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showSuccess(data.message);
                setTimeout(() => location.reload(), 1000);
            } else {
                showError(data.message);
            }
        })
        .catch(error => {
            console.error('Error:', error);
            showError('解绑失败');
        });
    }
}

function savePointsEdit() {
    const operation = document.getElementById('pointsOperation').value;
    const points = parseInt(document.getElementById('pointsValue').value) || 0;
    
    fetch(`/vip/cards/${cardNumber}/points`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({
            operation: operation,
            points: points
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showSuccess(data.message);
            setTimeout(() => location.reload(), 1000);
        } else {
            showError(data.message);
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showError('积分更新失败');
    });
    
    bootstrap.Modal.getInstance(document.getElementById('editPointsModal')).hide();
}

function toggleExpiryInputs() {
    const operation = document.getElementById('expiryOperation').value;
    const dateGroup = document.getElementById('dateInputGroup');
    const daysGroup = document.getElementById('daysInputGroup');
    const permanentGroup = document.getElementById('permanentGroup');
    const daysHelpText = document.getElementById('daysHelpText');

    // 隐藏所有输入组
    dateGroup.style.display = 'none';
    daysGroup.style.display = 'none';
    permanentGroup.style.display = 'none';

    // 根据操作类型显示相应的输入组
    switch(operation) {
        case 'set_date':
            dateGroup.style.display = 'block';
            break;
        case 'add_days':
            daysGroup.style.display = 'block';
            daysHelpText.textContent = '在当前过期时间基础上增加天数';
            break;
        case 'subtract_days':
            daysGroup.style.display = 'block';
            daysHelpText.textContent = '在当前过期时间基础上减少天数';
            break;
        case 'permanent':
            permanentGroup.style.display = 'block';
            break;
    }
}

function setQuickExpiry(days) {
    const date = new Date();
    date.setDate(date.getDate() + days);
    document.getElementById('expiryDate').value = date.toISOString().slice(0, 16);
}

function setDaysValue(days) {
    document.getElementById('expiryDays').value = days;
}

function saveExpiryEdit() {
    const operation = document.getElementById('expiryOperation').value;
    let requestData = { operation: operation };

    switch(operation) {
        case 'set_date':
            const expiry = document.getElementById('expiryDate').value;
            if (!expiry) {
                showError('请选择过期时间');
                return;
            }
            requestData.expired_at = expiry;
            break;

        case 'add_days':
        case 'subtract_days':
            const days = parseInt(document.getElementById('expiryDays').value);
            if (!days || days <= 0) {
                showError('请输入有效的天数');
                return;
            }
            requestData.days = days;
            break;

        case 'permanent':
            requestData.expired_at = null;
            break;
    }

    fetch(`/vip/cards/${cardNumber}/expiry`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify(requestData)
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showSuccess(data.message);
            setTimeout(() => location.reload(), 1000);
        } else {
            showError(data.message);
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showError('设置失败');
    });

    bootstrap.Modal.getInstance(document.getElementById('setExpiryModal')).hide();
}

function toggleStatus(isActive) {
    const action = isActive ? '激活' : '停用';
    
    if (confirm(`确定要${action}此卡片吗？`)) {
        fetch(`/vip/cards/${cardNumber}/status`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({is_active: isActive})
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showSuccess(data.message);
                setTimeout(() => location.reload(), 1000);
            } else {
                showError(data.message);
            }
        })
        .catch(error => {
            console.error('Error:', error);
            showError('操作失败');
        });
    }
}

function deleteCard() {
    if (confirm('确定要删除此VIP卡吗？此操作不可恢复！')) {
        fetch(`/vip/cards/${cardNumber}/delete`, {
            method: 'DELETE',
            headers: {
                'Content-Type': 'application/json',
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showSuccess(data.message);
                setTimeout(() => window.location.href = '/vip/cards', 1000);
            } else {
                showError(data.message);
            }
        })
        .catch(error => {
            console.error('Error:', error);
            showError('删除失败');
        });
    }
}
</script>
{% endblock %}
