# V3 API (GeWeChat) 集成说明

## 概述

本项目已成功集成第三套微信API（V3），基于GeWeChat客户端库。V3 API提供了更稳定和功能丰富的微信自动化接口。

## 主要特性

- **统一接口**: 与V1、V2 API保持统一的调用接口
- **完整功能**: 支持登录、消息发送、联系人管理、群组管理等完整功能
- **稳定性**: 基于GeWeChat成熟的API库
- **易于使用**: 提供专用的管理界面和工具

## 文件结构

```
app/wechat/
├── gewechat_client/           # GeWeChat客户端库
│   ├── __init__.py
│   ├── client.py             # 主客户端类
│   ├── api/                  # API模块
│   │   ├── login_api.py      # 登录API
│   │   ├── personal_api.py   # 个人信息API
│   │   ├── message_api.py    # 消息API
│   │   ├── contact_api.py    # 联系人API
│   │   ├── group_api.py      # 群组API
│   │   └── ...
│   └── util/                 # 工具模块
│       ├── http_util.py      # HTTP工具
│       └── terminal_printer.py # 终端打印工具
├── client_v3.py              # V3 API客户端封装
├── api_manager.py            # 统一API管理器（已更新支持V3）
└── routes.py                 # 路由（已更新支持V3）
```

## 配置要求

### 数据库配置
确保代理商表中包含以下字段：
- `wechat_base_url`: 微信API基础URL
- `token`: V3 API的访问令牌
- `app_id`: 应用ID（可选，登录时自动获取）

### 依赖安装
```bash
pip install qrcode==7.4.2
```

## 使用方法

### 1. 登录页面选择V3
在登录页面选择"V3登录"选项卡：
1. 访问登录页面
2. 点击"V3登录"选项卡
3. 输入Token（必填）
4. 输入应用ID（可选，登录时会自动获取）
5. 点击"V3登录"按钮

### 2. 程序化设置API版本
在session中设置API版本为'v3'：
```python
session['api_version'] = 'v3'
```

### 3. 配置代理商信息
确保代理商配置包含：
```python
agent_config = {
    'id': 'agent_id',
    'name': '代理商名称',
    'wechat_base_url': 'http://your-server:2531/v2/api',
    'token': 'your_api_token',
    'app_id': 'your_app_id',  # 可选
    'proxy': 'proxy_config',   # 可选
    'wechat_is_active': True
}
```

### 4. 使用API管理器
```python
from app.wechat.api_manager import APIManager

# 创建API管理器
api_manager = APIManager(agent_config, 'v3')

# 获取登录状态
status = api_manager.get_login_status()

# 获取二维码
qr_result = api_manager.get_qr_code()

# 检查二维码状态
qr_status = api_manager.check_qr_status(uuid)

# 获取用户信息
user_info = api_manager.get_user_info()

# 退出登录
logout_result = api_manager.logout()
```

### 5. 直接使用V3客户端
```python
from app.wechat.client_v3 import WeChatClientV3

# 创建客户端
client = WeChatClientV3(
    base_url="http://your-server:2531/v2/api",
    token="your_token",
    app_id="your_app_id"
)

# 获取登录状态
status = client.get_login_status()

# 获取二维码
qr_data = client.get_qr_code()

# 检查二维码状态
qr_status = client.check_qr_status(uuid)
```

## Web界面

### 登录页面
- 访问 `/auth/login` 进入登录页面
- 选择"V3登录"选项卡
- 输入Token和应用ID（可选）进行登录

### 主页面
- 访问 `/wechat/` 查看微信登录状态
- 支持V3 API的状态显示和操作按钮
- 显示"V3管理"按钮，可进入V3专用管理界面

### V3专用管理页面
- 访问 `/wechat/v3/login` 进入V3专用管理界面
- 支持获取Token、设置回调地址等V3特有功能

### API端点
- `GET /wechat/v3/get-token` - 获取V3 API token
- `GET /wechat/v3/set-callback` - 设置回调地址

## API响应格式

### 统一响应格式
```json
{
    "Code": 200,
    "Text": "操作成功",
    "Data": {
        // 具体数据
    }
}
```

### V3原生响应格式
```json
{
    "ret": 200,
    "msg": "操作成功",
    "data": {
        // 具体数据
    }
}
```

## 登录流程

1. **获取二维码**: 调用 `get_qr_code()` 获取登录二维码
2. **扫码登录**: 用户使用微信扫描二维码
3. **状态检查**: 定期调用 `check_qr_status()` 检查登录状态
4. **登录成功**: 状态返回成功后，可以进行其他操作

## 注意事项

1. **Token管理**: V3 API需要有效的token，请确保token配置正确
2. **App ID**: 首次登录时会自动获取app_id，建议保存以便后续使用
3. **回调地址**: 如需接收消息回调，请设置正确的回调地址
4. **网络配置**: 确保服务器能够访问GeWeChat API服务

## 测试

运行测试脚本验证集成：
```bash
python test_v3_api.py
```

## 故障排除

### 常见问题
1. **导入错误**: 确保所有依赖已正确安装
2. **Token无效**: 检查token配置是否正确
3. **网络连接**: 确保能够访问GeWeChat API服务
4. **二维码过期**: 二维码有时效性，过期后需重新获取

### 日志查看
查看应用日志获取详细错误信息：
```python
import logging
logger = logging.getLogger(__name__)
```

## 更新历史

- **2024-01-XX**: 完成V3 API集成
  - 移植gewechat_client库
  - 创建统一接口封装
  - 更新API管理器
  - 添加Web界面支持
  - 完成测试验证
