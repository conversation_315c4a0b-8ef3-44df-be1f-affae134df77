"""
微信机器人统一适配器模块

该模块为V1、V2、V3版本的微信机器人API提供统一的接口，
将不同版本的API转换为标准化的数据格式，便于第三方系统调用。

主要功能：
1. 统一的消息收发接口
2. RabbitMQ消息接收处理
3. 数据格式标准化
4. 多版本API适配

使用示例：
```python
from wechat_adapter import WechatAdapter, AdapterConfig

# 配置适配器
config = AdapterConfig(
    version="v1",
    base_url="http://*************:8080",
    token="your_token",
    rabbitmq_config={
        "host": "localhost",
        "port": 5672,
        "username": "guest",
        "password": "guest"
    }
)

# 创建适配器实例
adapter = WechatAdapter(config)

# 发送消息
adapter.send_text_message("target_wxid", "Hello World!")

# 接收消息
adapter.start_message_listener()
```
"""

from .adapter import WechatAdapter
from .config import AdapterConfig
from .models import *

__version__ = "1.0.0"
__author__ = "WeChat Bot Adapter Team"

__all__ = [
    "WechatAdapter",
    "AdapterConfig",
]
