"""
VIP会员管理工具类
"""
import random
import string
import datetime
import logging
from app.models import get_db_connection

logger = logging.getLogger(__name__)


def check_vip_card_permission(card_number, agent_group, is_admin=False):
    """
    检查VIP卡操作权限

    Args:
        card_number: VIP卡号
        agent_group: 代理组名称
        is_admin: 是否为管理员

    Returns:
        bool: 是否有权限操作此VIP卡
    """
    if is_admin:
        return True

    connection = get_db_connection()
    try:
        with connection.cursor() as cursor:
            sql = "SELECT agent_group FROM vip_cards WHERE card_number = %s"
            cursor.execute(sql, (card_number,))
            result = cursor.fetchone()

            if not result:
                return False

            card_group = result[0]
            return card_group == agent_group
    except Exception as e:
        logger.error(f"检查VIP卡权限失败: {str(e)}")
        return False
    finally:
        connection.close()


def get_agent_group_vip_cards(agent_group, limit=50, offset=0, search=None, status='all'):
    """
    获取代理组的VIP卡列表

    Args:
        agent_group: 代理组名称
        limit: 限制数量
        offset: 偏移量
        search: 搜索关键词
        status: 状态筛选

    Returns:
        tuple: (cards_list, total_count)
    """
    connection = get_db_connection()
    try:
        with connection.cursor() as cursor:
            # 构建查询条件
            where_conditions = ["agent_group = %s"]
            params = [agent_group]

            if search:
                where_conditions.append("(card_number LIKE %s OR user_id LIKE %s)")
                params.extend([f'%{search}%', f'%{search}%'])

            if status == 'bound':
                where_conditions.append("user_id IS NOT NULL AND user_id != ''")
            elif status == 'unbound':
                where_conditions.append("(user_id IS NULL OR user_id = '')")
            elif status == 'expired':
                where_conditions.append("expired_at < NOW()")
            elif status == 'active':
                where_conditions.append("is_active = 1")
            elif status == 'inactive':
                where_conditions.append("is_active = 0")

            where_clause = " AND ".join(where_conditions)

            # 获取总数
            count_sql = f"""
            SELECT COUNT(*)
            FROM vip_cards
            WHERE {where_clause}
            """
            cursor.execute(count_sql, params)
            total = cursor.fetchone()[0]

            # 获取列表数据
            list_sql = f"""
            SELECT id, card_number, agent_group, user_id, points,
                   created_at, expired_at, is_active
            FROM vip_cards
            WHERE {where_clause}
            ORDER BY created_at DESC
            LIMIT %s OFFSET %s
            """
            cursor.execute(list_sql, params + [limit, offset])
            cards = cursor.fetchall()

            return cards, total
    except Exception as e:
        logger.error(f"获取代理组VIP卡列表失败: {str(e)}")
        return [], 0
    finally:
        connection.close()


class VIPCardManager:
    """VIP卡管理器"""
    
    def __init__(self):
        """初始化VIP卡管理器"""
        pass
    
    def generate_card_number(self, agent_group):
        """
        生成会员卡号
        格式：代理前缀-18位数字字母组合
        """
        # 生成18位随机字符串（数字和大写字母）
        chars = string.ascii_uppercase + string.digits
        random_part = ''.join(random.choices(chars, k=18))
        
        # 组合卡号
        card_number = f"{agent_group}-{random_part}"
        
        # 检查是否重复
        connection = get_db_connection()
        try:
            with connection.cursor() as cursor:
                sql = "SELECT COUNT(*) FROM vip_cards WHERE card_number = %s"
                cursor.execute(sql, (card_number,))
                count = cursor.fetchone()[0]
                
                # 如果重复，递归重新生成
                if count > 0:
                    return self.generate_card_number(agent_group)
                
                return card_number
        finally:
            connection.close()
    
    def create_vip_card(self, agent_group, user_id=None):
        """
        创建VIP卡
        
        Args:
            agent_group: 代理分组
            user_id: 绑定的用户ID（可选）
            
        Returns:
            dict: 创建的VIP卡信息
        """
        # 查询代理
        connection = get_db_connection()
        try:
            with connection.cursor() as cursor:
                sql = "SELECT id, `group`, name FROM agents WHERE `group` = %s AND vip_is_active = 1"
                cursor.execute(sql, (agent_group,))
                agent = cursor.fetchone()

                if not agent:
                    raise ValueError(f"代理分组 {agent_group} 不存在或未激活")

                agent_id = agent[0]  # 获取代理商ID

                # 生成卡号
                card_number = self.generate_card_number(agent_group)

                # 创建VIP卡 - 使用agent_group字段，保留agent_id为兼容性
                # 获取第一个匹配的代理ID作为兼容性字段
                cursor.execute("SELECT id FROM agents WHERE `group` = %s LIMIT 1", (agent_group,))
                agent_result = cursor.fetchone()
                agent_id = agent_result[0] if agent_result else 1  # 默认使用ID 1

                insert_sql = """
                INSERT INTO vip_cards (card_number, agent_id, agent_group, user_id, points, created_at, expired_at, is_active)
                VALUES (%s, %s, %s, %s, %s, %s, %s, %s)
                """

                now = datetime.datetime.now()
                cursor.execute(insert_sql, (
                    card_number,
                    agent_id,
                    agent_group,
                    user_id,
                    0,  # 初始积分
                    now,
                    None,  # 首次绑定后设置过期时间
                    True  # 默认激活
                ))
                
                card_id = cursor.lastrowid
                
                # 如果指定了用户ID，创建绑定记录
                if user_id:
                    binding_sql = """
                    INSERT INTO user_binding_history (vip_card_id, user_id, bound_at)
                    VALUES (%s, %s, %s)
                    """
                    cursor.execute(binding_sql, (card_id, user_id, now))
                
                connection.commit()
                
                return {
                    'id': card_id,
                    'card_number': card_number,
                    'agent_group': agent_group,
                    'user_id': user_id,
                    'points': 0,
                    'created_at': now,
                    'expired_at': None,
                    'is_active': True
                }
        except Exception as e:
            connection.rollback()
            raise ValueError(f"创建VIP卡失败: {str(e)}")
        finally:
            connection.close()
    
    def batch_create_vip_cards(self, agent_group, count):
        """
        批量创建VIP卡
        
        Args:
            agent_group: 代理分组
            count: 创建数量
            
        Returns:
            list: 创建的VIP卡列表
        """
        if count <= 0:
            raise ValueError("创建数量必须大于0")
        
        if count > 1000:
            raise ValueError("单次批量创建数量不能超过1000")
        
        cards = []
        for i in range(count):
            try:
                card = self.create_vip_card(agent_group)
                cards.append(card)
            except Exception as e:
                logger.warning(f"批量创建第{i+1}张卡失败: {str(e)}")
                continue
        
        return cards
    
    def get_card_by_number(self, card_number):
        """根据卡号获取VIP卡信息"""
        connection = get_db_connection()
        try:
            with connection.cursor() as cursor:
                sql = """
                SELECT id, card_number, agent_group, user_id, points,
                       created_at, expired_at, is_active
                FROM vip_cards
                WHERE card_number = %s
                """
                cursor.execute(sql, (card_number,))
                result = cursor.fetchone()
                if result:
                    return {
                        'id': result[0],
                        'card_number': result[1],
                        'agent_group': result[2],
                        'user_id': result[3],
                        'points': result[4],
                        'created_at': result[5],
                        'expired_at': result[6],
                        'is_active': result[7]
                    }
                return None
        finally:
            connection.close()
    
    def bind_user(self, card_number, user_id):
        """
        绑定用户到VIP卡
        
        Args:
            card_number: 会员卡号
            user_id: 用户ID
            
        Returns:
            bool: 绑定是否成功
        """
        connection = get_db_connection()
        try:
            with connection.cursor() as cursor:
                # 获取VIP卡信息
                sql = "SELECT id, card_number, agent_id, user_id, points, created_at, expired_at, is_active FROM vip_cards WHERE card_number = %s"
                cursor.execute(sql, (card_number,))
                card = cursor.fetchone()

                if not card:
                    raise ValueError(f"会员卡 {card_number} 不存在")

                card_id, card_number_db, agent_id, current_user_id, points, created_at, expired_at, is_active = card

                if not is_active:
                    raise ValueError(f"会员卡 {card_number} 已停用")

                if expired_at and expired_at < datetime.datetime.now():
                    raise ValueError(f"会员卡 {card_number} 已过期")

                # 如果已经绑定了相同的用户ID，直接返回成功
                if current_user_id == user_id:
                    return True

                # 如果已经绑定了其他用户ID，需要先解绑
                if current_user_id:
                    # 更新历史记录中的解绑时间
                    update_history_sql = """
                    UPDATE user_binding_history
                    SET unbound_at = %s
                    WHERE vip_card_id = %s AND user_id = %s AND unbound_at IS NULL
                    """
                    cursor.execute(update_history_sql, (datetime.datetime.now(), card_id, current_user_id))

                # 更新会员卡的用户ID
                update_card_sql = "UPDATE vip_cards SET user_id = %s WHERE id = %s"
                cursor.execute(update_card_sql, (user_id, card_id))
                
                # 创建新的绑定记录
                insert_history_sql = """
                INSERT INTO user_binding_history (vip_card_id, user_id, bound_at)
                VALUES (%s, %s, %s)
                """
                cursor.execute(insert_history_sql, (card_id, user_id, datetime.datetime.now()))
                
                connection.commit()
                return True
        except Exception as e:
            connection.rollback()
            raise ValueError(f"绑定用户失败: {str(e)}")
        finally:
            connection.close()
    
    def unbind_user(self, card_number):
        """
        解绑VIP卡的用户
        
        Args:
            card_number: 会员卡号
            
        Returns:
            bool: 解绑是否成功
        """
        connection = get_db_connection()
        try:
            with connection.cursor() as cursor:
                # 获取VIP卡信息
                sql = "SELECT id, card_number, agent_id, user_id FROM vip_cards WHERE card_number = %s"
                cursor.execute(sql, (card_number,))
                card = cursor.fetchone()

                if not card:
                    raise ValueError(f"会员卡 {card_number} 不存在")

                card_id, card_number_db, agent_id, current_user_id = card

                if not current_user_id:
                    return True  # 已经是未绑定状态

                # 更新历史记录中的解绑时间
                update_history_sql = """
                UPDATE user_binding_history
                SET unbound_at = %s
                WHERE vip_card_id = %s AND user_id = %s AND unbound_at IS NULL
                """
                cursor.execute(update_history_sql, (datetime.datetime.now(), card_id, current_user_id))

                # 清除会员卡的用户ID
                update_card_sql = "UPDATE vip_cards SET user_id = NULL WHERE id = %s"
                cursor.execute(update_card_sql, (card_id,))
                
                connection.commit()
                return True
        except Exception as e:
            connection.rollback()
            raise ValueError(f"解绑用户失败: {str(e)}")
        finally:
            connection.close()
    
    def update_points(self, card_number, points, operation='set'):
        """
        更新VIP卡积分
        
        Args:
            card_number: 会员卡号
            points: 积分数量
            operation: 操作类型 (set, add, subtract)
            
        Returns:
            bool: 更新是否成功
        """
        connection = get_db_connection()
        try:
            with connection.cursor() as cursor:
                # 获取VIP卡信息
                sql = "SELECT id, card_number, points FROM vip_cards WHERE card_number = %s"
                cursor.execute(sql, (card_number,))
                card = cursor.fetchone()

                if not card:
                    raise ValueError(f"会员卡 {card_number} 不存在")

                card_id, card_number_db, current_points = card
                current_points = current_points or 0

                if operation == 'set':
                    new_points = points
                elif operation == 'add':
                    new_points = current_points + points
                elif operation == 'subtract':
                    new_points = max(0, current_points - points)  # 积分不能为负
                else:
                    raise ValueError(f"不支持的操作类型: {operation}")

                # 更新积分
                update_sql = "UPDATE vip_cards SET points = %s WHERE id = %s"
                cursor.execute(update_sql, (new_points, card_id))
                
                connection.commit()
                return True
        except Exception as e:
            connection.rollback()
            raise ValueError(f"更新积分失败: {str(e)}")
        finally:
            connection.close()
    
    def set_expiration(self, card_number, days=None, operation='add_days', expired_at=None):
        """
        设置VIP卡过期时间

        Args:
            card_number: 会员卡号
            days: 天数（用于add_days和subtract_days操作）
            operation: 操作类型 (add_days, subtract_days, set_date, permanent)
            expired_at: 具体过期时间（用于set_date操作）

        Returns:
            bool: 设置是否成功
        """
        connection = get_db_connection()
        try:
            with connection.cursor() as cursor:
                # 获取VIP卡信息
                sql = "SELECT id, expired_at FROM vip_cards WHERE card_number = %s"
                cursor.execute(sql, (card_number,))
                card = cursor.fetchone()

                if not card:
                    raise ValueError(f"会员卡 {card_number} 不存在")

                card_id, current_expired_at = card

                # 根据操作类型计算新的过期时间
                if operation == 'add_days':
                    if not days or days <= 0:
                        raise ValueError("天数必须大于0")
                    base_date = current_expired_at if current_expired_at else datetime.datetime.now()
                    new_expired_at = base_date + datetime.timedelta(days=days)

                elif operation == 'subtract_days':
                    if not days or days <= 0:
                        raise ValueError("天数必须大于0")
                    if not current_expired_at:
                        raise ValueError("当前为永久有效，无法减少天数")
                    new_expired_at = current_expired_at - datetime.timedelta(days=days)
                    # 确保不会设置为过去的时间
                    if new_expired_at < datetime.datetime.now():
                        new_expired_at = datetime.datetime.now() + datetime.timedelta(hours=1)

                elif operation == 'set_date':
                    if expired_at is None:
                        raise ValueError("必须指定过期时间")
                    if isinstance(expired_at, str):
                        new_expired_at = datetime.datetime.fromisoformat(expired_at.replace('T', ' '))
                    else:
                        new_expired_at = expired_at

                elif operation == 'permanent':
                    new_expired_at = None

                else:
                    raise ValueError(f"不支持的操作类型: {operation}")

                # 更新过期时间
                update_sql = "UPDATE vip_cards SET expired_at = %s WHERE id = %s"
                cursor.execute(update_sql, (new_expired_at, card_id))

                connection.commit()
                return True
        except Exception as e:
            connection.rollback()
            raise ValueError(f"设置过期时间失败: {str(e)}")
        finally:
            connection.close()
