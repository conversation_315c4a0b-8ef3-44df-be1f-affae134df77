import sqlite3
import json
import logging
from typing import List, Dict, Optional, Any, Union
from contextlib import contextmanager
import re
import datetime

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class Database:
    def __init__(self):
        self.db_path = 'ppmt.db'
        # self._create_tables()

    @staticmethod
    def normalize_store_name(name: str) -> str:
        """标准化店铺名称"""
        return name.replace(" ", "").replace("（新店）", "").replace("(新店)", "").replace("店", "").replace("泡泡玛特", "").replace("-", "").lower()

    @staticmethod
    def normalize_product_name(name: str) -> str:
        """标准化商品名称"""
        # return name.replace(" ", "").replace("-", "").lower()
        return re.sub(r'[^\u4e00-\u9fa5a-zA-Z0-9]', '', name).strip().lower()

    @contextmanager
    def get_connection(self):
        conn = sqlite3.connect(self.db_path)
        try:
            yield conn
        finally:
            conn.close()

    def _create_tables(self):
        """创建必要的数据表"""
        try:
            with self.get_connection() as conn:
                cursor = conn.cursor()
                
                # 创建VIP用户表
                logger.info("创建vip_users表...")
                cursor.execute('''
                    CREATE TABLE IF NOT EXISTS vip_users (
                        wxid TEXT PRIMARY KEY,
                        is_active INTEGER DEFAULT 0,
                        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                        expires_at TIMESTAMP,
                        balance DECIMAL(10,2) DEFAULT 0.00,
                        order_id TEXT,
                        pending_purchase TEXT
                    )
                ''')
                
                # 为VIP用户表添加索引，提高查询性能
                cursor.execute('''
                    CREATE INDEX IF NOT EXISTS idx_vip_users_orderid ON vip_users(order_id);
                ''')
                
                cursor.execute('''
                    CREATE INDEX IF NOT EXISTS idx_vip_users_active ON vip_users(is_active);
                ''')
                
                # 创建VIP订单表
                logger.info("创建vip_orders表...")
                cursor.execute('''
                    CREATE TABLE IF NOT EXISTS vip_orders (
                        order_id TEXT PRIMARY KEY,
                        script_id TEXT NOT NULL,
                        script_name TEXT NOT NULL,
                        amount DECIMAL(10,2) NOT NULL,
                        plan_name TEXT NOT NULL,
                        period_unit TEXT NOT NULL,
                        free_trial INTEGER DEFAULT 0,
                        expires_at TIMESTAMP NOT NULL,
                        max_vip_count INTEGER NOT NULL,
                        bonus_count INTEGER DEFAULT 0,
                        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                    )
                ''')
                
                # 创建店铺表（只存储原始名称）
                logger.info("创建stores表...")
                cursor.execute('''
                    CREATE TABLE IF NOT EXISTS stores (
                        store_id TEXT PRIMARY KEY,
                        original_name TEXT NOT NULL
                    )
                ''')
                
                # 创建店铺昵称表
                logger.info("创建store_aliases表...")
                cursor.execute('''
                    CREATE TABLE IF NOT EXISTS store_aliases (
                        store_id TEXT,
                        alias TEXT NOT NULL,
                        PRIMARY KEY (store_id, alias),
                        FOREIGN KEY (store_id) REFERENCES stores(store_id)
                    )
                ''')
                
                # 创建店铺群组关联表
                logger.info("创建store_groups表...")
                cursor.execute('''
                    CREATE TABLE IF NOT EXISTS store_groups (
                        store_id TEXT,
                        group_id TEXT,
                        PRIMARY KEY (store_id, group_id),
                        FOREIGN KEY (store_id) REFERENCES stores(store_id)
                    )
                ''')
                
                # 创建商品表（存储原始名称和昵称）
                logger.info("创建products表...")
                cursor.execute('''
                    CREATE TABLE IF NOT EXISTS products (
                        product_id TEXT PRIMARY KEY,
                        original_name TEXT NOT NULL,
                        nick_name TEXT,
                        product_img TEXT,
                        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                    )
                ''')
                
                # 创建商品昵称表
                logger.info("创建product_aliases表...")
                cursor.execute('''
                    CREATE TABLE IF NOT EXISTS product_aliases (
                        product_id TEXT,
                        alias TEXT NOT NULL,
                        PRIMARY KEY (product_id, alias),
                        FOREIGN KEY (product_id) REFERENCES products(product_id)
                    )
                ''')
                
                # 创建商品CDN信息表
                logger.info("创建product_cdn_info表...")
                cursor.execute('''
                    CREATE TABLE IF NOT EXISTS product_cdn_info (
                        product_id TEXT,
                        wxid TEXT DEFAULT 'main',
                        aes_key TEXT,
                        file_id TEXT,
                        length INTEGER,
                        width INTEGER,
                        height INTEGER,
                        md5 TEXT,
                        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                        PRIMARY KEY (product_id, wxid),
                        FOREIGN KEY (product_id) REFERENCES products(product_id)
                    )
                ''')
                
                # 创建通知商品表
                logger.info("创建notify_products表...")
                cursor.execute('''
                    CREATE TABLE IF NOT EXISTS notify_products (
                        product_id TEXT,
                        wxid TEXT DEFAULT 'main',
                        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                        PRIMARY KEY (product_id, wxid),
                        FOREIGN KEY (product_id) REFERENCES products(product_id)
                    )
                ''')
                
                # 创建VIP卡密表
                logger.info("创建vip_cards表...")
                cursor.execute('''
                    CREATE TABLE IF NOT EXISTS vip_cards (
                        card_id TEXT PRIMARY KEY,
                        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                        used_at TIMESTAMP,
                        used_by TEXT,
                        is_used INTEGER DEFAULT 0,
                        amount DECIMAL(10,2) DEFAULT 0.00,  -- 卡密金额
                        creator TEXT  -- 创建者的wxid
                    )
                ''')
                
                # 创建商品表
                logger.info("创建vip_products表...")
                cursor.execute('''
                    CREATE TABLE IF NOT EXISTS vip_products (
                        product_id TEXT PRIMARY KEY,
                        product_name TEXT NOT NULL,
                        price DECIMAL(10,2) NOT NULL,
                        description TEXT,
                        action_type TEXT NOT NULL,  -- 商品类型：vip_time, balance等
                        action_value TEXT NOT NULL,  -- 商品作用值，如"30"表示30天
                        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                    )
                ''')
                
                # 创建购买记录表
                logger.info("创建vip_purchases表...")
                cursor.execute('''
                    CREATE TABLE IF NOT EXISTS vip_purchases (
                        purchase_id INTEGER PRIMARY KEY AUTOINCREMENT,
                        user_wxid TEXT NOT NULL,
                        product_id TEXT NOT NULL,
                        amount DECIMAL(10,2) NOT NULL,
                        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                        FOREIGN KEY (product_id) REFERENCES vip_products(product_id)
                    )
                ''')
                
                # 为VIP卡密表添加索引
                cursor.execute('''
                    CREATE INDEX IF NOT EXISTS idx_vip_cards_used ON vip_cards(is_used);
                ''')
                
                # 初始化商品数据
                cursor.execute('''
                    INSERT OR IGNORE INTO vip_products 
                    (product_id, product_name, price, description, action_type, action_value)
                    VALUES 
                    ('vip_month', 'VIP', 20.00, '增加VIP时长一个月', 'vip_time', '30')
                ''')
                
                conn.commit()
                logger.info("所有表创建完成")
        except Exception as e:
            logger.error(f"创建表失败: {str(e)}")
            raise

    def add_store(self, store_id: str, store_name: str, group_id: Optional[str] = None) -> bool:
        """添加或更新店铺信息"""
        try:
            with self.get_connection() as conn:
                cursor = conn.cursor()
                
                # 检查店铺是否存在
                cursor.execute('SELECT 1 FROM stores WHERE store_id = ?', (store_id,))
                store_exists = cursor.fetchone() is not None
                
                if not store_exists:
                    # 如果是新店铺，添加基本信息
                    cursor.execute('''
                        INSERT INTO stores (store_id, original_name)
                        VALUES (?, ?)
                    ''', (store_id, store_name))
                
                # 添加标准化后的昵称
                normalized_name = self.normalize_store_name(store_name)
                cursor.execute('''
                    INSERT OR IGNORE INTO store_aliases (store_id, alias)
                    VALUES (?, ?)
                ''', (store_id, normalized_name))
                
                # 如果提供了group_id，添加到关联表
                if group_id:
                    cursor.execute('''
                        INSERT OR IGNORE INTO store_groups (store_id, group_id)
                        VALUES (?, ?)
                    ''', (store_id, group_id))
                
                conn.commit()
                return True
        except Exception as e:
            logger.error(f"添加店铺失败: {str(e)}")
            return False

    def add_product(self, product_id: str, product_name: str, product_img: Optional[str] = None, nick_name: Optional[str] = None) -> bool:
        """添加或更新商品信息"""
        try:
            with self.get_connection() as conn:
                cursor = conn.cursor()
                
                # 检查商品是否存在
                cursor.execute('SELECT 1 FROM products WHERE product_id = ?', (product_id,))
                product_exists = cursor.fetchone() is not None
                
                if not product_exists:
                    # 如果是新商品，添加基本信息
                    cursor.execute('''
                        INSERT INTO products (product_id, original_name, nick_name, product_img)
                        VALUES (?, ?, ?, ?)
                    ''', (product_id, product_name, nick_name, product_img))
                    
                    # 添加标准化后的名称作为别名
                    normalized_name = self.normalize_product_name(product_name)
                    cursor.execute('''
                        INSERT INTO product_aliases (product_id, alias)
                        VALUES (?, ?)
                    ''', (product_id, normalized_name))
                else:
                    # 如果商品已存在，只更新昵称
                    if product_img:
                        cursor.execute('''
                            UPDATE products 
                            SET product_img = ?
                            WHERE product_id = ?
                        ''', (product_img, product_id))
                    if nick_name:
                        # 如果商品已存在，只更新昵称
                        if nick_name:
                            cursor.execute('''
                                UPDATE products 
                                SET nick_name = ?
                                WHERE product_id = ?
                            ''', (nick_name, product_id))
                
                # 添加标准化后的名称作为别名（如果不同）
                normalized_name = self.normalize_product_name(product_name)
                cursor.execute('''
                    INSERT OR IGNORE INTO product_aliases (product_id, alias)
                    VALUES (?, ?)
                ''', (product_id, normalized_name))
                
                conn.commit()
                return True
        except Exception as e:
            logger.error(f"添加商品失败: {str(e)}")
            return False

    def update_product_cdn_info(self, product_id: str, cdn_info: Dict[str, Any], wxid: str = 'main') -> bool:
        """更新商品的CDN信息
        
        Args:
            product_id: 商品ID
            cdn_info: CDN信息字典
            wxid: 机器人wxid，默认为'main'代表主通知列表
            
        Returns:
            bool: 是否成功
        """
        try:
            with self.get_connection() as conn:
                cursor = conn.cursor()
                cursor.execute('''
                    REPLACE INTO product_cdn_info (
                        product_id, wxid, aes_key, file_id, length, width, height, md5, updated_at
                    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, CURRENT_TIMESTAMP)
                ''', (
                    product_id,
                    wxid,
                    cdn_info.get('aesKey', ''),
                    cdn_info.get('fileId', ''),
                    cdn_info.get('length', 0),
                    cdn_info.get('width', 0),
                    cdn_info.get('height', 0),
                    cdn_info.get('md5', ''),
                ))
                conn.commit()
                return True
        except Exception as e:
            logger.error(f"更新商品CDN信息失败: {str(e)}")
            return False

    def get_all_stores(self) -> List[Dict[str, Any]]:
        """获取所有店铺信息"""
        try:
            with self.get_connection() as conn:
                cursor = conn.cursor()
                cursor.execute('''
                    SELECT s.store_id, s.original_name, GROUP_CONCAT(sg.group_id) as group_ids
                    FROM stores s
                    LEFT JOIN store_groups sg ON s.store_id = sg.store_id
                    GROUP BY s.store_id, s.original_name
                ''')
                stores = []
                for row in cursor.fetchall():
                    store = {
                        'store_id': row[0],
                        'store_name': row[1]
                    }
                    if row[2]:  # 如果有群组ID
                        store['group_id'] = row[2].split(',')
                    stores.append(store)
                return stores
        except Exception as e:
            logger.error(f"获取店铺信息失败: {str(e)}")
            return []

    def get_all_products(self, wxid: str = 'main') -> List[Dict[str, Any]]:
        """获取所有商品信息
        
        Args:
            wxid: 机器人wxid，默认为'main'代表主CDN信息列表
            
        Returns:
            List[Dict[str, Any]]: 商品信息列表
        """
        try:
            with self.get_connection() as conn:
                cursor = conn.cursor()
                cursor.execute('''
                    SELECT p.product_id, p.original_name, p.nick_name, p.product_img, 
                           pa.alias
                    FROM products p
                    LEFT JOIN product_aliases pa ON p.product_id = pa.product_id
                ''')
                products = []
                for row in cursor.fetchall():
                    product = {
                        'product_id': row[0],
                        'product_name': row[4] or row[1],  # 如果有别名就用别名，否则用原始名称
                        'nick_name': row[2],
                        'product_img': row[3]
                    }
                    products.append(product)
                return products
        except Exception as e:
            logger.error(f"获取商品信息失败: {str(e)}")
            return []

    def get_all_notify_products(self, wxid: str = 'main') -> List[Dict[str, Any]]:
        """获取所有通知商品信息
        
        Args:
            wxid: 机器人wxid，默认为'main'代表主通知列表
            
        Returns:
            List[Dict[str, Any]]: 通知商品列表
        """
        try:
            with self.get_connection() as conn:
                cursor = conn.cursor()
                # 检查该wxid是否有通知列表
                cursor.execute('''
                    SELECT 1 FROM notify_products WHERE wxid = ? LIMIT 1
                ''', (wxid,))
                
                exists = cursor.fetchone()
                
                # 如果没有该wxid的通知列表，使用main通知列表的商品创建一个
                if not exists and wxid != 'main':
                    cursor.execute('''
                        INSERT INTO notify_products (product_id, wxid)
                        SELECT product_id, ?
                        FROM notify_products
                        WHERE wxid = 'main'
                    ''', (wxid,))
                    conn.commit()
                
                cursor.execute('''
                    SELECT p.product_id, p.original_name, p.nick_name, p.product_img, 
                           pa.alias
                    FROM notify_products np
                    JOIN products p ON np.product_id = p.product_id
                    LEFT JOIN product_aliases pa ON p.product_id = pa.product_id
                    WHERE np.wxid = ?
                ''', (wxid,))
                products = []
                for row in cursor.fetchall():
                    product = {
                        'product_id': row[0],
                        'product_name': row[4] or row[1],  # 如果有别名就用别名，否则用原始名称
                        'nick_name': row[2],
                        'product_img': row[3]
                    }
                    products.append(product)
                return products
        except Exception as e:
            logger.error(f"获取通知商品信息失败: {str(e)}")
            return []

    def add_notify_product(self, product_id: str, wxid: str = 'main') -> bool:
        """添加通知商品
        
        Args:
            product_id: 商品ID
            wxid: 机器人wxid，默认为'main'代表主通知列表
            
        Returns:
            bool: 是否成功
        """
        try:
            with self.get_connection() as conn:
                cursor = conn.cursor()
                # 检查该wxid是否有通知列表
                cursor.execute('''
                    SELECT 1 FROM notify_products WHERE wxid = ? LIMIT 1
                ''', (wxid,))
                
                exists = cursor.fetchone()
                
                # 如果没有该wxid的通知列表，使用main通知列表的商品创建一个
                if not exists and wxid != 'main':
                    cursor.execute('''
                        INSERT INTO notify_products (product_id, wxid)
                        SELECT product_id, ?
                        FROM notify_products
                        WHERE wxid = 'main'
                    ''', (wxid,))
                    conn.commit()
                
                cursor.execute('''
                    INSERT OR IGNORE INTO notify_products (product_id, wxid)
                    VALUES (?, ?)
                ''', (product_id, wxid))
                conn.commit()
                return True
        except Exception as e:
            logger.error(f"添加通知商品失败: {str(e)}")
            return False

    def remove_notify_product(self, product_id: str, wxid: str = 'main') -> bool:
        """从通知商品列表中移除商品
        
        Args:
            product_id: 商品ID
            wxid: 机器人wxid，默认为'main'代表主通知列表
            
        Returns:
            bool: 是否成功
        """
        try:
            with self.get_connection() as conn:
                cursor = conn.cursor()
                # 检查该wxid是否有通知列表
                cursor.execute('''
                    SELECT 1 FROM notify_products WHERE wxid = ? LIMIT 1
                ''', (wxid,))
                
                exists = cursor.fetchone()
                
                # 如果没有该wxid的通知列表，使用main通知列表的商品创建一个
                if not exists and wxid != 'main':
                    cursor.execute('''
                        INSERT INTO notify_products (product_id, wxid)
                        SELECT product_id, ?
                        FROM notify_products
                        WHERE wxid = 'main'
                    ''', (wxid,))
                    conn.commit()
                
                cursor.execute('DELETE FROM notify_products WHERE product_id = ? AND wxid = ?', (product_id, wxid))
                conn.commit()
                return True
        except Exception as e:
            logger.error(f"移除通知商品失败: {str(e)}")
            return False
            
    def get_notify_product_ids(self, wxid: str = 'main') -> List[str]:
        """获取通知商品ID列表
        
        Args:
            wxid: 机器人wxid，默认为'main'代表主通知列表
            
        Returns:
            List[str]: 通知商品ID列表
        """
        try:
            with self.get_connection() as conn:
                cursor = conn.cursor()
                # 检查该wxid是否有通知列表
                cursor.execute('''
                    SELECT 1 FROM notify_products WHERE wxid = ? LIMIT 1
                ''', (wxid,))
                
                exists = cursor.fetchone()
                
                # 如果没有该wxid的通知列表，使用main通知列表的商品创建一个
                if not exists and wxid != 'main':
                    wxid = 'main'
                    # cursor.execute('''
                    #     INSERT INTO notify_products (product_id, wxid)
                    #     SELECT product_id, ?
                    #     FROM notify_products
                    #     WHERE wxid = 'main'
                    # ''', (wxid,))
                    # conn.commit()
                
                cursor.execute('''
                    SELECT product_id
                    FROM notify_products
                    WHERE wxid = ?
                ''', (wxid,))
                
                return [row[0] for row in cursor.fetchall()]
        except Exception as e:
            logger.error(f"获取通知商品ID列表失败: {str(e)}")
            return []

    def add_vip_user(self, wxid: str, expires_at: Optional[str] = None, 
                    balance: Optional[float] = None, order_id: Optional[str] = None,
                    is_active: Optional[bool] = None) -> bool:
        """添加或更新VIP用户信息
        
        Args:
            wxid: 用户wxid
            expires_at: 过期时间，格式：YYYY-MM-DD HH:MM:SS
            balance: 余额
            order_id: 订单ID
            is_active: 是否激活（如果不指定，将根据过期时间自动判断）
            
        Returns:
            bool: 是否成功
        """
        try:
            with self.get_connection() as conn:
                cursor = conn.cursor()
                
                # 1. 检查用户是否存在
                cursor.execute('''
                    SELECT expires_at, balance, order_id, is_active
                    FROM vip_users WHERE wxid = ?
                ''', (wxid,))
                existing_user = cursor.fetchone()
                
                # 如果提供了过期时间，检查是否过期并自动设置 is_active
                if expires_at is not None:
                    try:
                        expires_dt = datetime.datetime.strptime(expires_at, "%Y-%m-%d %H:%M:%S")
                        current_time = datetime.datetime.now()
                        # 只有在未明确指定 is_active 时才自动设置
                        if is_active is None:
                            is_active = expires_dt > current_time
                    except ValueError:
                        logger.error(f"无效的过期时间格式: {expires_at}")
                        # 如果时间格式无效且未指定 is_active，默认设为非活跃
                        if is_active is None:
                            is_active = False
                
                if existing_user:
                    # 2. 用户存在，构建更新语句
                    update_fields = []
                    update_values = []
                    
                    if expires_at is not None:
                        update_fields.append('expires_at = ?')
                        update_values.append(expires_at)
                    
                    if balance is not None:
                        update_fields.append('balance = ?')
                        update_values.append(balance)
                    
                    if order_id is not None:
                        # 特殊处理: 如果 order_id 是 'null' 字符串或 None，则设置为 NULL
                        if order_id == '':
                            update_fields.append('order_id = NULL')
                        else:
                            update_fields.append('order_id = ?')
                            update_values.append(order_id)
                    
                    if is_active is not None:
                        update_fields.append('is_active = ?')
                        update_values.append(1 if is_active else 0)
                    
                    # 始终更新时间戳
                    update_fields.append('updated_at = CURRENT_TIMESTAMP')
                    
                    if update_fields:
                        update_values.append(wxid)
                        cursor.execute(f'''
                            UPDATE vip_users
                            SET {', '.join(update_fields)}
                            WHERE wxid = ?
                        ''', update_values)
                else:
                    # 3. 用户不存在，创建新用户
                    cursor.execute('''
                        INSERT INTO vip_users (
                            wxid, expires_at, balance, order_id, is_active,
                            updated_at
                        ) VALUES (?, ?, ?, ?, ?, CURRENT_TIMESTAMP)
                    ''', (
                        wxid,
                        expires_at,
                        balance if balance is not None else 0.0,
                        order_id,
                        1 if is_active else 0  # 使用计算后的 is_active 值
                    ))
                
                conn.commit()
                return True
        except Exception as e:
            logger.error(f"添加或更新VIP用户失败: {str(e)}")
            return False

    def get_vip_user(self, wxid: str) -> Optional[Dict[str, Any]]:
        """获取VIP用户信息"""
        try:
            with self.get_connection() as conn:
                cursor = conn.cursor()
                cursor.execute('''
                    SELECT wxid, is_active, updated_at, expires_at, balance, order_id, 
                           pending_purchase
                    FROM vip_users WHERE wxid = ?
                ''', (wxid,))
                row = cursor.fetchone()
                if row:
                    return {
                        'wxid': row[0],
                        'is_active': bool(row[1]),
                        'updated_at': row[2],
                        'expires_at': row[3],
                        'balance': float(row[4]) if row[4] is not None else 0.0,
                        'order_id': row[5],
                        'pending_purchase': row[6]
                    }
                return None
        except Exception as e:
            logger.error(f"获取VIP用户信息失败: {str(e)}")
            return None

    def add_vip_order(self, order_id: str, script_id: Optional[str] = None, 
                     script_name: Optional[str] = None, amount: Optional[float] = None,
                     plan_name: Optional[str] = None, period_unit: Optional[str] = None,
                     free_trial: Optional[bool] = None, expires_at: Optional[str] = None,
                     max_vip_count: Optional[int] = None, bonus_count: Optional[int] = None) -> bool:
        """添加或更新VIP订单
        
        Args:
            order_id: 订单ID
            script_id: 脚本ID（可选）
            script_name: 脚本名称（可选）
            amount: 订单金额（可选）
            plan_name: 计划名称（可选）
            period_unit: 周期单位（可选）
            free_trial: 是否为试用期（可选）
            expires_at: 过期时间（可选）
            max_vip_count: 最大VIP数量（可选）
            bonus_count: 赠送数量（可选）
            
        Returns:
            bool: 是否成功
        """
        try:
            with self.get_connection() as conn:
                cursor = conn.cursor()
                
                # 1. 检查订单是否存在
                cursor.execute('''
                    SELECT script_id, script_name, amount, plan_name, period_unit,
                           free_trial, expires_at, max_vip_count, bonus_count
                    FROM vip_orders WHERE order_id = ?
                ''', (order_id,))
                existing_order = cursor.fetchone()
                
                if existing_order:
                    # 2. 如果订单存在，构建更新语句
                    update_fields = []
                    update_values = []
                    
                    if script_id is not None:
                        update_fields.append('script_id = ?')
                        update_values.append(script_id)
                    
                    if script_name is not None:
                        update_fields.append('script_name = ?')
                        update_values.append(script_name)
                    
                    if amount is not None:
                        update_fields.append('amount = ?')
                        update_values.append(amount)
                    
                    if plan_name is not None:
                        update_fields.append('plan_name = ?')
                        update_values.append(plan_name)
                    
                    if period_unit is not None:
                        update_fields.append('period_unit = ?')
                        update_values.append(period_unit)
                    
                    if free_trial is not None:
                        update_fields.append('free_trial = ?')
                        update_values.append(1 if free_trial else 0)
                    
                    if expires_at is not None:
                        update_fields.append('expires_at = ?')
                        update_values.append(expires_at)
                    
                    if max_vip_count is not None:
                        update_fields.append('max_vip_count = ?')
                        update_values.append(max_vip_count)
                    
                    
                    if update_fields:
                        update_values.append(order_id)
                        cursor.execute(f'''
                            UPDATE vip_orders
                            SET {', '.join(update_fields)}
                            WHERE order_id = ?
                        ''', update_values)
                else:
                    # 3. 如果订单不存在，插入新订单
                    cursor.execute('''
                        INSERT INTO vip_orders (
                            order_id, script_id, script_name, amount, plan_name,
                            period_unit, free_trial, expires_at, max_vip_count, bonus_count
                        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                    ''', (
                        order_id,
                        script_id,
                        script_name,
                        amount if amount is not None else 0.0,
                        plan_name,
                        period_unit,
                        1 if free_trial else 0,
                        expires_at,
                        max_vip_count if max_vip_count is not None else 0,
                        bonus_count if bonus_count is not None else 0
                    ))
                
                conn.commit()
                return True
        except Exception as e:
            logger.error(f"添加或更新VIP订单失败: {str(e)}")
            return False

    def bind_vip_order(self, order_id: str, wxid: str) -> Dict[str, Any]:
        """绑定VIP订单到用户
        
        Returns:
            Dict[str, Any]: 包含绑定结果的字典
                - success: 是否成功
                - message: 结果消息
                - current_count: 当前绑定数量
                - max_count: 最大绑定数量（包含赠送数量）
        """
        try:
            with self.get_connection() as conn:
                cursor = conn.cursor()
                
                # 1. 获取订单信息
                cursor.execute('''
                    SELECT max_vip_count, bonus_count, expires_at FROM vip_orders WHERE order_id = ?
                ''', (order_id,))
                order_info = cursor.fetchone()
                if not order_info:
                    return {
                        'success': False,
                        'message': '订单不存在',
                        'current_count': 0,
                        'max_count': 0
                    }
                max_vip_count = order_info[0] + order_info[1]  # 基础数量 + 赠送数量
                order_expires_at = order_info[2]
                
                # 2. 获取当前绑定此订单的用户数量
                cursor.execute('''
                    SELECT COUNT(*) FROM vip_users WHERE order_id = ?
                ''', (order_id,))
                current_count = cursor.fetchone()[0]
                
                # 3. 检查用户是否已绑定其他订单
                cursor.execute('''
                    SELECT order_id FROM vip_users WHERE wxid = ? AND order_id IS NOT NULL
                ''', (wxid,))
                existing_order = cursor.fetchone()
                if existing_order and existing_order[0] != order_id:
                    return {
                        'success': False,
                        'message': '您已绑定其他订单，请先解绑',
                        'current_count': current_count,
                        'max_count': max_vip_count
                    }
                
                # 4. 如果用户已绑定此订单，则更新时间并返回成功
                if existing_order and existing_order[0] == order_id:
                    cursor.execute('''
                        UPDATE vip_users
                        SET updated_at = CURRENT_TIMESTAMP
                        WHERE wxid = ?
                    ''', (wxid,))
                    conn.commit()
                    return {
                        'success': True,
                        'message': '您已经绑定了此订单',
                        'current_count': current_count,
                        'max_count': max_vip_count
                    }
                
                # 5. 如果超过最大数量，不再绑定新用户
                if current_count >= max_vip_count:
                    return {
                        'success': False,
                        'message': f'订单已达到最大绑定数量({max_vip_count})',
                        'current_count': current_count,
                        'max_count': max_vip_count
                    }
                
                # 6. 获取用户现有信息
                cursor.execute('''
                    SELECT expires_at FROM vip_users WHERE wxid = ?
                ''', (wxid,))
                user_row = cursor.fetchone()
                
                current_time = datetime.datetime.now()
                new_expires_at = order_expires_at
                
                if user_row:
                    # 7. 用户已存在，处理过期时间
                    current_expires = user_row[0]
                    
                    if current_expires:
                        try:
                            current_expires_dt = datetime.datetime.strptime(current_expires, "%Y-%m-%d %H:%M:%S")
                            order_expires_dt = datetime.datetime.strptime(order_expires_at, "%Y-%m-%d %H:%M:%S")
                            if order_expires_dt < current_time:
                                order_expires_dt = current_time 
                            # 9. 如果用户过期时间>当前时间，增加用户过期时间
                            if current_expires_dt > current_time:
                                remaining_seconds = (order_expires_dt - current_time).total_seconds()
                                new_expires_dt = current_expires_dt + datetime.timedelta(seconds=remaining_seconds)
                                new_expires_at = new_expires_dt.strftime("%Y-%m-%d %H:%M:%S")
                            # 10. 如果用户没有过期时间或<当前时间，更新过期时间
                            else:
                                new_expires_at = order_expires_at
                        except ValueError:
                            new_expires_at = order_expires_at
                    
                # 更新用户信息
                self.add_vip_user(wxid, new_expires_at, order_id=order_id)
                
                return {
                    'success': True,
                    'message': '订单绑定成功',
                    'current_count': current_count + 1,
                    'max_count': max_vip_count
                }
        except Exception as e:
            logger.error(f"绑定VIP订单失败: {str(e)}")
            return {
                'success': False,
                'message': f'绑定失败: {str(e)}',
                'current_count': 0,
                'max_count': 0
            }

    def unbind_vip_order(self, wxid: str) -> bool:
        """解除用户的VIP订单绑定
        
        Args:
            wxid: 用户wxid
            
        Returns:
            bool: 是否成功
        """
        try:
            with self.get_connection() as conn:
                cursor = conn.cursor()
                
                # 1. 判断用户是否存在订单
                cursor.execute('''
                    SELECT order_id, expires_at
                    FROM vip_users
                    WHERE wxid = ?
                ''', (wxid,))
                user_info = cursor.fetchone()
                
                if not user_info or not user_info[0]:
                    return False
                
                order_id = user_info[0]
                current_expires = user_info[1]
                
                # 2. 获取订单信息
                cursor.execute('''
                    SELECT expires_at FROM vip_orders WHERE order_id = ?
                ''', (order_id,))
                order_info = cursor.fetchone()
                
                if not order_info:
                    return False
                
                order_expires_at = order_info[0]
                current_time = datetime.datetime.now()
                
                # 3. 如果订单过期时间>当前时间，从用户过期时间中减去订单剩余有效时间
                try:
                    order_expires_dt = datetime.datetime.strptime(order_expires_at, "%Y-%m-%d %H:%M:%S")
                    if order_expires_dt > current_time and current_expires:
                        current_expires_dt = datetime.datetime.strptime(current_expires, "%Y-%m-%d %H:%M:%S")
                        remaining_seconds = (order_expires_dt - current_time).total_seconds()
                        new_expires_dt = current_expires_dt - datetime.timedelta(seconds=remaining_seconds)
                        new_expires_at = new_expires_dt.strftime("%Y-%m-%d %H:%M:%S")
                    else:
                        new_expires_at = current_time.strftime("%Y-%m-%d %H:%M:%S")
                except ValueError:
                    new_expires_at = current_time.strftime("%Y-%m-%d %H:%M:%S")
                
                # 4. 清除用户订单信息，保留其他信息
                self.add_vip_user(wxid, new_expires_at, order_id = "")

                return True
        except Exception as e:
            logger.error(f"解除VIP订单绑定失败: {str(e)}")
            return False

    def get_order_users(self, order_id: str) -> List[Dict[str, Any]]:
        """获取订单绑定的所有用户信息"""
        try:
            with self.get_connection() as conn:
                cursor = conn.cursor()
                cursor.execute('''
                    SELECT wxid, updated_at
                    FROM vip_users
                    WHERE order_id = ?
                    ORDER BY updated_at DESC
                ''', (order_id,))
                return [{'wxid': row[0], 'bind_time': row[1]} for row in cursor.fetchall()]
        except Exception as e:
            logger.error(f"获取订单用户列表失败: {str(e)}")
            return []

    def get_vip_orders(self, order_id: str = None) -> List[Dict[str, Any]]:
        """获取所有VIP订单信息或特定订单信息
        
        Args:
            order_id: 可选，指定要查询的订单ID
            
        Returns:
            List[Dict[str, Any]]: 订单信息列表
        """
        try:
            with self.get_connection() as conn:
                cursor = conn.cursor()
                
                if order_id:
                    # 查询特定订单
                    cursor.execute('''
                        SELECT o.order_id, o.script_id, o.script_name, o.amount, o.plan_name,
                               o.period_unit, o.free_trial, o.expires_at, o.max_vip_count,
                               o.bonus_count, o.created_at, COUNT(u.wxid) as bound_count
                        FROM vip_orders o
                        LEFT JOIN vip_users u ON o.order_id = u.order_id
                        WHERE o.order_id = ?
                        GROUP BY o.order_id
                    ''', (order_id,))
                else:
                    # 查询所有订单
                    cursor.execute('''
                        SELECT o.order_id, o.script_id, o.script_name, o.amount, o.plan_name,
                               o.period_unit, o.free_trial, o.expires_at, o.max_vip_count,
                               o.bonus_count, o.created_at, COUNT(u.wxid) as bound_count
                        FROM vip_orders o
                        LEFT JOIN vip_users u ON o.order_id = u.order_id
                        GROUP BY o.order_id
                        ORDER BY o.created_at DESC
                    ''')
                
                orders = []
                for row in cursor.fetchall():
                    orders.append({
                        'order_id': row[0],
                        'script_id': row[1],
                        'script_name': row[2],
                        'amount': float(row[3]),
                        'plan_name': row[4],
                        'period_unit': row[5],
                        'free_trial': bool(row[6]),
                        'expires_at': row[7],
                        'max_vip_count': int(row[8]) if row[8] is not None else 0,
                        'bonus_count': int(row[9]) if row[9] is not None else 0,
                        'created_at': row[10],
                        'bound_count': int(row[11]) if row[11] is not None else 0
                    })
                return orders
        except Exception as e:
            logger.error(f"获取VIP订单信息失败: {str(e)}")
            return []

    def get_user_order(self, wxid: str) -> Optional[Dict[str, Any]]:
        """获取用户当前绑定的订单信息"""
        try:
            with self.get_connection() as conn:
                cursor = conn.cursor()
                cursor.execute('''
                    SELECT o.order_id, o.script_id, o.script_name, o.amount, o.plan_name,
                           o.period_unit, o.free_trial, o.expires_at, o.max_vip_count,
                           o.bonus_count, o.created_at, COUNT(u2.wxid) as bound_count
                    FROM vip_orders o
                    JOIN vip_users u1 ON o.order_id = u1.order_id
                    LEFT JOIN vip_users u2 ON o.order_id = u2.order_id
                    WHERE u1.wxid = ?
                    GROUP BY o.order_id
                ''', (wxid,))
                row = cursor.fetchone()
                if row:
                    return {
                        'order_id': row[0],
                        'script_id': row[1],
                        'script_name': row[2],
                        'amount': float(row[3]),
                        'plan_name': row[4],
                        'period_unit': row[5],
                        'free_trial': bool(row[6]),
                        'expires_at': row[7],
                        'max_vip_count': row[8] if row[8] is not None else 0,
                        'bonus_count': row[9] if row[9] is not None else 0,
                        'created_at': row[10],
                        'bound_count': row[11] if row[11] is not None else 0
                    }
                return None
        except Exception as e:
            logger.error(f"获取用户订单信息失败: {str(e)}")
            return None

    def get_all_vip_users(self) -> List[Dict[str, Any]]:
        """获取所有有效的VIP用户信息"""
        try:
            with self.get_connection() as conn:
                cursor = conn.cursor()
                cursor.execute('''
                    SELECT wxid, is_active, updated_at, expires_at, balance, order_id
                    FROM vip_users
                    WHERE is_active = 1
                ''')
                return [{
                    'wxid': row[0],
                    'is_active': bool(row[1]),
                    'updated_at': row[2],
                    'expires_at': row[3],
                    'balance': float(row[4]) if row[4] is not None else 0.0,
                    'order_id': row[5]
                } for row in cursor.fetchall()]
        except Exception as e:
            logger.error(f"获取所有VIP用户信息失败: {str(e)}")
            return []

    def get_vip_orders_by_type(self, free_trial: bool = False) -> List[Dict[str, Any]]:
        """获取特定类型的VIP订单信息
        
        Args:
            free_trial: 是否获取试用期订单
            
        Returns:
            List[Dict[str, Any]]: 订单信息列表
        """
        try:
            with self.get_connection() as conn:
                cursor = conn.cursor()
                
                # 查询指定类型的订单
                cursor.execute('''
                    SELECT o.order_id, o.script_id, o.script_name, o.amount, o.plan_name,
                           o.period_unit, o.free_trial, o.expires_at, o.max_vip_count,
                           o.bonus_count, o.created_at, COUNT(u.wxid) as bound_count
                    FROM vip_orders o
                    LEFT JOIN vip_users u ON o.order_id = u.order_id
                    WHERE o.free_trial = ?
                    GROUP BY o.order_id
                    ORDER BY o.created_at DESC
                ''', (1 if free_trial else 0,))
                
                orders = []
                for row in cursor.fetchall():
                    orders.append({
                        'order_id': row[0],
                        'script_id': row[1],
                        'script_name': row[2],
                        'amount': float(row[3]),
                        'plan_name': row[4],
                        'period_unit': row[5],
                        'free_trial': bool(row[6]),
                        'expires_at': row[7],
                        'max_vip_count': int(row[8]) if row[8] is not None else 0,
                        'bonus_count': int(row[9]) if row[9] is not None else 0,
                        'created_at': row[10],
                        'bound_count': int(row[11]) if row[11] is not None else 0
                    })
                return orders
        except Exception as e:
            logger.error(f"获取VIP订单信息失败: {str(e)}")
            return []

    def add_vip_card(self, card_id: str, creator: str, amount: float) -> bool:
        """添加VIP卡密
        
        Args:
            card_id: 卡密ID
            creator: 创建者wxid
            amount: 卡密金额
        """
        try:
            with self.get_connection() as conn:
                cursor = conn.cursor()
                cursor.execute('''
                    INSERT INTO vip_cards (card_id, creator, amount)
                    VALUES (?, ?, ?)
                ''', (card_id, creator, amount))
                conn.commit()
                return True
        except Exception as e:
            logger.error(f"添加VIP卡密失败: {str(e)}")
            return False

    def use_vip_card(self, card_id: str, user_wxid: str) -> Dict[str, Any]:
        """使用VIP卡密
        
        Args:
            card_id: 卡密ID
            user_wxid: 使用者wxid
            
        Returns:
            Dict[str, Any]: 使用结果
                - success: 是否成功
                - message: 结果消息
                - amount: 充值金额（如果成功）
        """
        try:
            with self.get_connection() as conn:
                cursor = conn.cursor()
                
                # 检查卡密是否存在且未使用
                cursor.execute('''
                    SELECT amount FROM vip_cards
                    WHERE card_id = ? AND is_used = 0
                ''', (card_id,))
                
                result = cursor.fetchone()
                if not result:
                    return {
                        'success': False,
                        'message': '无效的卡密或卡密已被使用',
                        'amount': 0
                    }
                
                amount = float(result[0])
                
                # 标记卡密为已使用
                cursor.execute('''
                    UPDATE vip_cards
                    SET is_used = 1, used_at = CURRENT_TIMESTAMP, used_by = ?
                    WHERE card_id = ?
                ''', (user_wxid, card_id))
                
                # 更新用户余额
                cursor.execute('''
                    UPDATE vip_users
                    SET balance = COALESCE(balance, 0) + ?,
                        updated_at = CURRENT_TIMESTAMP
                    WHERE wxid = ?
                ''', (amount, user_wxid))
                
                # 如果用户不存在，创建用户记录
                if cursor.rowcount == 0:
                    cursor.execute('''
                        INSERT INTO vip_users (wxid, balance, updated_at)
                        VALUES (?, ?, CURRENT_TIMESTAMP)
                    ''', (user_wxid, amount))
                
                conn.commit()
                return {
                    'success': True,
                    'message': '卡密使用成功',
                    'amount': amount
                }
        except Exception as e:
            logger.error(f"使用VIP卡密失败: {str(e)}")
            return {
                'success': False,
                'message': f'使用卡密失败: {str(e)}',
                'amount': 0
            }

    def get_vip_card(self, card_id: str) -> Optional[Dict[str, Any]]:
        """获取VIP卡密信息"""
        try:
            with self.get_connection() as conn:
                cursor = conn.cursor()
                cursor.execute('''
                    SELECT card_id, created_at, used_at, used_by, is_used, duration, creator
                    FROM vip_cards
                    WHERE card_id = ?
                ''', (card_id,))
                row = cursor.fetchone()
                if row:
                    return {
                        'card_id': row[0],
                        'created_at': row[1],
                        'used_at': row[2],
                        'used_by': row[3],
                        'is_used': bool(row[4]),
                        'duration': row[5],
                        'creator': row[6]
                    }
                return None
        except Exception as e:
            logger.error(f"获取VIP卡密信息失败: {str(e)}")
            return None

    def get_product(self, product_name: str) -> Optional[Dict[str, Any]]:
        """获取商品信息"""
        try:
            with self.get_connection() as conn:
                cursor = conn.cursor()
                cursor.execute('''
                    SELECT product_id, product_name, price, description, action_type, action_value
                    FROM vip_products
                    WHERE product_name = ?
                ''', (product_name,))
                row = cursor.fetchone()
                if row:
                    return {
                        'product_id': row[0],
                        'product_name': row[1],
                        'price': float(row[2]),
                        'description': row[3],
                        'action_type': row[4],
                        'action_value': row[5]
                    }
                return None
        except Exception as e:
            logger.error(f"获取商品信息失败: {str(e)}")
            return None

    def purchase_product(self, user_wxid: str, product_id: str) -> Dict[str, Any]:
        """购买商品
        
        Args:
            user_wxid: 用户wxid
            product_id: 商品ID
            
        Returns:
            Dict[str, Any]: 购买结果
                - success: 是否成功
                - message: 结果消息
                - balance: 剩余余额（如果成功）
        """
        try:
            with self.get_connection() as conn:
                cursor = conn.cursor()
                
                # 获取商品信息
                cursor.execute('''
                    SELECT price, action_type, action_value
                    FROM vip_products
                    WHERE product_id = ?
                ''', (product_id,))
                product = cursor.fetchone()
                if not product:
                    return {
                        'success': False,
                        'message': '商品不存在',
                        'balance': 0
                    }
                
                price = float(product[0])
                action_type = product[1]
                action_value = product[2]
                
                # 检查用户余额和当前VIP状态
                cursor.execute('''
                    SELECT balance, expires_at
                    FROM vip_users
                    WHERE wxid = ?
                ''', (user_wxid,))
                user = cursor.fetchone()
                if not user:
                    return {
                        'success': False,
                        'message': '用户不存在',
                        'balance': 0
                    }
                
                balance = float(user[0] or 0)
                current_expires = user[1]  # 当前过期时间
                
                if balance < price:
                    return {
                        'success': False,
                        'message': f'余额不足，当前余额：{balance}元',
                        'balance': balance
                    }
                
                # 扣除余额
                new_balance = round(balance - price, 2)
                
                # 根据商品类型执行相应操作
                if action_type == 'vip_time':
                    days = int(action_value)
                    current_time = datetime.datetime.now()
                    
                    # 计算新的过期时间
                    if current_expires:
                        try:
                            expires_dt = datetime.datetime.strptime(current_expires, "%Y-%m-%d %H:%M:%S")
                            # 如果已过期，从当前时间开始计算
                            if expires_dt <= current_time:
                                expires_dt = current_time
                        except ValueError:
                            expires_dt = current_time
                    else:
                        expires_dt = current_time
                    
                    # 增加购买的时间
                    new_expires_dt = expires_dt + datetime.timedelta(days=days)
                    new_expires_at = new_expires_dt.strftime("%Y-%m-%d %H:%M:%S")
                    
                    # 更新用户信息
                    cursor.execute('''
                        UPDATE vip_users
                        SET balance = ?,
                            expires_at = ?,
                            is_active = 1,
                            updated_at = CURRENT_TIMESTAMP
                        WHERE wxid = ?
                    ''', (new_balance, new_expires_at, user_wxid))
                else:
                    # 仅更新余额
                    cursor.execute('''
                        UPDATE vip_users
                        SET balance = ?,
                            updated_at = CURRENT_TIMESTAMP
                        WHERE wxid = ?
                    ''', (new_balance, user_wxid))
                
                # 记录购买记录
                cursor.execute('''
                    INSERT INTO vip_purchases (user_wxid, product_id, amount)
                    VALUES (?, ?, ?)
                ''', (user_wxid, product_id, price))
                
                conn.commit()
                return {
                    'success': True,
                    'message': '购买成功',
                    'balance': new_balance
                }
        except Exception as e:
            logger.error(f"购买商品失败: {str(e)}")
            return {
                'success': False,
                'message': f'购买失败: {str(e)}',
                'balance': 0
            }

    def set_pending_purchase(self, wxid, product_id):
        """设置用户的待购买商品"""
        with self.get_connection() as conn:
            cursor = conn.cursor()
            cursor.execute('''
                INSERT INTO vip_users (wxid, pending_purchase)
                VALUES (?, ?)
                ON CONFLICT(wxid) DO UPDATE SET pending_purchase = ?
            ''', (wxid, product_id, product_id))
            conn.commit()

    def clear_pending_purchase(self, wxid):
        """清除用户的待购买商品"""
        with self.get_connection() as conn:
            cursor = conn.cursor()
            cursor.execute('''
                UPDATE vip_users SET pending_purchase = NULL
                WHERE wxid = ?
            ''', (wxid,))
            conn.commit()

    def get_pending_purchase(self, wxid):
        """获取用户的待购买商品"""
        with self.get_connection() as conn:
            cursor = conn.cursor()
            cursor.execute('''
                SELECT pending_purchase FROM vip_users WHERE wxid = ?
            ''', (wxid,))
            result = cursor.fetchone()
            return result[0] if result else None

    def get_product_by_id(self, product_id):
        """通过ID获取商品信息"""
        with self.get_connection() as conn:
            cursor = conn.cursor()
            cursor.execute('''
                SELECT product_id, product_name, price, description, action_type, action_value
                FROM vip_products WHERE product_id = ?
            ''', (product_id,))
            row = cursor.fetchone()
            if row:
                return {
                    'product_id': row[0],
                    'product_name': row[1],
                    'price': float(row[2]),
                    'description': row[3],
                    'action_type': row[4],
                    'action_value': row[5]
                }
            return None

    def get_cdn_expired_products(self, wxid: str = 'main', expire_hours: int = 24) -> List[Dict[str, Any]]:
        """获取CDN信息已过期的商品列表
        
        Args:
            wxid: 机器人wxid，默认为'main'
            expire_hours: 过期时间（小时），默认24小时
            
        Returns:
            List[Dict[str, Any]]: 过期商品列表
        """
        try:
            with self.get_connection() as conn:
                cursor = conn.cursor()
                # 查询更新时间早于指定时间的CDN信息
                cursor.execute('''
                    SELECT p.product_id, p.original_name, p.nick_name, pc.updated_at
                    FROM products p
                    JOIN product_cdn_info pc ON p.product_id = pc.product_id
                    WHERE pc.wxid = ? AND 
                          datetime(pc.updated_at) < datetime('now', '-' || ? || ' hours')
                ''', (wxid, expire_hours))
                
                products = []
                for row in cursor.fetchall():
                    product = {
                        'product_id': row[0],
                        'product_name': row[1],
                        'nick_name': row[2],
                        'updated_at': row[3]
                    }
                    products.append(product)
                return products
        except Exception as e:
            logger.error(f"获取过期CDN信息商品失败: {str(e)}")
            return []

    def get_product_cdn_info(self, product_id: str, wxid: str = 'main') -> Optional[Dict[str, Any]]:
        """获取单个商品的CDN信息
        
        Args:
            product_id: 商品ID
            wxid: 机器人wxid，默认为'main'
            
        Returns:
            Optional[Dict[str, Any]]: CDN信息字典，如果不存在则返回None
        """
        try:
            with self.get_connection() as conn:
                cursor = conn.cursor()
                cursor.execute('''
                    SELECT aes_key, file_id, length, width, height, md5, updated_at
                    FROM product_cdn_info
                    WHERE product_id = ? AND wxid = ?
                ''', (product_id, wxid))
                
                row = cursor.fetchone()
                if row:
                    return {
                        'aesKey': row[0] or '',
                        'fileId': row[1] or '',
                        'length': row[2] or 0,
                        'width': row[3] or 0,
                        'height': row[4] or 0,
                        'md5': row[5] or '',
                        'updated_at': row[6]
                    }
                return None
        except Exception as e:
            logger.error(f"获取商品CDN信息失败: {str(e)}")
            return None
