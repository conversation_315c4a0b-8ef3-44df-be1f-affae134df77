<!DOCTYPE html>
<html lang="zh-CN" data-bs-theme="dark">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}YBA监控管理系统{% endblock %}</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Bootstrap Icons -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.1/font/bootstrap-icons.css">
    <!-- 自定义CSS -->
    <link rel="stylesheet" href="{{ url_for('static', filename='css/style.css') }}">
    
    {% block styles %}{% endblock %}
</head>
<body>
    {% if logged_in %}
    <!-- 已登录用户界面 -->
    <div class="wrapper">
        <!-- 侧边导航栏 -->
        <nav id="sidebar" class="sidebar">
            <div class="sidebar-header">
                <h4><i class="bi bi-activity me-2"></i>YBA监控</h4>
            </div>
            
            <ul class="sidebar-nav">
                <li class="nav-item">
                    <a class="nav-link {% if request.endpoint == 'auth.dashboard' %}active{% endif %}" 
                       href="{{ url_for('auth.dashboard') }}">
                        <i class="bi bi-speedometer2"></i>
                        <span>仪表盘</span>
                    </a>
                </li>
                
                <li class="nav-item">
                    <a class="nav-link {% if request.endpoint and request.endpoint.startswith('wechat.') %}active{% endif %}" 
                       href="{{ url_for('wechat.index') }}">
                        <i class="bi bi-wechat"></i>
                        <span>微信登录管理</span>
                    </a>
                </li>
                
                <li class="nav-item">
                    <a class="nav-link {% if request.endpoint and request.endpoint.startswith('vip.') %}active{% endif %}" 
                       href="{{ url_for('vip.index') }}">
                        <i class="bi bi-credit-card-2-front"></i>
                        <span>VIP会员管理</span>
                    </a>
                </li>
                
                {% if is_admin %}
                <li class="nav-item">
                    <a class="nav-link {% if request.endpoint and request.endpoint.startswith('admin.') %}active{% endif %}" 
                       href="{{ url_for('admin.index') }}">
                        <i class="bi bi-people"></i>
                        <span>用户管理</span>
                    </a>
                </li>
                {% endif %}
            </ul>
            

        </nav>
        
        <!-- 主内容区域 -->
        <div class="main-content">
            <!-- 顶部导航栏 -->
            <header class="topbar">
                <div class="topbar-left">
                    <button class="btn btn-link sidebar-toggle" id="sidebarToggle">
                        <i class="bi bi-list"></i>
                    </button>
                    <h5 class="page-title mb-0">{% block page_title %}{% endblock %}</h5>
                </div>
                
                <div class="topbar-right">
                    <div class="user-info dropdown">
                        <button class="btn btn-link dropdown-toggle" type="button" data-bs-toggle="dropdown">
                            <i class="bi bi-person-circle me-1"></i>
                            <span class="user-name">{{ current_user_name }}</span>
                            {% if is_admin %}
                            <span class="badge bg-danger ms-2">管理员</span>
                            {% else %}
                            <span class="badge bg-primary ms-2">{{ current_user_prefix }}</span>
                            {% endif %}
                        </button>
                        <ul class="dropdown-menu dropdown-menu-end">
                            <li><a class="dropdown-item" href="{{ url_for('auth.logout') }}">
                                <i class="bi bi-box-arrow-right me-2"></i>退出登录
                            </a></li>
                        </ul>
                    </div>
                </div>
            </header>
            
            <!-- 页面内容 -->
            <main class="content">
                <!-- 消息提示 -->
                {% with messages = get_flashed_messages(with_categories=true) %}
                    {% if messages %}
                        <div class="alerts-container">
                            {% for category, message in messages %}
                                <div class="alert alert-{{ 'danger' if category == 'error' else category }} alert-dismissible fade show" role="alert">
                                    <i class="bi bi-{% if category == 'success' %}check-circle{% elif category == 'danger' or category == 'error' %}exclamation-triangle{% elif category == 'warning' %}exclamation-circle{% else %}info-circle{% endif %} me-2"></i>
                                    {{ message }}
                                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                                </div>
                            {% endfor %}
                        </div>
                    {% endif %}
                {% endwith %}
                
                {% block content %}{% endblock %}
            </main>
        </div>
    </div>
    {% else %}
    <!-- 未登录用户界面 -->
    <div class="login-wrapper">
        {% block login_content %}{% endblock %}
    </div>
    {% endif %}
    
    <!-- 加载指示器 -->
    <div id="loadingOverlay" class="loading-overlay">
        <div class="loading-spinner">
            <div class="spinner-border text-primary" role="status">
                <span class="visually-hidden">加载中...</span>
            </div>
            <div class="loading-text">加载中...</div>
        </div>
    </div>
    
    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>
    <!-- jQuery -->
    <script src="https://code.jquery.com/jquery-3.7.1.min.js"></script>
    <!-- 自定义JS -->
    <script src="{{ url_for('static', filename='js/main.js') }}"></script>
    
    {% block scripts %}{% endblock %}
</body>
</html>
