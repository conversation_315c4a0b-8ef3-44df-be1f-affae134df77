# V3 API数据库迁移说明

## 概述

为了支持V3 API（GeWeChat），我们已经成功为agents表添加了`app_id`字段，并完善了相关的数据库结构。

## 迁移内容

### 新增字段

1. **app_id** (VARCHAR(100))
   - 用途：存储V3 API的应用ID
   - 允许空值：是
   - 默认值：NULL
   - 注释：V3 API应用ID

### 已有字段确认

以下字段在之前的迁移中已经添加，本次迁移确认其存在：

2. **api_version** (VARCHAR(10))
   - 用途：标识使用的API版本
   - 默认值：'v1'
   - 可选值：'v1', 'v2', 'v3'

3. **wxid** (VARCHAR(100))
   - 用途：V2 API的微信ID
   - 允许空值：是

4. **device_id** (VARCHAR(100))
   - 用途：V2 API的设备ID
   - 允许空值：是

## 数据库表结构

迁移后的agents表包含以下与API相关的字段：

```sql
-- V1 API字段
token VARCHAR(100)              -- V1/V3 API认证令牌
wechat_base_url VARCHAR(500)    -- 微信API基础URL
proxy VARCHAR(200)              -- 代理设置

-- V2 API字段
wxid VARCHAR(100)               -- 微信ID
device_id VARCHAR(100)          -- 设备ID

-- V3 API字段
app_id VARCHAR(100)             -- 应用ID

-- 通用字段
api_version VARCHAR(10)         -- API版本标识
```

## 使用方法

### 1. 管理后台创建V3代理商

在管理后台创建代理商时：

1. 选择"V3 API (GeWeChat)"版本
2. 填写必填字段：
   - V3 Token：API认证令牌
   - V3微信API地址：GeWeChat API服务器地址
3. 可选字段：
   - 应用ID：如果已有app_id可以填写
   - V3代理设置：代理服务器配置

### 2. 登录页面使用V3

用户可以在登录页面：

1. 选择"V3登录"选项卡
2. 输入Token（必填）
3. 输入应用ID（可选，登录时自动获取）
4. 点击登录

### 3. 程序化操作

```python
# 创建V3代理商
from app.models import Agent

agent = Agent(
    id=1,
    name="V3代理商",
    token="your_v3_token",
    api_version="v3",
    app_id="your_app_id",  # 可选
    wechat_base_url="https://www.geweapi.com/gewe/v2/api"
)

# 使用V3 API管理器
from app.wechat.api_manager import APIManager

config = {
    'id': agent.id,
    'name': agent.name,
    'wechat_base_url': agent.wechat_base_url,
    'token': agent.token,
    'app_id': agent.app_id,
    'wechat_is_active': True
}

api_manager = APIManager(config, 'v3')
```

## 兼容性

### 向后兼容

- 现有的V1和V2代理商不受影响
- 所有新增字段都允许空值
- 现有功能保持不变

### API版本支持

- **V1 API**: 使用token + wechat_base_url
- **V2 API**: 使用wxid + wechat_base_url + device_id（可选）
- **V3 API**: 使用token + wechat_base_url + app_id（可选）

## 验证

迁移完成后，系统已通过以下测试：

1. ✅ 数据库字段存在性检查
2. ✅ V3代理商数据插入/查询测试
3. ✅ Agent模型app_id属性测试
4. ✅ V3 API客户端集成测试
5. ✅ API管理器V3支持测试

## 注意事项

1. **app_id字段**：V3 API的app_id是可选的，如果在创建代理商时不填写，系统会在首次登录时自动获取并保存。

2. **Token复用**：V3 API使用与V1相同的token字段进行认证，但API调用方式不同。

3. **URL格式**：V3 API的URL通常为：`https://www.geweapi.com/gewe/v2/api` 或类似格式。

4. **代理设置**：V3支持代理配置，格式与V1、V2相同。

## 故障排除

如果遇到问题，请检查：

1. 数据库连接是否正常
2. app_id字段是否已正确添加
3. Agent模型是否已更新
4. 管理后台表单是否包含V3配置项

## 文件清单

本次迁移涉及的文件：

- `database_migration_v3_app_id.sql` - SQL迁移脚本
- `app/models.py` - 更新Agent模型支持app_id
- `app/admin/routes.py` - 更新管理后台支持V3
- `app/templates/admin/create_agent.html` - 更新创建代理商表单
- `V3_DATABASE_MIGRATION.md` - 本说明文档

## 总结

V3 API数据库迁移已成功完成，系统现在完全支持三套API（V1、V2、V3）的统一管理。用户可以在管理后台创建V3代理商，在登录页面选择V3登录，并使用完整的V3 API功能。
