"""
VIP会员管理路由
"""
from flask import render_template, request, redirect, url_for, flash, session, jsonify
from app.vip import bp
from app.auth.decorators import login_required, admin_required
from app.vip.utils import VIPCardManager
from app.models import get_db_connection
import logging
import json

logger = logging.getLogger(__name__)


@bp.route('/')
@login_required
def index():
    """VIP会员管理首页"""
    agent_id = session.get('agent_id')
    is_admin = session.get('is_admin', False)
    
    # 获取代理商信息
    from app.models import Agent
    agent = Agent.get_by_id(agent_id)
    
    if not agent:
        flash('用户信息获取失败', 'danger')
        return redirect(url_for('auth.dashboard'))
    
    # 检查VIP功能是否激活
    if not is_admin and not agent.vip_is_active:
        flash('VIP功能未激活，请联系管理员', 'warning')
        return redirect(url_for('auth.dashboard'))
    
    # 获取统计数据
    stats = {}
    
    if is_admin:
        # 管理员看到全局统计
        connection = get_db_connection()
        try:
            with connection.cursor() as cursor:
                # VIP卡总数
                cursor.execute("SELECT COUNT(*) FROM vip_cards")
                stats['total_cards'] = cursor.fetchone()[0]
                
                # 已绑定VIP卡数
                cursor.execute("SELECT COUNT(*) FROM vip_cards WHERE user_id IS NOT NULL AND user_id != ''")
                stats['bound_cards'] = cursor.fetchone()[0]
                
                # 活跃代理数
                cursor.execute("SELECT COUNT(*) FROM agents WHERE vip_is_active = 1")
                stats['active_agents'] = cursor.fetchone()[0]
                
                # 今日新增VIP卡
                cursor.execute("SELECT COUNT(*) FROM vip_cards WHERE DATE(created_at) = CURDATE()")
                stats['today_cards'] = cursor.fetchone()[0]
        finally:
            connection.close()
    else:
        # 代理商看到自己的统计
        connection = get_db_connection()
        try:
            with connection.cursor() as cursor:
                # 我的VIP卡总数
                cursor.execute("SELECT COUNT(*) FROM vip_cards WHERE agent_id = %s", (agent_id,))
                stats['my_cards'] = cursor.fetchone()[0]
                
                # 已绑定的VIP卡数
                cursor.execute("SELECT COUNT(*) FROM vip_cards WHERE agent_id = %s AND user_id IS NOT NULL AND user_id != ''", (agent_id,))
                stats['my_bound_cards'] = cursor.fetchone()[0]
                
                # 今日新增VIP卡
                cursor.execute("SELECT COUNT(*) FROM vip_cards WHERE agent_id = %s AND DATE(created_at) = CURDATE()", (agent_id,))
                stats['my_today_cards'] = cursor.fetchone()[0]
                
                # 总积分
                cursor.execute("SELECT SUM(points) FROM vip_cards WHERE agent_id = %s", (agent_id,))
                result = cursor.fetchone()
                stats['total_points'] = result[0] if result[0] else 0
        finally:
            connection.close()
    
    return render_template('vip/index.html', stats=stats, agent=agent)


@bp.route('/cards')
@login_required
def cards():
    """VIP卡列表"""
    agent_id = session.get('agent_id')
    is_admin = session.get('is_admin', False)

    # 获取代理商信息
    from app.models import Agent
    agent = Agent.get_by_id(agent_id)

    if not agent:
        flash('用户信息获取失败', 'danger')
        return redirect(url_for('auth.dashboard'))

    # 分页参数
    page = request.args.get('page', 1, type=int)
    per_page = 20
    offset = (page - 1) * per_page

    # 搜索参数
    search = request.args.get('search', '').strip()
    status = request.args.get('status', 'all')

    # 构建查询条件
    where_conditions = []
    params = []

    if not is_admin:
        # 基于代理组的权限检查 - 直接使用agent_group字段
        where_conditions.append("agent_group = %s")
        params.append(agent.group)
    
    if search:
        where_conditions.append("(card_number LIKE %s OR user_id LIKE %s)")
        params.extend([f'%{search}%', f'%{search}%'])
    
    if status == 'bound':
        where_conditions.append("user_id IS NOT NULL AND user_id != ''")
    elif status == 'unbound':
        where_conditions.append("(user_id IS NULL OR user_id = '')")
    elif status == 'expired':
        where_conditions.append("expired_at < NOW()")
    elif status == 'active':
        where_conditions.append("is_active = 1")
    elif status == 'inactive':
        where_conditions.append("is_active = 0")
    
    where_clause = " AND ".join(where_conditions) if where_conditions else "1=1"
    
    # 获取VIP卡列表
    connection = get_db_connection()
    try:
        with connection.cursor() as cursor:
            # 获取总数
            count_sql = f"""
            SELECT COUNT(*)
            FROM vip_cards
            WHERE {where_clause}
            """
            cursor.execute(count_sql, params)
            total = cursor.fetchone()[0]

            # 获取列表数据 - 直接查询vip_cards表
            list_sql = f"""
            SELECT id, card_number, agent_group, user_id, points,
                   created_at, expired_at, is_active
            FROM vip_cards
            WHERE {where_clause}
            ORDER BY created_at DESC
            LIMIT %s OFFSET %s
            """
            cursor.execute(list_sql, params + [per_page, offset])
            raw_cards = cursor.fetchall()

            # 转换为字典格式
            cards = []
            for card in raw_cards:
                cards.append({
                    'id': card[0],
                    'card_number': card[1],
                    'agent_group': card[2],
                    'user_id': card[3],
                    'points': card[4],
                    'created_at': card[5],
                    'expired_at': card[6],
                    'is_active': card[7]
                })
    finally:
        connection.close()
    
    # 计算分页信息
    total_pages = (total + per_page - 1) // per_page
    has_prev = page > 1
    has_next = page < total_pages
    
    return render_template('vip/cards.html', 
                          cards=cards,
                          page=page,
                          total_pages=total_pages,
                          has_prev=has_prev,
                          has_next=has_next,
                          total=total,
                          search=search,
                          status=status)


@bp.route('/cards/create', methods=['GET', 'POST'])
@login_required
def create_card():
    """创建VIP卡"""
    agent_id = session.get('agent_id')
    is_admin = session.get('is_admin', False)
    
    if request.method == 'POST':
        try:
            # 获取表单数据
            count = request.form.get('count', 1, type=int)
            user_id = request.form.get('user_id', '').strip() or None
            
            if count <= 0 or count > 100:
                flash('创建数量必须在1-100之间', 'danger')
                return render_template('vip/create_card.html')
            
            # 获取代理商信息
            from app.models import Agent
            agent = Agent.get_by_id(agent_id)
            
            if not agent:
                flash('用户信息获取失败', 'danger')
                return redirect(url_for('vip.index'))
            
            # 创建VIP卡管理器
            vip_manager = VIPCardManager()
            
            if count == 1:
                # 创建单张卡
                card = vip_manager.create_vip_card(agent.group, user_id)
                flash(f'VIP卡创建成功：{card["card_number"]}', 'success')
            else:
                # 批量创建
                cards = vip_manager.batch_create_vip_cards(agent.group, count)
                flash(f'批量创建VIP卡成功，共创建 {len(cards)} 张', 'success')
            
            return redirect(url_for('vip.cards'))
            
        except Exception as e:
            logger.error(f"创建VIP卡失败: {str(e)}")
            flash(f'创建VIP卡失败: {str(e)}', 'danger')
    
    return render_template('vip/create_card.html')


@bp.route('/cards/<card_number>')
@login_required
def card_detail(card_number):
    """VIP卡详情"""
    agent_id = session.get('agent_id')
    is_admin = session.get('is_admin', False)

    # 获取代理商信息
    from app.models import Agent
    agent = Agent.get_by_id(agent_id)

    if not agent:
        flash('用户信息获取失败', 'danger')
        return redirect(url_for('auth.dashboard'))

    # 获取VIP卡信息
    connection = get_db_connection()
    try:
        with connection.cursor() as cursor:
            sql = """
            SELECT id, card_number, agent_group, user_id, points,
                   created_at, expired_at, is_active
            FROM vip_cards
            WHERE card_number = %s
            """

            # 非管理员只能查看同组的卡
            if not is_admin:
                sql += " AND agent_group = %s"
                cursor.execute(sql, (card_number, agent.group))
            else:
                cursor.execute(sql, (card_number,))

            card_result = cursor.fetchone()

            if not card_result:
                flash('VIP卡不存在或无权限查看', 'danger')
                return redirect(url_for('vip.cards'))

            # 转换为字典格式
            card = {
                'id': card_result[0],
                'card_number': card_result[1],
                'agent_group': card_result[2],
                'user_id': card_result[3],
                'points': card_result[4],
                'created_at': card_result[5],
                'expired_at': card_result[6],
                'is_active': card_result[7]
            }

            # 获取绑定历史
            history_sql = """
            SELECT id, vip_card_id, user_id, bound_at, unbound_at FROM user_binding_history
            WHERE vip_card_id = %s
            ORDER BY bound_at DESC
            """
            cursor.execute(history_sql, (card['id'],))
            raw_history = cursor.fetchall()

            # 转换历史记录为字典格式
            history = []
            for h in raw_history:
                history.append({
                    'id': h[0],
                    'vip_card_id': h[1],
                    'user_id': h[2],
                    'bound_at': h[3],
                    'unbound_at': h[4]
                })
    finally:
        connection.close()
    
    return render_template('vip/card_detail.html', card=card, history=history)


@bp.route('/cards/<card_number>/bind', methods=['POST'])
@login_required
def bind_user(card_number):
    """绑定用户"""
    try:
        agent_id = session.get('agent_id')
        is_admin = session.get('is_admin', False)

        # 获取代理商信息
        from app.models import Agent
        agent = Agent.get_by_id(agent_id)

        if not agent:
            return jsonify({'success': False, 'message': '用户信息获取失败'})

        # 检查权限：非管理员只能操作同组的VIP卡
        if not is_admin:
            connection = get_db_connection()
            try:
                with connection.cursor() as cursor:
                    sql = """
                    SELECT a.group
                    FROM vip_cards v
                    LEFT JOIN agents a ON v.agent_id = a.id
                    WHERE v.card_number = %s
                    """
                    cursor.execute(sql, (card_number,))
                    result = cursor.fetchone()

                    if not result or result[0] != agent.group:
                        return jsonify({'success': False, 'message': '无权限操作此VIP卡'})
            finally:
                connection.close()

        data = request.get_json()
        user_id = data.get('user_id', '').strip()

        if not user_id:
            return jsonify({'success': False, 'message': '用户ID不能为空'})

        # 创建VIP卡管理器
        vip_manager = VIPCardManager()

        # 绑定用户
        result = vip_manager.bind_user(card_number, user_id)

        if result:
            return jsonify({'success': True, 'message': '绑定成功'})
        else:
            return jsonify({'success': False, 'message': '绑定失败'})

    except Exception as e:
        logger.error(f"绑定用户失败: {str(e)}")
        return jsonify({'success': False, 'message': str(e)})


@bp.route('/cards/<card_number>/unbind', methods=['POST'])
@login_required
def unbind_user(card_number):
    """解绑用户"""
    try:
        agent_id = session.get('agent_id')
        is_admin = session.get('is_admin', False)

        # 获取代理商信息
        from app.models import Agent
        agent = Agent.get_by_id(agent_id)

        if not agent:
            return jsonify({'success': False, 'message': '用户信息获取失败'})

        # 检查权限：非管理员只能操作同组的VIP卡
        if not is_admin:
            connection = get_db_connection()
            try:
                with connection.cursor() as cursor:
                    sql = """
                    SELECT a.group
                    FROM vip_cards v
                    LEFT JOIN agents a ON v.agent_id = a.id
                    WHERE v.card_number = %s
                    """
                    cursor.execute(sql, (card_number,))
                    result = cursor.fetchone()

                    if not result or result[0] != agent.group:
                        return jsonify({'success': False, 'message': '无权限操作此VIP卡'})
            finally:
                connection.close()

        # 创建VIP卡管理器
        vip_manager = VIPCardManager()

        # 解绑用户
        result = vip_manager.unbind_user(card_number)

        if result:
            return jsonify({'success': True, 'message': '解绑成功'})
        else:
            return jsonify({'success': False, 'message': '解绑失败'})

    except Exception as e:
        logger.error(f"解绑用户失败: {str(e)}")
        return jsonify({'success': False, 'message': str(e)})


@bp.route('/cards/<card_number>/edit', methods=['POST'])
@login_required
def edit_card(card_number):
    """编辑VIP卡"""
    try:
        data = request.get_json()
        user_id = data.get('user_id')
        points = data.get('points', 0)
        is_active = data.get('is_active', True)
        expired_at = data.get('expired_at')

        connection = get_db_connection()
        try:
            with connection.cursor() as cursor:
                # 检查卡片是否存在
                cursor.execute("SELECT id FROM vip_cards WHERE card_number = %s", (card_number,))
                card = cursor.fetchone()

                if not card:
                    return jsonify({'success': False, 'message': 'VIP卡不存在'})

                card_id = card[0]

                # 更新卡片信息
                update_sql = """
                UPDATE vip_cards
                SET user_id = %s, points = %s, is_active = %s, expired_at = %s
                WHERE id = %s
                """

                # 处理过期时间
                expired_at_value = None
                if expired_at:
                    from datetime import datetime
                    expired_at_value = datetime.fromisoformat(expired_at.replace('T', ' '))

                cursor.execute(update_sql, (user_id, points, is_active, expired_at_value, card_id))
                connection.commit()

                return jsonify({'success': True, 'message': 'VIP卡更新成功'})

        except Exception as e:
            connection.rollback()
            raise e
        finally:
            connection.close()

    except Exception as e:
        logger.error(f"编辑VIP卡失败: {str(e)}")
        return jsonify({'success': False, 'message': str(e)})


@bp.route('/cards/<card_number>/unbind', methods=['POST'])
@login_required
def unbind_user_route(card_number):
    """解绑用户"""
    try:
        # 创建VIP卡管理器
        vip_manager = VIPCardManager()

        # 解绑用户
        result = vip_manager.unbind_user(card_number)

        if result:
            return jsonify({'success': True, 'message': '解绑成功'})
        else:
            return jsonify({'success': False, 'message': '解绑失败'})

    except Exception as e:
        logger.error(f"解绑用户失败: {str(e)}")
        return jsonify({'success': False, 'message': str(e)})


@bp.route('/cards/<card_number>/points', methods=['POST'])
@login_required
def update_points(card_number):
    """更新积分"""
    try:
        data = request.get_json()
        points = data.get('points', 0)
        operation = data.get('operation', 'set')  # set, add, subtract

        # 创建VIP卡管理器
        vip_manager = VIPCardManager()

        # 更新积分
        result = vip_manager.update_points(card_number, points, operation)

        if result:
            return jsonify({'success': True, 'message': '积分更新成功'})
        else:
            return jsonify({'success': False, 'message': '积分更新失败'})

    except Exception as e:
        logger.error(f"更新积分失败: {str(e)}")
        return jsonify({'success': False, 'message': str(e)})


@bp.route('/cards/<card_number>/expiry', methods=['POST'])
@login_required
def update_expiry(card_number):
    """更新过期时间"""
    try:
        data = request.get_json()
        operation = data.get('operation', 'set_date')
        expired_at = data.get('expired_at')
        days = data.get('days', 0)

        connection = get_db_connection()
        try:
            with connection.cursor() as cursor:
                # 检查卡片是否存在并获取当前过期时间
                cursor.execute("SELECT id, expired_at FROM vip_cards WHERE card_number = %s", (card_number,))
                card = cursor.fetchone()

                if not card:
                    return jsonify({'success': False, 'message': 'VIP卡不存在'})

                card_id, current_expired_at = card

                # 根据操作类型计算新的过期时间
                from datetime import datetime, timedelta

                if operation == 'set_date':
                    # 设置具体日期
                    if expired_at:
                        expired_at_value = datetime.fromisoformat(expired_at.replace('T', ' '))
                    else:
                        expired_at_value = None
                    message = '过期时间设置成功'

                elif operation == 'add_days':
                    # 增加天数
                    if days <= 0:
                        return jsonify({'success': False, 'message': '天数必须大于0'})

                    base_date = current_expired_at if current_expired_at else datetime.now()
                    expired_at_value = base_date + timedelta(days=days)
                    message = f'有效期延长 {days} 天成功'

                elif operation == 'subtract_days':
                    # 减少天数
                    if days <= 0:
                        return jsonify({'success': False, 'message': '天数必须大于0'})

                    if not current_expired_at:
                        return jsonify({'success': False, 'message': '当前为永久有效，无法减少天数'})

                    expired_at_value = current_expired_at - timedelta(days=days)

                    # 确保不会设置为过去的时间
                    if expired_at_value < datetime.now():
                        expired_at_value = datetime.now() + timedelta(hours=1)  # 至少1小时后过期

                    message = f'有效期缩短 {days} 天成功'

                elif operation == 'permanent':
                    # 设为永久有效
                    expired_at_value = None
                    message = '设置为永久有效成功'

                else:
                    return jsonify({'success': False, 'message': '不支持的操作类型'})

                # 更新过期时间
                cursor.execute("UPDATE vip_cards SET expired_at = %s WHERE id = %s",
                             (expired_at_value, card_id))
                connection.commit()

                return jsonify({'success': True, 'message': message})

        except Exception as e:
            connection.rollback()
            raise e
        finally:
            connection.close()

    except Exception as e:
        logger.error(f"更新过期时间失败: {str(e)}")
        return jsonify({'success': False, 'message': str(e)})


@bp.route('/cards/<card_number>/status', methods=['POST'])
@login_required
def update_status(card_number):
    """更新卡片状态"""
    try:
        data = request.get_json()
        is_active = data.get('is_active', True)

        connection = get_db_connection()
        try:
            with connection.cursor() as cursor:
                # 检查卡片是否存在
                cursor.execute("SELECT id FROM vip_cards WHERE card_number = %s", (card_number,))
                card = cursor.fetchone()

                if not card:
                    return jsonify({'success': False, 'message': 'VIP卡不存在'})

                card_id = card[0]

                # 更新状态
                cursor.execute("UPDATE vip_cards SET is_active = %s WHERE id = %s",
                             (is_active, card_id))
                connection.commit()

                status_text = '激活' if is_active else '停用'
                return jsonify({'success': True, 'message': f'卡片{status_text}成功'})

        except Exception as e:
            connection.rollback()
            raise e
        finally:
            connection.close()

    except Exception as e:
        logger.error(f"更新卡片状态失败: {str(e)}")
        return jsonify({'success': False, 'message': str(e)})


@bp.route('/cards/<card_number>/delete', methods=['DELETE'])
@login_required
def delete_card(card_number):
    """删除VIP卡"""
    try:
        agent_id = session.get('agent_id')
        is_admin = session.get('is_admin', False)

        # 获取代理商信息
        from app.models import Agent
        agent = Agent.get_by_id(agent_id)

        if not agent:
            return jsonify({'success': False, 'message': '用户信息获取失败'})

        connection = get_db_connection()
        try:
            with connection.cursor() as cursor:
                # 检查卡片是否存在并验证权限
                sql = """
                SELECT v.id, a.group
                FROM vip_cards v
                LEFT JOIN agents a ON v.agent_id = a.id
                WHERE v.card_number = %s
                """
                cursor.execute(sql, (card_number,))
                card = cursor.fetchone()

                if not card:
                    return jsonify({'success': False, 'message': 'VIP卡不存在'})

                card_id, card_group = card

                # 检查权限：非管理员只能删除同组的VIP卡
                if not is_admin and card_group != agent.group:
                    return jsonify({'success': False, 'message': '无权限删除此VIP卡'})

                # 删除绑定历史
                cursor.execute("DELETE FROM user_binding_history WHERE vip_card_id = %s", (card_id,))

                # 删除VIP卡
                cursor.execute("DELETE FROM vip_cards WHERE id = %s", (card_id,))

                connection.commit()

                return jsonify({'success': True, 'message': 'VIP卡删除成功'})

        except Exception as e:
            connection.rollback()
            raise e
        finally:
            connection.close()

    except Exception as e:
        logger.error(f"删除VIP卡失败: {str(e)}")
        return jsonify({'success': False, 'message': str(e)})


@bp.route('/users/<user_id>/cards')
@login_required
def user_cards(user_id):
    """查询用户的VIP卡"""
    agent_id = session.get('agent_id')
    is_admin = session.get('is_admin', False)

    # 获取代理商信息
    from app.models import Agent
    agent = Agent.get_by_id(agent_id)

    if not agent:
        flash('用户信息获取失败', 'danger')
        return redirect(url_for('auth.dashboard'))

    # 获取用户的VIP卡
    connection = get_db_connection()
    try:
        with connection.cursor() as cursor:
            sql = """
            SELECT id, card_number, agent_group, user_id, points,
                   created_at, expired_at, is_active
            FROM vip_cards
            WHERE user_id = %s
            """

            # 非管理员只能查看同组代理的卡
            if not is_admin:
                sql += " AND agent_group = %s"
                cursor.execute(sql, (user_id, agent.group))
            else:
                cursor.execute(sql, (user_id,))

            raw_cards = cursor.fetchall()

            # 转换为字典格式
            cards = []
            for card in raw_cards:
                cards.append({
                    'id': card[0],
                    'card_number': card[1],
                    'agent_id': card[2],
                    'user_id': card[3],
                    'points': card[4],
                    'created_at': card[5],
                    'expired_at': card[6],
                    'is_active': card[7],
                    'agent_name': card[8],
                    'agent_group': card[9]
                })
    finally:
        connection.close()
    
    return render_template('vip/user_cards.html', cards=cards, user_id=user_id)


@bp.route('/cards/batch/activate', methods=['POST'])
@login_required
def batch_activate():
    """批量激活VIP卡"""
    try:
        data = request.get_json()
        card_ids = data.get('card_ids', [])

        if not card_ids:
            return jsonify({'success': False, 'message': '请选择要激活的VIP卡'})

        connection = get_db_connection()
        try:
            with connection.cursor() as cursor:
                # 批量更新状态
                placeholders = ','.join(['%s'] * len(card_ids))
                sql = f"UPDATE vip_cards SET is_active = 1 WHERE id IN ({placeholders})"
                cursor.execute(sql, card_ids)

                affected_rows = cursor.rowcount
                connection.commit()

                return jsonify({
                    'success': True,
                    'message': f'批量激活成功，共激活 {affected_rows} 张VIP卡'
                })

        except Exception as e:
            connection.rollback()
            raise e
        finally:
            connection.close()

    except Exception as e:
        logger.error(f"批量激活失败: {str(e)}")
        return jsonify({'success': False, 'message': str(e)})


@bp.route('/cards/batch/deactivate', methods=['POST'])
@login_required
def batch_deactivate():
    """批量停用VIP卡"""
    try:
        data = request.get_json()
        card_ids = data.get('card_ids', [])

        if not card_ids:
            return jsonify({'success': False, 'message': '请选择要停用的VIP卡'})

        connection = get_db_connection()
        try:
            with connection.cursor() as cursor:
                # 批量更新状态
                placeholders = ','.join(['%s'] * len(card_ids))
                sql = f"UPDATE vip_cards SET is_active = 0 WHERE id IN ({placeholders})"
                cursor.execute(sql, card_ids)

                affected_rows = cursor.rowcount
                connection.commit()

                return jsonify({
                    'success': True,
                    'message': f'批量停用成功，共停用 {affected_rows} 张VIP卡'
                })

        except Exception as e:
            connection.rollback()
            raise e
        finally:
            connection.close()

    except Exception as e:
        logger.error(f"批量停用失败: {str(e)}")
        return jsonify({'success': False, 'message': str(e)})


@bp.route('/cards/batch/extend', methods=['POST'])
@login_required
def batch_extend():
    """批量设置过期时间"""
    try:
        data = request.get_json()
        card_ids = data.get('card_ids', [])
        operation = data.get('operation', 'add_days')
        days = data.get('days', 0)
        expired_at = data.get('expired_at')

        if not card_ids:
            return jsonify({'success': False, 'message': '请选择要操作的VIP卡'})

        from datetime import datetime, timedelta

        connection = get_db_connection()
        try:
            with connection.cursor() as cursor:
                if operation == 'set_date':
                    # 设置具体日期
                    if not expired_at:
                        return jsonify({'success': False, 'message': '请指定过期时间'})

                    expired_at_value = datetime.fromisoformat(expired_at.replace('T', ' '))
                    placeholders = ','.join(['%s'] * len(card_ids))
                    sql = f"UPDATE vip_cards SET expired_at = %s WHERE id IN ({placeholders})"
                    cursor.execute(sql, [expired_at_value] + card_ids)
                    message = f'批量设置过期时间成功，共处理 {cursor.rowcount} 张VIP卡'

                elif operation == 'add_days':
                    # 增加天数
                    if days <= 0:
                        return jsonify({'success': False, 'message': '天数必须大于0'})

                    placeholders = ','.join(['%s'] * len(card_ids))
                    sql = f"""
                    UPDATE vip_cards
                    SET expired_at = COALESCE(expired_at, NOW()) + INTERVAL %s DAY
                    WHERE id IN ({placeholders})
                    """
                    cursor.execute(sql, [days] + card_ids)
                    message = f'批量延期成功，共延期 {cursor.rowcount} 张VIP卡 {days} 天'

                elif operation == 'subtract_days':
                    # 减少天数
                    if days <= 0:
                        return jsonify({'success': False, 'message': '天数必须大于0'})

                    placeholders = ','.join(['%s'] * len(card_ids))
                    sql = f"""
                    UPDATE vip_cards
                    SET expired_at = GREATEST(
                        NOW() + INTERVAL 1 HOUR,
                        COALESCE(expired_at, NOW()) - INTERVAL %s DAY
                    )
                    WHERE id IN ({placeholders}) AND expired_at IS NOT NULL
                    """
                    cursor.execute(sql, [days] + card_ids)
                    message = f'批量缩短有效期成功，共处理 {cursor.rowcount} 张VIP卡 {days} 天'

                elif operation == 'permanent':
                    # 设为永久有效
                    placeholders = ','.join(['%s'] * len(card_ids))
                    sql = f"UPDATE vip_cards SET expired_at = NULL WHERE id IN ({placeholders})"
                    cursor.execute(sql, card_ids)
                    message = f'批量设置永久有效成功，共处理 {cursor.rowcount} 张VIP卡'

                else:
                    return jsonify({'success': False, 'message': '不支持的操作类型'})

                affected_rows = cursor.rowcount
                connection.commit()

                return jsonify({
                    'success': True,
                    'message': message
                })

        except Exception as e:
            connection.rollback()
            raise e
        finally:
            connection.close()

    except Exception as e:
        logger.error(f"批量设置过期时间失败: {str(e)}")
        return jsonify({'success': False, 'message': str(e)})


@bp.route('/cards/batch/points', methods=['POST'])
@login_required
def batch_update_points():
    """批量更新积分"""
    try:
        data = request.get_json()
        card_ids = data.get('card_ids', [])
        operation = data.get('operation', 'set')  # set, add, subtract
        points = data.get('points', 0)

        if not card_ids:
            return jsonify({'success': False, 'message': '请选择要更新积分的VIP卡'})

        connection = get_db_connection()
        try:
            with connection.cursor() as cursor:
                if operation == 'set':
                    # 直接设置积分
                    placeholders = ','.join(['%s'] * len(card_ids))
                    sql = f"UPDATE vip_cards SET points = %s WHERE id IN ({placeholders})"
                    cursor.execute(sql, [points] + card_ids)
                elif operation == 'add':
                    # 增加积分
                    placeholders = ','.join(['%s'] * len(card_ids))
                    sql = f"UPDATE vip_cards SET points = COALESCE(points, 0) + %s WHERE id IN ({placeholders})"
                    cursor.execute(sql, [points] + card_ids)
                elif operation == 'subtract':
                    # 减少积分（不能为负）
                    placeholders = ','.join(['%s'] * len(card_ids))
                    sql = f"UPDATE vip_cards SET points = GREATEST(0, COALESCE(points, 0) - %s) WHERE id IN ({placeholders})"
                    cursor.execute(sql, [points] + card_ids)
                else:
                    return jsonify({'success': False, 'message': '不支持的操作类型'})

                affected_rows = cursor.rowcount
                connection.commit()

                operation_text = {'set': '设置', 'add': '增加', 'subtract': '减少'}[operation]
                return jsonify({
                    'success': True,
                    'message': f'批量积分{operation_text}成功，共处理 {affected_rows} 张VIP卡'
                })

        except Exception as e:
            connection.rollback()
            raise e
        finally:
            connection.close()

    except Exception as e:
        logger.error(f"批量更新积分失败: {str(e)}")
        return jsonify({'success': False, 'message': str(e)})


@bp.route('/cards/batch/delete', methods=['POST'])
@login_required
def batch_delete():
    """批量删除VIP卡"""
    try:
        data = request.get_json()
        card_ids = data.get('card_ids', [])

        if not card_ids:
            return jsonify({'success': False, 'message': '请选择要删除的VIP卡'})

        connection = get_db_connection()
        try:
            with connection.cursor() as cursor:
                # 先删除绑定历史
                placeholders = ','.join(['%s'] * len(card_ids))
                history_sql = f"DELETE FROM user_binding_history WHERE vip_card_id IN ({placeholders})"
                cursor.execute(history_sql, card_ids)

                # 再删除VIP卡
                cards_sql = f"DELETE FROM vip_cards WHERE id IN ({placeholders})"
                cursor.execute(cards_sql, card_ids)

                affected_rows = cursor.rowcount
                connection.commit()

                return jsonify({
                    'success': True,
                    'message': f'批量删除成功，共删除 {affected_rows} 张VIP卡'
                })

        except Exception as e:
            connection.rollback()
            raise e
        finally:
            connection.close()

    except Exception as e:
        logger.error(f"批量删除失败: {str(e)}")
        return jsonify({'success': False, 'message': str(e)})
