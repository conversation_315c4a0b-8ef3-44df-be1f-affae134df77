#!/usr/bin/env python3
"""
测试VIP数据库迁移后的功能
验证基于agent_group的查询逻辑是否正常工作
"""
import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app.models import get_db_connection
from app.vip.utils import VIPCardManager, check_vip_card_permission, get_agent_group_vip_cards
import logging

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def test_database_structure():
    """测试数据库结构"""
    logger.info("=== 1. 测试数据库结构 ===")
    
    connection = get_db_connection()
    try:
        with connection.cursor() as cursor:
            # 检查表结构
            cursor.execute("DESCRIBE vip_cards")
            columns = cursor.fetchall()
            
            column_names = [col[0] for col in columns]
            has_agent_id = 'agent_id' in column_names
            has_agent_group = 'agent_group' in column_names
            
            logger.info(f"agent_id字段存在: {has_agent_id}")
            logger.info(f"agent_group字段存在: {has_agent_group}")
            
            if has_agent_group:
                logger.info("✓ agent_group字段已成功添加")
            else:
                logger.error("✗ agent_group字段不存在")
                return False
            
            # 检查数据
            cursor.execute("SELECT COUNT(*) FROM vip_cards WHERE agent_group IS NOT NULL AND agent_group != ''")
            valid_group_count = cursor.fetchone()[0]
            
            cursor.execute("SELECT COUNT(*) FROM vip_cards")
            total_count = cursor.fetchone()[0]
            
            logger.info(f"总VIP卡数量: {total_count}")
            logger.info(f"有效agent_group数量: {valid_group_count}")
            
            if valid_group_count == total_count:
                logger.info("✓ 所有VIP卡都有有效的agent_group")
                return True
            else:
                logger.error(f"✗ 有 {total_count - valid_group_count} 张VIP卡的agent_group为空")
                return False
                
    finally:
        connection.close()

def test_vip_card_creation():
    """测试VIP卡创建"""
    logger.info("\n=== 2. 测试VIP卡创建 ===")
    
    try:
        vip_manager = VIPCardManager()
        
        # 测试创建VIP卡
        test_group = "YBA"
        logger.info(f"为组 {test_group} 创建测试VIP卡...")
        
        card = vip_manager.create_vip_card(test_group)
        
        logger.info(f"✓ VIP卡创建成功:")
        logger.info(f"  卡号: {card['card_number']}")
        logger.info(f"  代理组: {card['agent_group']}")
        logger.info(f"  创建时间: {card['created_at']}")
        
        # 验证数据库中的记录
        connection = get_db_connection()
        try:
            with connection.cursor() as cursor:
                cursor.execute("SELECT agent_group FROM vip_cards WHERE card_number = %s", (card['card_number'],))
                result = cursor.fetchone()
                
                if result and result[0] == test_group:
                    logger.info("✓ 数据库记录验证成功")
                    return card['card_number']
                else:
                    logger.error("✗ 数据库记录验证失败")
                    return None
        finally:
            connection.close()
            
    except Exception as e:
        logger.error(f"✗ VIP卡创建失败: {e}")
        return None

def test_permission_check(test_card_number):
    """测试权限检查"""
    logger.info("\n=== 3. 测试权限检查 ===")
    
    if not test_card_number:
        logger.warning("跳过权限检查测试（没有测试卡号）")
        return
    
    # 测试同组权限
    same_group = "YBA"
    has_permission = check_vip_card_permission(test_card_number, same_group, False)
    logger.info(f"同组权限检查 ({same_group}): {'✓ 通过' if has_permission else '✗ 失败'}")
    
    # 测试不同组权限
    different_group = "TEST"
    has_permission = check_vip_card_permission(test_card_number, different_group, False)
    logger.info(f"不同组权限检查 ({different_group}): {'✓ 正确拒绝' if not has_permission else '✗ 错误通过'}")
    
    # 测试管理员权限
    has_permission = check_vip_card_permission(test_card_number, "ANY_GROUP", True)
    logger.info(f"管理员权限检查: {'✓ 通过' if has_permission else '✗ 失败'}")

def test_group_query():
    """测试组查询"""
    logger.info("\n=== 4. 测试组查询 ===")
    
    # 获取所有组
    connection = get_db_connection()
    try:
        with connection.cursor() as cursor:
            cursor.execute("SELECT DISTINCT agent_group FROM vip_cards ORDER BY agent_group")
            groups = [row[0] for row in cursor.fetchall()]
            
            logger.info(f"发现的代理组: {groups}")
            
            for group in groups:
                cards, total = get_agent_group_vip_cards(group, limit=5)
                logger.info(f"组 {group}: {total} 张VIP卡")
                
                if cards:
                    for card in cards[:2]:  # 显示前2张
                        logger.info(f"  卡号: {card[1]}, 用户: {card[3] or '未绑定'}")
                        
    finally:
        connection.close()

def test_simplified_queries():
    """测试简化后的查询"""
    logger.info("\n=== 5. 测试简化后的查询 ===")
    
    connection = get_db_connection()
    try:
        with connection.cursor() as cursor:
            # 测试基于组的统计查询
            test_group = "YBA"
            
            # 统计该组的VIP卡数量
            cursor.execute("SELECT COUNT(*) FROM vip_cards WHERE agent_group = %s", (test_group,))
            total_cards = cursor.fetchone()[0]
            
            # 统计已绑定的VIP卡
            cursor.execute("""
                SELECT COUNT(*) FROM vip_cards 
                WHERE agent_group = %s AND user_id IS NOT NULL AND user_id != ''
            """, (test_group,))
            bound_cards = cursor.fetchone()[0]
            
            # 统计积分总和
            cursor.execute("""
                SELECT COALESCE(SUM(points), 0) FROM vip_cards 
                WHERE agent_group = %s AND user_id IS NOT NULL AND user_id != ''
            """, (test_group,))
            total_points = cursor.fetchone()[0]
            
            logger.info(f"组 {test_group} 统计:")
            logger.info(f"  总VIP卡: {total_cards} 张")
            logger.info(f"  已绑定: {bound_cards} 张")
            logger.info(f"  总积分: {total_points}")
            
            # 验证查询性能（无JOIN）
            import time
            start_time = time.time()
            
            cursor.execute("""
                SELECT id, card_number, agent_group, user_id, points,
                       created_at, expired_at, is_active
                FROM vip_cards
                WHERE agent_group = %s
                ORDER BY created_at DESC
                LIMIT 10
            """, (test_group,))
            
            results = cursor.fetchall()
            end_time = time.time()
            
            logger.info(f"✓ 查询 {len(results)} 条记录，耗时: {(end_time - start_time)*1000:.2f}ms")
            
    finally:
        connection.close()

def test_vip_card_manager():
    """测试VIP卡管理器"""
    logger.info("\n=== 6. 测试VIP卡管理器 ===")
    
    try:
        vip_manager = VIPCardManager()
        
        # 获取一张现有的VIP卡
        connection = get_db_connection()
        try:
            with connection.cursor() as cursor:
                cursor.execute("SELECT card_number FROM vip_cards LIMIT 1")
                result = cursor.fetchone()
                
                if result:
                    card_number = result[0]
                    logger.info(f"测试卡号: {card_number}")
                    
                    # 测试获取卡片信息
                    card_info = vip_manager.get_card_by_number(card_number)
                    
                    if card_info:
                        logger.info("✓ 获取卡片信息成功:")
                        logger.info(f"  卡号: {card_info['card_number']}")
                        logger.info(f"  代理组: {card_info['agent_group']}")
                        logger.info(f"  用户ID: {card_info['user_id'] or '未绑定'}")
                        logger.info(f"  积分: {card_info['points']}")
                    else:
                        logger.error("✗ 获取卡片信息失败")
                else:
                    logger.warning("没有找到测试VIP卡")
                    
        finally:
            connection.close()
            
    except Exception as e:
        logger.error(f"✗ VIP卡管理器测试失败: {e}")

def main():
    """主函数"""
    logger.info("=" * 60)
    logger.info("VIP数据库迁移功能测试")
    logger.info("=" * 60)
    
    try:
        # 1. 测试数据库结构
        if not test_database_structure():
            logger.error("数据库结构测试失败，停止测试")
            return False
        
        # 2. 测试VIP卡创建
        test_card_number = test_vip_card_creation()
        
        # 3. 测试权限检查
        test_permission_check(test_card_number)
        
        # 4. 测试组查询
        test_group_query()
        
        # 5. 测试简化后的查询
        test_simplified_queries()
        
        # 6. 测试VIP卡管理器
        test_vip_card_manager()
        
        logger.info("\n" + "=" * 60)
        logger.info("✓ 所有测试完成！VIP数据库迁移成功")
        logger.info("✓ 基于agent_group的查询逻辑正常工作")
        logger.info("✓ 查询性能得到优化（移除了JOIN操作）")
        logger.info("=" * 60)
        
        return True
        
    except Exception as e:
        logger.error(f"测试过程中出现异常: {e}")
        return False

if __name__ == '__main__':
    success = main()
    sys.exit(0 if success else 1)
