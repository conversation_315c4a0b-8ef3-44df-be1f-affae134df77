"""
第二套微信API客户端
基于Swagger文档: http://8.133.252.208:8060/swagger.json
支持wxid认证和多种登录方式
"""
import logging
import requests
from typing import Dict, Any, Optional

logger = logging.getLogger(__name__)


class WeChatClientV2:
    """第二套微信API客户端"""
    
    def __init__(self, base_url: str, wxid: Optional[str] = None, timeout: int = 30):
        """
        初始化第二套微信API客户端
        
        Args:
            base_url: API基础URL
            wxid: 微信ID（登录后获得）
            timeout: 请求超时时间
        """
        self.base_url = base_url.rstrip('/')
        self.wxid = wxid
        self.timeout = timeout
        
        # API基础路径
        self.api_base = f"{self.base_url}/api"
        
        logger.info(f"初始化第二套微信API客户端: {self.api_base}")
    
    def _make_request(self, method: str, endpoint: str, data: Optional[Dict] = None, 
                     params: Optional[Dict] = None) -> Dict[str, Any]:
        """
        发送HTTP请求
        
        Args:
            method: HTTP方法
            endpoint: API端点
            data: 请求体数据
            params: 查询参数
        """
        url = f"{self.api_base}{endpoint}"
        headers = {'Content-Type': 'application/json'}
        
        # 如果有wxid，添加到查询参数中
        if self.wxid and params is None:
            params = {}
        if self.wxid and 'wxid' not in (params or {}):
            if params is None:
                params = {}
            params['wxid'] = self.wxid
        
        logger.debug(f"发送请求: {method} {url}")
        logger.debug(f"参数: {params}")
        logger.debug(f"数据: {data}")
        
        try:
            if method.upper() == 'GET':
                response = requests.get(url, headers=headers, params=params, timeout=self.timeout)
            else:
                response = requests.post(url, json=data, headers=headers, params=params, timeout=self.timeout)
            
            logger.debug(f"响应状态码: {response.status_code}")
            
            # 检查HTTP状态码
            if response.status_code == 404:
                logger.warning(f"API端点不存在: {url}")
                return {
                    'Code': -2,
                    'Text': '该链接不存在！',
                    'Data': None
                }
            
            response.raise_for_status()
            
            # 尝试解析JSON响应
            try:
                json_response = response.json()
                logger.debug(f"API响应: {json_response}")
                return json_response
            except ValueError:
                # 如果不是JSON响应，返回文本内容
                logger.warning(f"非JSON响应: {response.text[:200]}")
                return {
                    'Code': 500,
                    'Text': f'非JSON响应: {response.text[:100]}',
                    'Data': None
                }
                
        except requests.exceptions.ConnectionError as e:
            logger.error(f"连接失败: {str(e)}")
            return {
                'Code': -1,
                'Text': f'连接失败: 无法连接到API服务器 {self.base_url}',
                'Data': None
            }
        except requests.exceptions.Timeout as e:
            logger.error(f"请求超时: {str(e)}")
            return {
                'Code': -1,
                'Text': f'请求超时: API服务器响应超时',
                'Data': None
            }
        except requests.exceptions.RequestException as e:
            logger.error(f"请求失败: {str(e)}")
            return {
                'Code': 500,
                'Text': f'请求失败: {str(e)}',
                'Data': None
            }
        except Exception as e:
            logger.error(f"未知错误: {str(e)}")
            return {
                'Code': 500,
                'Text': f'未知错误: {str(e)}',
                'Data': None
            }
    
    # ==================== 登录相关方法 ====================
    
    def get_qr_code(self, device_id: str = None, device_name: str = "YBA_Device",
                   login_type: str = "iPad", proxy: Optional[Dict] = None) -> Dict[str, Any]:
        """
        获取登录二维码

        Args:
            device_id: 设备ID
            device_name: 设备名称
            login_type: 登录类型（iPad, Mac, Windows等）
            proxy: 代理配置
        """
        logger.info(f"获取登录二维码 - 类型: {login_type}")

        data = {
            'DeviceID': device_id or '',
            'DeviceName': device_name,
            'LoginType': login_type
        }

        if proxy:
            data['Proxy'] = proxy

        # 根据登录类型选择不同的端点
        endpoint_map = {
            'iPad': '/Login/LoginGetQR',
            'Mac': '/Login/LoginGetQRMac',
            'Windows': '/Login/LoginGetQRWin',
            'Car': '/Login/LoginGetQRCar',
            'Pad': '/Login/LoginGetQRPad',
            'PadX': '/Login/LoginGetQRPadx',  # 绕过验证码
            'WinUnified': '/Login/LoginGetQRWinUnified',
            'WinUwp': '/Login/LoginGetQRWinUwp'  # 绕过验证码
        }

        endpoint = endpoint_map.get(login_type, '/Login/LoginGetQR')
        return self._make_request('POST', endpoint, data)

    def get_car_qr_code(self, device_id: str = None, device_name: str = "YBA_Car_Device",
                       proxy: Optional[Dict] = None) -> Dict[str, Any]:
        """
        获取车载登录二维码（专用方法）

        Args:
            device_id: 设备ID（可选，没有就留空）
            device_name: 设备名称
            proxy: 代理配置
        """
        logger.info(f"获取车载登录二维码 - 设备ID: {device_id or '未设置'}")

        data = {
            'DeviceID': device_id or '',
            'DeviceName': device_name,
            'LoginType': 'Car'
        }

        if proxy:
            data['Proxy'] = proxy
            logger.debug(f"使用代理配置: {proxy}")

        return self._make_request('POST', '/Login/LoginGetQRCar', data)
    
    def check_qr_status(self, uuid: str) -> Dict[str, Any]:
        """
        检查二维码扫描状态

        Args:
            uuid: 二维码UUID
        """
        logger.info(f"检查二维码状态: {uuid}")
        return self._make_request('POST', '/Login/LoginCheckQR', params={'uuid': uuid})

    def check_car_qr_status(self, uuid: str) -> Dict[str, Any]:
        """
        检查车载二维码扫描状态（专用方法）

        Args:
            uuid: 二维码UUID
        """
        logger.info(f"检查车载二维码状态: {uuid}")
        return self._make_request('POST', '/Login/LoginCheckQR', params={'uuid': uuid})
    
    def a16_login(self, username: str, password: str, a16_data: str, 
                 device_name: str = "YBA_Device", proxy: Optional[Dict] = None) -> Dict[str, Any]:
        """
        A16数据登录
        
        Args:
            username: 用户名
            password: 密码
            a16_data: A16数据
            device_name: 设备名称
            proxy: 代理配置
        """
        logger.info(f"A16登录: {username}")
        
        data = {
            'UserName': username,
            'Password': password,
            'A16': a16_data,
            'DeviceName': device_name
        }
        
        if proxy:
            data['Proxy'] = proxy
        
        return self._make_request('POST', '/Login/A16Data', data)
    
    def data62_login(self, username: str, password: str, data62: str,
                    device_name: str = "YBA_Device", proxy: Optional[Dict] = None) -> Dict[str, Any]:
        """
        62数据登录
        
        Args:
            username: 用户名
            password: 密码
            data62: 62数据
            device_name: 设备名称
            proxy: 代理配置
        """
        logger.info(f"62数据登录: {username}")
        
        data = {
            'UserName': username,
            'Password': password,
            'Data62': data62,
            'DeviceName': device_name
        }
        
        if proxy:
            data['Proxy'] = proxy
        
        return self._make_request('POST', '/Login/Data62Login', data)
    
    def submit_verification_code(self, code: str, ticket: str, uuid: str, data62: str = None) -> Dict[str, Any]:
        """
        提交登录验证码
        
        Args:
            code: 验证码
            ticket: 票据
            uuid: UUID
            data62: 62数据（可选）
        """
        logger.info("提交登录验证码")
        
        data = {
            'Code': code,
            'Ticket': ticket,
            'Uuid': uuid
        }
        
        if data62:
            data['Data62'] = data62
        
        return self._make_request('POST', '/Login/YPayVerificationcode', data)
    
    def logout(self) -> Dict[str, Any]:
        """退出登录"""
        if not self.wxid:
            return {
                'Code': -1,
                'Text': '未设置wxid，无法退出登录',
                'Data': None
            }
        
        logger.info(f"退出登录: {self.wxid}")
        return self._make_request('POST', '/Login/LogOut', params={'wxid': self.wxid})
    
    # ==================== 状态和信息获取 ====================
    
    def get_user_profile(self) -> Dict[str, Any]:
        """获取用户个人信息"""
        if not self.wxid:
            return {
                'Code': -1,
                'Text': '未设置wxid，无法获取用户信息',
                'Data': None
            }
        
        logger.info(f"获取用户信息: {self.wxid}")
        return self._make_request('POST', '/User/GetContractProfile', params={'wxid': self.wxid})
    
    def heart_beat(self) -> Dict[str, Any]:
        """发送心跳包"""
        if not self.wxid:
            return {
                'Code': -1,
                'Text': '未设置wxid，无法发送心跳',
                'Data': None
            }
        
        logger.debug(f"发送心跳: {self.wxid}")
        return self._make_request('POST', '/Login/HeartBeat', params={'wxid': self.wxid})
    
    def get_login_status(self) -> Dict[str, Any]:
        """
        获取登录状态（通过心跳检测）
        第二套API没有直接的登录状态接口，使用心跳来检测
        """
        if not self.wxid:
            return {
                'Code': 0,
                'Text': '未设置wxid',
                'Data': {
                    'loginState': 0,
                    'status': 'offline',
                    'message': '未设置wxid'
                }
            }
        
        logger.info(f"检查登录状态: {self.wxid}")
        
        # 使用心跳检测登录状态
        result = self.heart_beat()
        
        # 根据心跳结果判断登录状态
        if result.get('Code') == 200 or result.get('Code') == 0:
            return {
                'Code': 200,
                'Text': '在线',
                'Data': {
                    'loginState': 1,
                    'status': 'online',
                    'wxid': self.wxid,
                    'message': '心跳正常'
                }
            }
        else:
            return {
                'Code': result.get('Code', 500),
                'Text': result.get('Text', '离线'),
                'Data': {
                    'loginState': 0,
                    'status': 'offline',
                    'wxid': self.wxid,
                    'message': result.get('Text', '心跳失败')
                }
            }
    
    def initialize(self, max_sync_key: str = None, current_sync_key: str = None) -> Dict[str, Any]:
        """
        初始化

        Args:
            max_sync_key: 最大同步键
            current_sync_key: 当前同步键
        """
        if not self.wxid:
            return {
                'Code': -1,
                'Text': '未设置wxid，无法初始化',
                'Data': None
            }

        logger.info(f"初始化: {self.wxid}")

        params = {'wxid': self.wxid}
        if max_sync_key:
            params['MaxSynckey'] = max_sync_key
        if current_sync_key:
            params['CurrentSynckey'] = current_sync_key

        return self._make_request('POST', '/Login/Newinit', params=params)

    def enable_auto_heartbeat(self) -> Dict[str, Any]:
        """
        开启自动心跳、自动二次登录和自动推送消息（长链接）

        Returns:
            API响应结果
        """
        if not self.wxid:
            return {
                'Code': -1,
                'Text': '未设置wxid，无法开启自动心跳',
                'Data': None
            }

        logger.info(f"开启自动心跳: {self.wxid}")
        return self._make_request('POST', '/Login/AutoHeartBeat', params={'wxid': self.wxid})

    def disable_auto_heartbeat(self) -> Dict[str, Any]:
        """
        关闭自动心跳、自动二次登录

        Returns:
            API响应结果
        """
        if not self.wxid:
            return {
                'Code': -1,
                'Text': '未设置wxid，无法关闭自动心跳',
                'Data': None
            }

        logger.info(f"关闭自动心跳: {self.wxid}")
        return self._make_request('POST', '/Login/CloseAutoHeartBeat', params={'wxid': self.wxid})

    def get_auto_heartbeat_log(self) -> Dict[str, Any]:
        """
        获取自动心跳日志

        Returns:
            API响应结果
        """
        if not self.wxid:
            return {
                'Code': -1,
                'Text': '未设置wxid，无法获取心跳日志',
                'Data': None
            }

        logger.info(f"获取自动心跳日志: {self.wxid}")
        return self._make_request('POST', '/Login/AutoHeartBeatLog', params={'wxid': self.wxid})
    
    # ==================== 工具方法 ====================
    
    def set_wxid(self, wxid: str):
        """设置微信ID"""
        self.wxid = wxid
        logger.info(f"设置wxid: {wxid}")
    
    def is_logged_in(self) -> bool:
        """检查是否已登录"""
        if not self.wxid:
            return False

        status = self.get_login_status()
        return status.get('Data', {}).get('loginState', 0) == 1

    # ==================== V2扫码登录专用流程 ====================

    def start_car_qr_login(self, device_id: str = None, device_name: str = "YBA_Car_Device",
                          proxy: Optional[Dict] = None) -> Dict[str, Any]:
        """
        启动车载扫码登录流程

        Args:
            device_id: 设备ID（可选，没有就留空）
            device_name: 设备名称
            proxy: 代理配置

        Returns:
            包含二维码信息的响应
        """
        logger.info(f"启动车载扫码登录流程 - 设备ID: {device_id or '未设置'}")

        # 获取车载二维码
        qr_result = self.get_car_qr_code(device_id=device_id, device_name=device_name, proxy=proxy)

        # 适配正确的返回数据格式：Code=1表示二维码获取成功
        if qr_result.get('Code') == 1 and qr_result.get('Data'):
            qr_data = qr_result.get('Data', {})
            uuid = qr_data.get('Uuid') or qr_data.get('uuid') or qr_data.get('UUID')

            if uuid:
                logger.info(f"车载二维码获取成功，UUID: {uuid}")
                # 将UUID存储在响应中，便于后续状态检查
                qr_result['uuid'] = uuid
            else:
                logger.warning("车载二维码响应中缺少UUID")
        else:
            logger.error(f"获取车载二维码失败: Code={qr_result.get('Code')}, Message={qr_result.get('Message', '未知错误')}")

        return qr_result

    def complete_car_qr_login(self, uuid: str) -> Dict[str, Any]:
        """
        完成车载扫码登录流程（登录成功后调用）

        Args:
            uuid: 二维码UUID

        Returns:
            自动心跳开启结果
        """
        if not self.wxid:
            logger.error("完成登录流程失败：未设置wxid")
            return {
                'Code': -1,
                'Text': '未设置wxid，无法完成登录流程',
                'Data': None
            }

        logger.info(f"完成车载扫码登录流程 - WXID: {self.wxid}")

        # 开启自动心跳、自动二次登录
        heartbeat_result = self.enable_auto_heartbeat()

        if heartbeat_result.get('Code') == 200:
            logger.info(f"自动心跳开启成功 - WXID: {self.wxid}")
        else:
            logger.warning(f"自动心跳开启失败 - WXID: {self.wxid}, 错误: {heartbeat_result.get('Text', '未知错误')}")

        return heartbeat_result
