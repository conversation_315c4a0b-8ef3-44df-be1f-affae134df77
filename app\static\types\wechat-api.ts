/**
 * 微信API TypeScript类型定义
 * 基于Swagger文档: http://*************:8080/docs/swagger.json
 * 生成时间: 2025-01-28
 */

// 基础API响应接口
export interface ApiResponse<T = any> {
  Code: number;
  Text: string;
  Data: T | null;
}

// 登录状态相关类型
export interface LoginStatusData {
  loginState?: number;
  status?: string;
  message?: string;
  wxid?: string;
  nickname?: string;
  headImgUrl?: string;
}

export interface LoginStatusResponse extends ApiResponse<LoginStatusData> {}

// 二维码相关类型
export interface QrCodeData {
  QrCodeUrl?: string;
  QrCodeBase64?: string;
  uuid?: string;
  status?: string;
  expiredTime?: number;
}

export interface QrCodeResponse extends ApiResponse<QrCodeData> {}

// 登录二维码请求参数
export interface GetLoginQrCodeRequest {
  Proxy?: string;
  Check?: boolean;
}

// 用户信息类型
export interface UserProfileData {
  wxid?: string;
  nickname?: string;
  headImgUrl?: string;
  signature?: string;
  sex?: number;
  country?: string;
  province?: string;
  city?: string;
  alias?: string;
}

export interface UserProfileResponse extends ApiResponse<UserProfileData> {}

// 设备登录相关类型
export interface DeviceLoginRequest {
  username?: string;
  password?: string;
  deviceId?: string;
  a16?: string;
}

// 验证码相关类型
export interface VerificationCodeRequest {
  ticket?: string;
  verificationCode?: string;
}

// 代理配置类型
export interface ProxyConfig {
  host?: string;
  port?: number;
  username?: string;
  password?: string;
  type?: 'http' | 'socks5';
}

// 微信API客户端配置
export interface WeChatApiConfig {
  baseUrl: string;
  apiKey: string;
  timeout?: number;
  proxy?: ProxyConfig;
}

// API端点枚举
export enum WeChatApiEndpoints {
  // 登录相关
  GET_LOGIN_STATUS = '/login/GetLoginStatus',
  CHECK_LOGIN_STATUS = '/login/CheckLoginStatus',
  GET_LOGIN_QRCODE = '/login/GetLoginQrCodeNew',
  GET_LOGIN_QRCODE_X = '/login/GetLoginQrCodeNewX',
  LOGOUT = '/login/LogOut',
  CHECK_CAN_SET_ALIAS = '/login/CheckCanSetAlias',
  GET_INIT_STATUS = '/login/GetInItStatus',
  
  // 用户相关
  GET_PROFILE = '/user/GetProfile',
  GET_PROXY_INFO = '/user/GetProxyInfo',
  
  // 设备相关
  DEVICE_LOGIN = '/login/DeviceLogin',
  A16_LOGIN = '/login/A16Login',
  SMS_LOGIN = '/login/SmsLogin',
  
  // 验证码相关
  VERIFICATION_CODE = '/login/Verificationcode',
  AUTO_VERIFICATION_CODE = '/login/AutoVerificationcode',
}

// 登录状态枚举
export enum LoginState {
  NO_LOGIN = 0,
  WAITING_SCAN = 1,
  SCAN_SUCCESS = 2,
  LOGIN_SUCCESS = 3,
  LOGIN_FAILED = 4,
  EXPIRED = 5,
  OFFLINE = 6,
}

// API响应状态码枚举
export enum ApiResponseCode {
  SUCCESS = 200,
  WAITING = 300,
  UNAUTHORIZED = 401,
  FORBIDDEN = 403,
  NOT_FOUND = 404,
  SERVER_ERROR = 500,
  CONNECTION_ERROR = -1,
  LINK_NOT_EXISTS = -2,
}

// 微信API客户端类型
export interface WeChatApiClient {
  // 配置
  baseUrl: string;
  apiKey: string;
  timeout: number;
  proxy?: ProxyConfig;
  
  // 登录相关方法
  getLoginStatus(): Promise<LoginStatusResponse>;
  getQrCode(proxy?: string): Promise<QrCodeResponse>;
  checkLoginStatus(): Promise<ApiResponse>;
  logout(): Promise<ApiResponse>;
  
  // 用户相关方法
  getUserInfo(): Promise<UserProfileResponse>;
  
  // 内部方法
  makeRequest<T>(
    method: 'GET' | 'POST',
    endpoint: string,
    data?: any,
    useKeyParam?: boolean
  ): Promise<ApiResponse<T>>;
}

// 错误类型
export interface WeChatApiError {
  code: number;
  message: string;
  endpoint?: string;
  originalError?: any;
}

// 事件类型
export interface LoginStatusChangeEvent {
  oldStatus: LoginState;
  newStatus: LoginState;
  data?: LoginStatusData;
}

export interface QrCodeExpiredEvent {
  expiredTime: number;
  elapsedTime: number;
}

// 配置验证结果
export interface ConfigValidationResult {
  isValid: boolean;
  errors: string[];
  warnings: string[];
}

// 工具函数类型
export interface WeChatApiUtils {
  validateConfig(config: WeChatApiConfig): ConfigValidationResult;
  formatApiUrl(baseUrl: string, endpoint: string, apiKey: string): string;
  parseLoginState(code: number): LoginState;
  isLoginSuccess(response: LoginStatusResponse): boolean;
  isQrCodeExpired(qrCodeData: QrCodeData): boolean;
}

// 常量
export const DEFAULT_TIMEOUT = 30000;
export const DEFAULT_QRCODE_EXPIRED_TIME = 300; // 5分钟
export const API_KEY_PARAM_NAME = 'key';

// 导出所有类型
export type {
  ApiResponse,
  LoginStatusData,
  LoginStatusResponse,
  QrCodeData,
  QrCodeResponse,
  GetLoginQrCodeRequest,
  UserProfileData,
  UserProfileResponse,
  DeviceLoginRequest,
  VerificationCodeRequest,
  ProxyConfig,
  WeChatApiConfig,
  WeChatApiClient,
  WeChatApiError,
  LoginStatusChangeEvent,
  QrCodeExpiredEvent,
  ConfigValidationResult,
  WeChatApiUtils,
};

export {
  WeChatApiEndpoints,
  LoginState,
  ApiResponseCode,
  DEFAULT_TIMEOUT,
  DEFAULT_QRCODE_EXPIRED_TIME,
  API_KEY_PARAM_NAME,
};
