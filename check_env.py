#!/usr/bin/env python3
"""
环境变量检查脚本
验证.env文件配置是否正确
"""
import os
import sys
import logging
from app.utils.env_loader import EnvLoader

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


def check_env_file_exists():
    """检查.env文件是否存在"""
    if os.path.exists('.env'):
        logger.info("✓ .env文件存在")
        return True
    else:
        logger.error("✗ .env文件不存在")
        if os.path.exists('.env.example'):
            logger.info("提示: 请复制.env.example为.env并配置相关参数")
        else:
            logger.info("正在创建.env.example模板文件...")
            EnvLoader.create_env_example()
        return False


def check_env_variables():
    """检查必需的环境变量"""
    logger.info("检查必需的环境变量...")
    
    # 加载环境变量
    EnvLoader.load_env_file()
    
    # 验证必需的环境变量
    is_valid, missing_vars = EnvLoader.validate_required_env()
    
    if is_valid:
        logger.info("✓ 所有必需的环境变量都已设置")
        return True
    else:
        logger.error(f"✗ 缺少必需的环境变量: {', '.join(missing_vars)}")
        return False


def check_database_config():
    """检查数据库配置"""
    logger.info("检查数据库配置...")
    
    try:
        db_config = EnvLoader.get_database_config()
        logger.info(f"✓ 数据库主机: {db_config['host']}")
        logger.info(f"✓ 数据库端口: {db_config['port']}")
        logger.info(f"✓ 数据库用户: {db_config['user']}")
        logger.info(f"✓ 数据库名称: {db_config['database']}")
        logger.info("✓ 数据库配置正常")
        return True
    except ValueError as e:
        logger.error(f"✗ 数据库配置错误: {str(e)}")
        return False


def check_flask_config():
    """检查Flask配置"""
    logger.info("检查Flask配置...")
    
    try:
        flask_config = EnvLoader.get_flask_config()
        logger.info(f"✓ Flask主机: {flask_config['HOST']}")
        logger.info(f"✓ Flask端口: {flask_config['PORT']}")
        logger.info(f"✓ Flask环境: {flask_config['ENV']}")
        logger.info(f"✓ Flask调试模式: {flask_config['DEBUG']}")
        logger.info("✓ Flask配置正常")
        return True
    except ValueError as e:
        logger.error(f"✗ Flask配置错误: {str(e)}")
        return False


def check_admin_config():
    """检查管理员配置"""
    logger.info("检查管理员配置...")
    
    try:
        admin_config = EnvLoader.get_admin_config()
        admin_token = admin_config['ADMIN_TOKEN']
        if len(admin_token) >= 16:
            logger.info("✓ 管理员Token长度合适")
        else:
            logger.warning("⚠ 管理员Token长度较短，建议使用更长的Token")
        logger.info("✓ 管理员配置正常")
        return True
    except ValueError as e:
        logger.error(f"✗ 管理员配置错误: {str(e)}")
        return False


def test_database_connection():
    """测试数据库连接"""
    logger.info("测试数据库连接...")
    
    try:
        from app.models import get_db_connection
        connection = get_db_connection()
        with connection.cursor() as cursor:
            cursor.execute("SELECT 1")
            result = cursor.fetchone()
            if result:
                logger.info("✓ 数据库连接成功")
                return True
        connection.close()
    except Exception as e:
        logger.error(f"✗ 数据库连接失败: {str(e)}")
        return False


def show_current_env():
    """显示当前环境变量值（隐藏敏感信息）"""
    logger.info("当前环境变量配置:")
    
    env_vars = [
        'MYSQL_HOST',
        'MYSQL_PORT', 
        'MYSQL_USER',
        'MYSQL_DB',
        'FLASK_HOST',
        'FLASK_PORT',
        'FLASK_ENV',
        'FLASK_DEBUG'
    ]
    
    for var in env_vars:
        value = os.environ.get(var, '未设置')
        logger.info(f"  {var}: {value}")
    
    # 敏感信息只显示是否设置
    sensitive_vars = ['SECRET_KEY', 'MYSQL_PASSWORD', 'ADMIN_TOKEN']
    for var in sensitive_vars:
        value = os.environ.get(var)
        status = "已设置" if value else "未设置"
        logger.info(f"  {var}: {status}")


def main():
    """主函数"""
    logger.info("=== YBA监控管理系统环境变量检查 ===")
    
    checks = [
        ("环境文件存在性", check_env_file_exists),
        ("必需环境变量", check_env_variables),
        ("数据库配置", check_database_config),
        ("Flask配置", check_flask_config),
        ("管理员配置", check_admin_config),
        ("数据库连接", test_database_connection)
    ]
    
    passed = 0
    total = len(checks)
    
    for check_name, check_func in checks:
        logger.info(f"\n--- 检查: {check_name} ---")
        try:
            if check_func():
                passed += 1
                logger.info(f"✓ {check_name} 检查通过")
            else:
                logger.error(f"✗ {check_name} 检查失败")
        except Exception as e:
            logger.error(f"✗ {check_name} 检查异常: {str(e)}")
    
    logger.info(f"\n=== 检查结果 ===")
    logger.info(f"通过: {passed}/{total}")
    logger.info(f"成功率: {passed/total*100:.1f}%")
    
    # 显示当前环境变量
    logger.info(f"\n=== 环境变量配置 ===")
    show_current_env()
    
    if passed == total:
        logger.info("\n🎉 所有检查通过！环境配置正确。")
        return True
    else:
        logger.warning("\n⚠️ 部分检查失败，请修复相关问题。")
        return False


if __name__ == '__main__':
    success = main()
    sys.exit(0 if success else 1)
