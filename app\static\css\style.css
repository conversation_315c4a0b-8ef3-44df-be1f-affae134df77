/* 统一Web管理系统样式 */

:root {
    /* 简化的配色方案 */
    --primary-color: #3b82f6;
    --primary-hover: #2563eb;
    --success-color: #10b981;
    --warning-color: #f59e0b;
    --danger-color: #ef4444;
    --secondary-color: #6b7280;

    --sidebar-width: 280px;
    --sidebar-collapsed-width: 70px;
    --topbar-height: 70px;

    /* 提高对比度的背景色 */
    --bg-primary: #111827;
    --bg-secondary: #1f2937;
    --bg-tertiary: #374151;
    --bg-hover: #4b5563;

    /* 提高对比度的文字颜色 */
    --text-primary: #ffffff;
    --text-secondary: #d1d5db;
    --text-muted: #9ca3af;
    --border-color: #4b5563;

    /* 简化的阴影 */
    --shadow-sm: 0 1px 3px 0 rgba(0, 0, 0, 0.1);
    --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.15);
    --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.2);

    --border-radius: 8px;
    --border-radius-sm: 6px;
    --transition: all 0.2s ease;
}

/* 全局样式 */
* {
    box-sizing: border-box;
}

body {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    background-color: var(--bg-primary);
    color: var(--text-primary);
    margin: 0;
    padding: 0;
    overflow-x: hidden;
}

/* 布局容器 */
.wrapper {
    display: flex;
    min-height: 100vh;
}

/* 侧边导航栏 */
.sidebar {
    width: var(--sidebar-width);
    background: var(--bg-secondary);
    border-right: 1px solid var(--border-color);
    position: fixed;
    top: 0;
    left: 0;
    height: 100vh;
    z-index: 1000;
    transition: var(--transition);
    overflow-y: auto;
}

.sidebar.collapsed {
    width: var(--sidebar-collapsed-width);
}

.sidebar-header {
    padding: 1.5rem 1.25rem;
    border-bottom: 1px solid var(--border-color);
    background: var(--bg-tertiary);
}

.sidebar-header h4 {
    color: var(--primary-color);
    font-weight: 600;
    margin: 0;
    font-size: 1.1rem;
    transition: var(--transition);
    white-space: nowrap;
    overflow: hidden;
}

.sidebar.collapsed .sidebar-header h4 {
    opacity: 0;
    width: 0;
}

/* 导航菜单 */
.sidebar-nav {
    list-style: none;
    padding: 1rem 0;
    margin: 0;
}

.sidebar-nav .nav-item {
    margin: 0.25rem 0.75rem;
}

.sidebar-nav .nav-link {
    display: flex;
    align-items: center;
    padding: 0.875rem 1rem;
    color: var(--text-secondary);
    text-decoration: none;
    border-radius: var(--border-radius-sm);
    transition: var(--transition);
    font-weight: 500;
    position: relative;
    overflow: hidden;
}

.sidebar-nav .nav-link:hover {
    background: var(--bg-hover);
    color: var(--text-primary);
}

.sidebar-nav .nav-link.active {
    background: var(--primary-color);
    color: white;
}

.sidebar-nav .nav-link i {
    font-size: 1.25rem;
    margin-right: 0.75rem;
    width: 24px;
    min-width: 24px;
    text-align: center;
    transition: var(--transition);
}

.sidebar-nav .nav-link span {
    transition: var(--transition);
    white-space: nowrap;
    overflow: hidden;
}

.sidebar.collapsed .nav-link span {
    opacity: 0;
    width: 0;
    margin-left: 0;
}

.sidebar.collapsed .nav-link {
    justify-content: center;
    padding: 0.875rem;
    margin: 0.25rem;
}

.sidebar.collapsed .nav-link i {
    margin-right: 0;
}

/* 侧边栏底部 */
.sidebar-footer {
    margin-top: auto;
    padding: 1rem 0.75rem;
    border-top: 1px solid var(--border-color);
}

.sidebar-heading {
    color: var(--text-secondary);
    font-size: 0.75rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.05em;
    margin: 0 0 0.5rem 1rem;
    transition: var(--transition);
    white-space: nowrap;
    overflow: hidden;
}

.sidebar.collapsed .sidebar-heading {
    opacity: 0;
    width: 0;
    margin: 0;
}

.sidebar.collapsed .sidebar-footer {
    padding: 1rem 0.5rem;
}

/* 主内容区域 */
.main-content {
    flex: 1;
    margin-left: var(--sidebar-width);
    transition: var(--transition);
    min-height: 100vh;
    display: flex;
    flex-direction: column;
}

.sidebar.collapsed + .main-content {
    margin-left: var(--sidebar-collapsed-width);
}

/* 顶部导航栏 */
.topbar {
    height: var(--topbar-height);
    background: var(--bg-secondary);
    border-bottom: 1px solid var(--border-color);
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 1.5rem;
    box-shadow: var(--shadow-sm);
    position: sticky;
    top: 0;
    z-index: 999;
}

.topbar-left {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.sidebar-toggle {
    color: var(--text-secondary);
    border: none;
    background: none;
    font-size: 1.25rem;
    padding: 0.5rem;
    border-radius: var(--border-radius-sm);
    transition: var(--transition);
}

.sidebar-toggle:hover {
    color: var(--primary-color);
    background: rgba(99, 102, 241, 0.1);
}

.page-title {
    color: var(--text-primary);
    font-weight: 600;
}

.topbar-right {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.user-info .btn {
    color: var(--text-secondary);
    border: none;
    background: none;
    padding: 0.5rem 1rem;
    border-radius: var(--border-radius-sm);
    transition: var(--transition);
}

.user-info .btn:hover {
    color: var(--primary-color);
    background: rgba(99, 102, 241, 0.1);
}

.user-name {
    font-weight: 500;
}

/* 主要内容区域 */
.content {
    flex: 1;
    padding: 2rem;
    background: var(--bg-primary);
}

/* 消息提示 */
.alerts-container {
    margin-bottom: 1.5rem;
}

.alert {
    border: none;
    border-radius: var(--border-radius);
    padding: 1rem 1.25rem;
    margin-bottom: 1rem;
    box-shadow: var(--shadow-sm);
    animation: slideInDown 0.3s ease-out;
}

@keyframes slideInDown {
    from {
        opacity: 0;
        transform: translateY(-20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* 卡片样式 */
.card {
    background: var(--bg-secondary);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    box-shadow: var(--shadow-md);
    transition: var(--transition);
}

.card:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
}

.card-header {
    background: rgba(99, 102, 241, 0.05);
    border-bottom: 1px solid var(--border-color);
    padding: 1.25rem;
    border-radius: var(--border-radius) var(--border-radius) 0 0;
}

.card-body {
    padding: 1.5rem;
}

/* 按钮样式 */
.btn {
    border-radius: var(--border-radius-sm);
    font-weight: 500;
    padding: 0.625rem 1.25rem;
    transition: var(--transition);
    border: none;
}

.btn-primary {
    background: var(--primary-color);
    color: white;
}

.btn-primary:hover {
    background: var(--primary-hover);
    color: white;
}

/* 表格样式 */
.table {
    background: var(--bg-secondary);
    border-radius: var(--border-radius);
    overflow: hidden;
    box-shadow: var(--shadow-sm);
}

.table th {
    background: var(--bg-tertiary);
    border-bottom: 1px solid var(--border-color);
    font-weight: 600;
    color: var(--text-primary);
}

.table td {
    border-bottom: 1px solid var(--border-color);
    color: var(--text-secondary);
}

/* 表单样式 */
.form-control {
    background: var(--bg-tertiary);
    border: 1px solid var(--border-color);
    color: var(--text-primary);
    border-radius: var(--border-radius-sm);
    transition: var(--transition);
}

.form-control:focus {
    background: var(--bg-secondary);
    border-color: var(--primary-color);
    box-shadow: 0 0 0 0.2rem rgba(99, 102, 241, 0.25);
}

/* 加载指示器 */
.loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(15, 23, 42, 0.8);
    display: none;
    justify-content: center;
    align-items: center;
    z-index: 9999;
    backdrop-filter: blur(4px);
}

.loading-spinner {
    text-align: center;
    color: var(--text-primary);
}

.loading-text {
    margin-top: 1rem;
    font-weight: 500;
}

/* 登录页面样式 */
.login-wrapper {
    min-height: 100vh;
    display: flex;
    align-items: center;
    justify-content: center;
    background: linear-gradient(135deg, var(--bg-primary) 0%, var(--bg-secondary) 100%);
}

/* 高级UI组件 */

/* 数据表格 */
.data-table {
    background: var(--bg-secondary);
    border-radius: var(--border-radius);
    overflow: hidden;
    box-shadow: var(--shadow-sm);
    border: 1px solid var(--border-color);
}

.data-table .table {
    margin: 0;
    background: transparent;
}

.data-table .table thead th {
    background: var(--bg-tertiary);
    border-bottom: 2px solid var(--border-color);
    font-weight: 600;
    color: var(--text-primary);
    padding: 1rem;
    position: sticky;
    top: 0;
    z-index: 10;
}

.data-table .table tbody td {
    padding: 1rem;
    border-bottom: 1px solid var(--border-color);
    color: var(--text-secondary);
    vertical-align: middle;
}

.data-table .table tbody tr {
    transition: var(--transition);
}

.data-table .table tbody tr:hover {
    background: rgba(99, 102, 241, 0.05);
}

.data-table .table tbody tr:last-child td {
    border-bottom: none;
}

/* 状态徽章 */
.status-badge {
    display: inline-flex;
    align-items: center;
    gap: 0.25rem;
    padding: 0.25rem 0.75rem;
    border-radius: 50px;
    font-size: 0.75rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.025em;
}

.status-badge.status-active {
    background: rgba(16, 185, 129, 0.1);
    color: var(--success-color);
    border: 1px solid rgba(16, 185, 129, 0.2);
}

.status-badge.status-inactive {
    background: rgba(107, 114, 128, 0.1);
    color: var(--secondary-color);
    border: 1px solid rgba(107, 114, 128, 0.2);
}

.status-badge.status-warning {
    background: rgba(245, 158, 11, 0.1);
    color: var(--warning-color);
    border: 1px solid rgba(245, 158, 11, 0.2);
}

.status-badge.status-error {
    background: rgba(239, 68, 68, 0.1);
    color: var(--danger-color);
    border: 1px solid rgba(239, 68, 68, 0.2);
}

.status-badge i {
    font-size: 0.7rem;
}

/* 操作按钮组 */
.action-buttons {
    display: flex;
    gap: 0.5rem;
    align-items: center;
}

.action-btn {
    width: 32px;
    height: 32px;
    border-radius: 8px;
    border: none;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 0.875rem;
    transition: var(--transition);
    cursor: pointer;
}

.action-btn:hover {
    transform: translateY(-1px);
    box-shadow: var(--shadow-sm);
}

.action-btn.btn-edit {
    background: rgba(59, 130, 246, 0.1);
    color: var(--info-color);
}

.action-btn.btn-edit:hover {
    background: rgba(59, 130, 246, 0.2);
}

.action-btn.btn-delete {
    background: rgba(239, 68, 68, 0.1);
    color: var(--danger-color);
}

.action-btn.btn-delete:hover {
    background: rgba(239, 68, 68, 0.2);
}

.action-btn.btn-view {
    background: rgba(16, 185, 129, 0.1);
    color: var(--success-color);
}

.action-btn.btn-view:hover {
    background: rgba(16, 185, 129, 0.2);
}

/* 搜索框 */
.search-box {
    position: relative;
    max-width: 400px;
}

.search-box .form-control {
    padding-left: 2.5rem;
    border-radius: 50px;
    border: 2px solid transparent;
    background: var(--bg-tertiary);
    transition: var(--transition);
}

.search-box .form-control:focus {
    border-color: var(--primary-color);
    background: var(--bg-secondary);
    box-shadow: 0 0 0 0.2rem rgba(99, 102, 241, 0.1);
}

.search-box .search-icon {
    position: absolute;
    left: 0.875rem;
    top: 50%;
    transform: translateY(-50%);
    color: var(--text-secondary);
    font-size: 1rem;
    pointer-events: none;
}

/* 分页组件 */
.pagination-wrapper {
    display: flex;
    justify-content: between;
    align-items: center;
    margin-top: 1.5rem;
    padding: 1rem;
    background: var(--bg-tertiary);
    border-radius: var(--border-radius);
}

.pagination-info {
    color: var(--text-secondary);
    font-size: 0.875rem;
}

.pagination {
    margin: 0;
}

.pagination .page-link {
    background: transparent;
    border: 1px solid var(--border-color);
    color: var(--text-secondary);
    padding: 0.5rem 0.75rem;
    margin: 0 0.125rem;
    border-radius: var(--border-radius-sm);
    transition: var(--transition);
}

.pagination .page-link:hover {
    background: var(--primary-color);
    border-color: var(--primary-color);
    color: white;
    transform: translateY(-1px);
}

.pagination .page-item.active .page-link {
    background: var(--primary-color);
    border-color: var(--primary-color);
    color: white;
    box-shadow: var(--shadow-sm);
}

.pagination .page-item.disabled .page-link {
    opacity: 0.5;
    cursor: not-allowed;
}

/* 模态框增强 */
.modal-content {
    background: var(--bg-secondary);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    box-shadow: var(--shadow-lg);
}

.modal-header {
    background: rgba(99, 102, 241, 0.05);
    border-bottom: 1px solid var(--border-color);
    border-radius: var(--border-radius) var(--border-radius) 0 0;
}

.modal-title {
    color: var(--text-primary);
    font-weight: 600;
}

.modal-body {
    color: var(--text-secondary);
}

.modal-footer {
    background: var(--bg-tertiary);
    border-top: 1px solid var(--border-color);
    border-radius: 0 0 var(--border-radius) var(--border-radius);
}

/* 进度条 */
.progress-bar-custom {
    height: 8px;
    background: var(--bg-tertiary);
    border-radius: 4px;
    overflow: hidden;
    margin: 0.5rem 0;
}

.progress-bar-custom .progress-fill {
    height: 100%;
    background: linear-gradient(90deg, var(--primary-color), var(--success-color));
    border-radius: 4px;
    transition: width 0.3s ease;
    position: relative;
}

.progress-bar-custom .progress-fill::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    animation: shimmer 2s infinite;
}

@keyframes shimmer {
    0% { transform: translateX(-100%); }
    100% { transform: translateX(100%); }
}

/* 工具提示增强 */
.tooltip-custom {
    position: relative;
    cursor: help;
}

.tooltip-custom::before {
    content: attr(data-tooltip);
    position: absolute;
    bottom: 100%;
    left: 50%;
    transform: translateX(-50%);
    background: var(--bg-primary);
    color: var(--text-primary);
    padding: 0.5rem 0.75rem;
    border-radius: var(--border-radius-sm);
    font-size: 0.75rem;
    white-space: nowrap;
    opacity: 0;
    visibility: hidden;
    transition: var(--transition);
    z-index: 1000;
    border: 1px solid var(--border-color);
    box-shadow: var(--shadow-md);
}

.tooltip-custom::after {
    content: '';
    position: absolute;
    bottom: 100%;
    left: 50%;
    transform: translateX(-50%);
    border: 4px solid transparent;
    border-top-color: var(--border-color);
    opacity: 0;
    visibility: hidden;
    transition: var(--transition);
}

.tooltip-custom:hover::before,
.tooltip-custom:hover::after {
    opacity: 1;
    visibility: visible;
}

/* 移动端优化样式 */
.mobile-only {
    display: none;
}

.desktop-only {
    display: block;
}

/* 搜索框清除按钮 */
.search-clear {
    position: absolute;
    right: 0.75rem;
    top: 50%;
    transform: translateY(-50%);
    background: none;
    border: none;
    color: var(--text-secondary);
    font-size: 1rem;
    cursor: pointer;
    padding: 0.25rem;
    border-radius: 50%;
    transition: var(--transition);
    display: none;
}

.search-clear:hover {
    background: rgba(239, 68, 68, 0.1);
    color: var(--danger-color);
}

/* 卡片悬停效果 */
.card-hover {
    transform: translateY(-4px) !important;
    box-shadow: var(--shadow-lg) !important;
}

/* 懒加载图片 */
img.lazy {
    opacity: 0;
    transition: opacity 0.3s;
}

img.lazy.loaded {
    opacity: 1;
}

/* 表格排序图标 */
.data-table thead th[data-sort] {
    cursor: pointer;
    position: relative;
    user-select: none;
}

.data-table thead th[data-sort]:hover {
    background: rgba(99, 102, 241, 0.1);
}

.data-table thead th[data-sort]::after {
    content: '\f0dc';
    font-family: 'bootstrap-icons';
    position: absolute;
    right: 0.5rem;
    top: 50%;
    transform: translateY(-50%);
    opacity: 0.5;
    font-size: 0.75rem;
}

.data-table thead th.sort-asc::after {
    content: '\f0d8';
    opacity: 1;
    color: var(--primary-color);
}

.data-table thead th.sort-desc::after {
    content: '\f0d7';
    opacity: 1;
    color: var(--primary-color);
}

/* 表格选中行 */
.data-table tbody tr.selected {
    background: rgba(99, 102, 241, 0.1) !important;
    border-left: 3px solid var(--primary-color);
}

/* 响应式设计 */
@media (max-width: 1200px) {
    .sidebar {
        width: 240px;
    }

    .main-content {
        margin-left: 240px;
    }

    .sidebar.collapsed + .main-content {
        margin-left: var(--sidebar-collapsed-width);
    }
}

@media (max-width: 992px) {
    .stats-section .col-xl-3 {
        flex: 0 0 50%;
        max-width: 50%;
    }

    .quick-actions-grid,
    .admin-actions-grid {
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    }
}

@media (max-width: 768px) {
    .sidebar {
        transform: translateX(-100%);
        width: var(--sidebar-width);
        z-index: 1050;
    }

    .sidebar.show {
        transform: translateX(0);
    }

    .main-content {
        margin-left: 0;
    }

    .content {
        padding: 1rem;
    }

    .topbar {
        padding: 0 1rem;
    }

    .topbar-left .page-title {
        font-size: 1.1rem;
    }

    .user-info .user-name {
        display: none;
    }

    .stats-section .col-xl-3,
    .stats-section .col-md-6 {
        flex: 0 0 100%;
        max-width: 100%;
    }

    .stat-card {
        padding: 1rem;
    }

    .stat-icon {
        width: 50px;
        height: 50px;
        font-size: 1.25rem;
    }

    .stat-number {
        font-size: 1.5rem;
    }

    .data-table {
        font-size: 0.875rem;
        overflow-x: auto;
    }

    .data-table .table {
        min-width: 600px;
    }

    .data-table .table thead th,
    .data-table .table tbody td {
        padding: 0.75rem 0.5rem;
        white-space: nowrap;
    }

    .action-buttons {
        flex-direction: column;
        gap: 0.25rem;
    }

    .action-btn {
        width: 28px;
        height: 28px;
        font-size: 0.75rem;
    }

    .search-box {
        max-width: 100%;
    }

    .pagination-wrapper {
        flex-direction: column;
        gap: 1rem;
        text-align: center;
    }

    .quick-actions-grid,
    .admin-actions-grid {
        grid-template-columns: 1fr;
        gap: 0.75rem;
    }

    .action-card {
        padding: 1rem;
    }

    .action-icon {
        width: 40px;
        height: 40px;
        font-size: 1rem;
    }

    .mobile-only {
        display: block;
    }

    .desktop-only {
        display: none;
    }

    .modal-dialog {
        margin: 1rem;
        max-width: calc(100% - 2rem);
    }

    .card-body {
        padding: 1rem;
    }

    .help-steps {
        gap: 1rem;
    }

    .step-number {
        width: 28px;
        height: 28px;
        font-size: 0.8rem;
    }
}

@media (max-width: 576px) {
    .content {
        padding: 0.75rem;
    }

    .topbar {
        height: 60px;
        padding: 0 0.75rem;
    }

    .sidebar-header {
        padding: 1rem;
    }

    .sidebar-header h4 {
        font-size: 1rem;
    }

    .sidebar-nav .nav-link {
        padding: 0.75rem;
        font-size: 0.9rem;
    }

    .sidebar-nav .nav-link i {
        font-size: 1.1rem;
    }

    .stat-card {
        flex-direction: column;
        text-align: center;
        gap: 0.75rem;
    }

    .stat-icon {
        width: 60px;
        height: 60px;
        font-size: 1.5rem;
        margin: 0 auto;
    }

    .action-card {
        flex-direction: column;
        text-align: center;
        gap: 0.75rem;
    }

    .action-icon {
        width: 50px;
        height: 50px;
        font-size: 1.25rem;
        margin: 0 auto;
    }

    .system-info .info-row,
    .quick-stats .stat-row {
        flex-direction: column;
        gap: 0.25rem;
        text-align: center;
    }

    .info-row .value,
    .stat-row .value {
        text-align: center;
    }

    .qrcode-wrapper {
        padding: 1rem;
    }

    .qrcode-image {
        width: 200px;
        height: 200px;
    }

    .help-card .card-body {
        padding: 1rem;
    }
}

/* 打印样式 */
@media print {
    .sidebar,
    .topbar,
    .action-buttons,
    .pagination-wrapper,
    .btn,
    .alert {
        display: none !important;
    }

    .main-content {
        margin-left: 0 !important;
    }

    .content {
        padding: 0 !important;
    }

    .card {
        border: 1px solid #000 !important;
        box-shadow: none !important;
    }

    .data-table .table {
        border-collapse: collapse;
    }

    .data-table .table th,
    .data-table .table td {
        border: 1px solid #000 !important;
        padding: 0.5rem !important;
    }
}
