{% extends "base.html" %}

{% block title %}用户VIP卡 - YBA监控管理系统{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- 页面标题 -->
    <div class="page-header mb-4">
        <div class="row align-items-center">
            <div class="col">
                <h1 class="page-title">
                    <i class="bi bi-person-badge me-2"></i>用户VIP卡
                </h1>
                <p class="text-muted">用户ID: <code>{{ user_id }}</code></p>
            </div>
            <div class="col-auto">
                <a href="{{ url_for('vip.cards') }}" class="btn btn-outline-secondary">
                    <i class="bi bi-arrow-left me-2"></i>返回列表
                </a>
            </div>
        </div>
    </div>

    <!-- 用户VIP卡列表 -->
    <div class="card">
        <div class="card-header">
            <h5 class="card-title mb-0">
                VIP卡列表 
                <span class="badge bg-secondary ms-2">共 {{ cards|length }} 张</span>
            </h5>
        </div>
        <div class="card-body p-0">
            {% if cards %}
            <div class="table-responsive">
                <table class="table table-hover mb-0">
                    <thead class="table-light">
                        <tr>
                            <th>卡号</th>
                            <th>积分</th>
                            <th>状态</th>
                            <th>代理商</th>
                            <th>绑定时间</th>
                            <th>过期时间</th>
                            <th>操作</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for card in cards %}
                        <tr>
                            <td>
                                <code class="text-primary">{{ card.card_number }}</code>
                            </td>
                            <td>
                                <span class="badge bg-warning text-dark">{{ card.points or 0 }}</span>
                            </td>
                            <td>
                                {% if card.is_active %}
                                    <span class="badge bg-success">已激活</span>
                                {% else %}
                                    <span class="badge bg-secondary">未激活</span>
                                {% endif %}
                            </td>
                            <td>
                                <small class="text-muted">
                                    {{ card.agent_name or '未知' }}
                                    {% if card.agent_group %}
                                        <br><span class="badge bg-light text-dark">{{ card.agent_group }}</span>
                                    {% endif %}
                                </small>
                            </td>
                            <td>
                                <small class="text-muted">
                                    {{ card.bind_time.strftime('%Y-%m-%d %H:%M') if card.bind_time else '未知' }}
                                </small>
                            </td>
                            <td>
                                {% if card.expired_at %}
                                    <small class="text-muted">
                                        {{ card.expired_at.strftime('%Y-%m-%d') }}
                                        {% if card.expired_at < moment().date() %}
                                            <span class="badge bg-danger ms-1">已过期</span>
                                        {% endif %}
                                    </small>
                                {% else %}
                                    <small class="text-muted">永久有效</small>
                                {% endif %}
                            </td>
                            <td>
                                <div class="btn-group btn-group-sm">
                                    <button type="button" class="btn btn-outline-primary btn-sm" 
                                            onclick="viewCard('{{ card.id }}')">
                                        <i class="bi bi-eye"></i>
                                    </button>
                                    <button type="button" class="btn btn-outline-info btn-sm" 
                                            onclick="updatePoints('{{ card.id }}', {{ card.points or 0 }})">
                                        <i class="bi bi-star"></i>
                                    </button>
                                </div>
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
            {% else %}
            <div class="text-center py-5">
                <i class="bi bi-credit-card-2-front text-muted" style="font-size: 3rem;"></i>
                <h5 class="text-muted mt-3">该用户暂无VIP卡</h5>
                <p class="text-muted">用户 {{ user_id }} 还没有绑定任何VIP卡</p>
                <a href="{{ url_for('vip.create_card') }}" class="btn btn-primary">
                    <i class="bi bi-plus-circle me-2"></i>创建VIP卡
                </a>
            </div>
            {% endif %}
        </div>
    </div>

    <!-- 统计信息 -->
    {% if cards %}
    <div class="row mt-4">
        <div class="col-md-3">
            <div class="card text-center">
                <div class="card-body">
                    <h5 class="card-title text-primary">{{ cards|length }}</h5>
                    <p class="card-text">总卡数</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card text-center">
                <div class="card-body">
                    <h5 class="card-title text-success">
                        {{ cards|selectattr('is_active')|list|length }}
                    </h5>
                    <p class="card-text">已激活</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card text-center">
                <div class="card-body">
                    <h5 class="card-title text-warning">
                        {{ cards|sum(attribute='points') or 0 }}
                    </h5>
                    <p class="card-text">总积分</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card text-center">
                <div class="card-body">
                    <h5 class="card-title text-info">
                        {{ cards|selectattr('expired_at')|selectattr('expired_at', 'lt', moment().date())|list|length }}
                    </h5>
                    <p class="card-text">已过期</p>
                </div>
            </div>
        </div>
    </div>
    {% endif %}
</div>

<!-- 卡片详情模态框 -->
<div class="modal fade" id="cardModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">VIP卡详情</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body" id="cardModalBody">
                <!-- 卡片详情内容 -->
            </div>
        </div>
    </div>
</div>

<!-- 积分更新模态框 -->
<div class="modal fade" id="pointsModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">更新积分</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="pointsForm">
                    <input type="hidden" id="cardId" name="cardId">
                    <div class="mb-3">
                        <label for="currentPoints" class="form-label">当前积分</label>
                        <input type="number" class="form-control" id="currentPoints" readonly>
                    </div>
                    <div class="mb-3">
                        <label for="newPoints" class="form-label">新积分</label>
                        <input type="number" class="form-control" id="newPoints" name="newPoints" min="0" required>
                    </div>
                    <div class="mb-3">
                        <label for="reason" class="form-label">变更原因</label>
                        <textarea class="form-control" id="reason" name="reason" rows="3" placeholder="请输入积分变更原因..."></textarea>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                <button type="button" class="btn btn-primary" onclick="savePoints()">保存</button>
            </div>
        </div>
    </div>
</div>

<script>
function viewCard(cardId) {
    // 查看卡片详情
    fetch(`/vip/cards/${cardId}/details`)
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                document.getElementById('cardModalBody').innerHTML = data.html;
                new bootstrap.Modal(document.getElementById('cardModal')).show();
            } else {
                alert('获取卡片详情失败: ' + data.message);
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('获取卡片详情失败');
        });
}

function updatePoints(cardId, currentPoints) {
    document.getElementById('cardId').value = cardId;
    document.getElementById('currentPoints').value = currentPoints;
    document.getElementById('newPoints').value = currentPoints;
    document.getElementById('reason').value = '';
    
    new bootstrap.Modal(document.getElementById('pointsModal')).show();
}

function savePoints() {
    const form = document.getElementById('pointsForm');
    const formData = new FormData(form);
    
    const cardId = formData.get('cardId');
    const newPoints = formData.get('newPoints');
    const reason = formData.get('reason');
    
    fetch(`/vip/cards/${cardId}/update_points`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({
            points: parseInt(newPoints),
            reason: reason
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            bootstrap.Modal.getInstance(document.getElementById('pointsModal')).hide();
            location.reload();
        } else {
            alert('更新积分失败: ' + data.message);
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('更新积分失败');
    });
}
</script>
{% endblock %}
