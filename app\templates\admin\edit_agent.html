{% extends "base.html" %}

{% block title %}编辑代理商 - YBA监控管理系统{% endblock %}

{% block page_title %}
编辑代理商
<span class="badge bg-danger ms-2">管理员</span>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- 返回按钮 -->
    <div class="mb-3">
        <a href="{{ url_for('admin.agents') }}" class="btn btn-outline-secondary">
            <i class="bi bi-arrow-left me-1"></i>返回代理商列表
        </a>
    </div>

    <!-- 编辑表单 -->
    <div class="row justify-content-center">
        <div class="col-lg-8">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="bi bi-pencil me-2"></i>编辑代理商: {{ agent.name }}
                        <span class="badge {% if agent.api_version == 'v2' %}bg-success{% else %}bg-info{% endif %} ms-2">
                            {% if agent.api_version == 'v2' %}V2 API{% else %}V1 API{% endif %}
                        </span>
                    </h5>
                </div>
                <div class="card-body">
                    <form method="POST" id="editAgentForm">
                        <!-- 基本信息 -->
                        <div class="section-header">
                            <h6 class="text-primary">
                                <i class="bi bi-info-circle me-1"></i>基本信息
                            </h6>
                            <hr>
                        </div>

                        <div class="row mb-3">
                            <div class="col-md-6">
                                <label for="name" class="form-label">代理商名称 <span class="text-danger">*</span></label>
                                <input type="text" class="form-control" id="name" name="name" value="{{ agent.name }}" required>
                                <div class="form-text">用于系统显示的代理商名称</div>
                            </div>
                            <div class="col-md-6">
                                <label for="group" class="form-label">分组</label>
                                <input type="text" class="form-control" id="group" name="group" value="{{ agent.group or '' }}" placeholder="可选">
                                <div class="form-text">用于分类管理代理商</div>
                            </div>
                        </div>

                        <div class="row mb-3">
                            <div class="col-md-6">
                                <label for="contact" class="form-label">联系方式</label>
                                <input type="text" class="form-control" id="contact" name="contact" value="{{ agent.contact or '' }}" placeholder="电话/邮箱/微信等">
                            </div>
                            <div class="col-md-6">
                                <label for="api_version" class="form-label">API版本 <span class="text-danger">*</span></label>
                                <select class="form-select" id="api_version" name="api_version" required>
                                    <option value="v1" {% if agent.api_version == 'v1' or not agent.api_version %}selected{% endif %}>V1 API (传统版本)</option>
                                    <option value="v2" {% if agent.api_version == 'v2' %}selected{% endif %}>V2 API (新版本)</option>
                                </select>
                            </div>
                        </div>

                        <!-- V1 API 配置 -->
                        <div id="v1Config" class="api-config" {% if agent.api_version == 'v2' %}style="display: none;"{% endif %}>
                            <div class="section-header">
                                <h6 class="text-info">
                                    <i class="bi bi-gear me-1"></i>V1 API 配置
                                </h6>
                                <hr>
                            </div>

                            <div class="row mb-3">
                                <div class="col-md-6">
                                    <label for="token" class="form-label">Token <span class="text-danger">*</span></label>
                                    <input type="text" class="form-control" id="token" name="token" value="{{ agent.token or '' }}">
                                    <div class="form-text">V1 API认证令牌</div>
                                </div>
                                <div class="col-md-6">
                                    <label for="wechat_base_url" class="form-label">微信API地址 <span class="text-danger">*</span></label>
                                    <input type="url" class="form-control" id="wechat_base_url" name="wechat_base_url" 
                                           value="{{ agent.wechat_base_url or '' }}" placeholder="http://example.com:8080">
                                </div>
                            </div>

                            <div class="row mb-3">
                                <div class="col-md-6">
                                    <label for="proxy" class="form-label">代理设置</label>
                                    <input type="text" class="form-control" id="proxy" name="proxy" 
                                           value="{{ agent.proxy or '' }}" placeholder="http://proxy:port (可选)">
                                </div>
                            </div>
                        </div>

                        <!-- V2 API 配置 -->
                        <div id="v2Config" class="api-config" {% if agent.api_version != 'v2' %}style="display: none;"{% endif %}>
                            <div class="section-header">
                                <h6 class="text-success">
                                    <i class="bi bi-qr-code me-1"></i>V2 API 配置
                                </h6>
                                <hr>
                            </div>

                            <div class="row mb-3">
                                <div class="col-md-6">
                                    <label for="wxid" class="form-label">微信ID (WXID) <span class="text-danger">*</span></label>
                                    <input type="text" class="form-control" id="wxid" name="wxid" 
                                           value="{{ agent.wxid or '' }}" placeholder="wxid_xxxxxxxxxx">
                                    <div class="form-text">微信账号的唯一标识符</div>
                                </div>
                                <div class="col-md-6">
                                    <label for="device_id" class="form-label">设备ID <span class="text-danger">*</span></label>
                                    <input type="text" class="form-control" id="device_id" name="device_id" value="{{ agent.device_id or '' }}">
                                    <div class="form-text">设备的唯一标识符</div>
                                </div>
                            </div>

                            <div class="row mb-3">
                                <div class="col-md-6">
                                    <label for="wechat_base_url_v2" class="form-label">微信API地址 <span class="text-danger">*</span></label>
                                    <input type="url" class="form-control" id="wechat_base_url_v2" name="wechat_base_url_v2"
                                           value="{{ agent.wechat_base_url or '' }}" placeholder="http://example.com:8060">
                                </div>
                                <div class="col-md-6">
                                    <label for="proxy_v2" class="form-label">代理设置</label>
                                    <input type="text" class="form-control" id="proxy_v2" name="proxy_v2"
                                           value="{{ agent.proxy or '' }}" placeholder="http://proxy:port (可选)">
                                </div>
                            </div>
                        </div>

                        <!-- 状态设置 -->
                        <div class="section-header">
                            <h6 class="text-warning">
                                <i class="bi bi-toggles me-1"></i>状态设置
                            </h6>
                            <hr>
                        </div>

                        <div class="row mb-4">
                            <div class="col-md-6">
                                <div class="form-check form-switch">
                                    <input class="form-check-input" type="checkbox" id="vip_is_active" name="vip_is_active" 
                                           value="1" {% if agent.vip_is_active %}checked{% endif %}>
                                    <label class="form-check-label" for="vip_is_active">
                                        VIP功能激活
                                    </label>
                                    <div class="form-text">是否启用VIP相关功能</div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-check form-switch">
                                    <input class="form-check-input" type="checkbox" id="wechat_is_active" name="wechat_is_active" 
                                           value="1" {% if agent.wechat_is_active %}checked{% endif %}>
                                    <label class="form-check-label" for="wechat_is_active">
                                        微信功能激活
                                    </label>
                                    <div class="form-text">是否启用微信相关功能</div>
                                </div>
                            </div>
                        </div>

                        <!-- 系统信息 -->
                        <div class="section-header">
                            <h6 class="text-muted">
                                <i class="bi bi-clock me-1"></i>系统信息
                            </h6>
                            <hr>
                        </div>

                        <div class="row mb-4">
                            <div class="col-md-6">
                                <label class="form-label">创建时间</label>
                                <div class="form-control-plaintext">
                                    {% if agent.created_at %}
                                    {{ agent.created_at.strftime('%Y-%m-%d %H:%M:%S') }}
                                    {% else %}
                                    未知
                                    {% endif %}
                                </div>
                            </div>
                            <div class="col-md-6">
                                <label class="form-label">最后检查时间</label>
                                <div class="form-control-plaintext">
                                    {% if agent.last_check_time %}
                                    {{ agent.last_check_time.strftime('%Y-%m-%d %H:%M:%S') }}
                                    {% else %}
                                    从未检查
                                    {% endif %}
                                </div>
                            </div>
                        </div>

                        <!-- 提交按钮 -->
                        <div class="d-flex justify-content-end gap-2">
                            <a href="{{ url_for('admin.agents') }}" class="btn btn-secondary">取消</a>
                            <button type="submit" class="btn btn-primary">
                                <i class="bi bi-check-circle me-1"></i>保存修改
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_css %}
<style>
.section-header {
    margin-top: 2rem;
    margin-bottom: 1rem;
}

.section-header:first-child {
    margin-top: 0;
}

.section-header h6 {
    margin-bottom: 0.5rem;
    font-weight: 600;
}

.section-header hr {
    margin-top: 0.5rem;
    margin-bottom: 1rem;
    opacity: 0.3;
}

.api-config {
    background: rgba(var(--bs-light-rgb), 0.5);
    border-radius: var(--bs-border-radius);
    padding: 1.5rem;
    margin-bottom: 1rem;
    border: 1px solid var(--bs-border-color);
}

.form-text {
    font-size: 0.875rem;
    color: var(--bs-secondary);
}

.form-check-label {
    font-weight: 500;
}

.form-control-plaintext {
    padding: 0.375rem 0;
    margin-bottom: 0;
    font-size: 1rem;
    line-height: 1.5;
    color: var(--bs-body-color);
    background-color: transparent;
    border: solid transparent;
    border-width: 1px 0;
}

.card {
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
    border: 1px solid rgba(0, 0, 0, 0.125);
}
</style>
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    const apiVersionSelect = document.getElementById('api_version');
    const v1Config = document.getElementById('v1Config');
    const v2Config = document.getElementById('v2Config');
    
    // API版本切换
    apiVersionSelect.addEventListener('change', function() {
        const version = this.value;
        
        // 隐藏所有配置区域
        v1Config.style.display = 'none';
        v2Config.style.display = 'none';
        
        // 清除所有必填验证
        clearRequiredFields();
        
        if (version === 'v1') {
            v1Config.style.display = 'block';
            setV1Required();
        } else if (version === 'v2') {
            v2Config.style.display = 'block';
            setV2Required();
        }
    });
    
    function clearRequiredFields() {
        // 清除V1字段必填
        document.getElementById('token').required = false;
        document.getElementById('wechat_base_url').required = false;
        
        // 清除V2字段必填
        document.getElementById('wxid').required = false;
        document.getElementById('device_id').required = false;
        document.getElementById('wechat_base_url_v2').required = false;
    }
    
    function setV1Required() {
        document.getElementById('token').required = true;
        document.getElementById('wechat_base_url').required = true;
    }
    
    function setV2Required() {
        document.getElementById('wxid').required = true;
        document.getElementById('device_id').required = true;
        document.getElementById('wechat_base_url_v2').required = true;
    }
    
    // 初始化必填字段
    const currentVersion = apiVersionSelect.value;
    if (currentVersion === 'v1') {
        setV1Required();
    } else if (currentVersion === 'v2') {
        setV2Required();
    }
    
    // 表单提交验证
    document.getElementById('editAgentForm').addEventListener('submit', function(e) {
        const apiVersion = apiVersionSelect.value;
        
        // 根据API版本验证相应字段
        if (apiVersion === 'v1') {
            const token = document.getElementById('token').value.trim();
            const baseUrl = document.getElementById('wechat_base_url').value.trim();
            
            if (!token || !baseUrl) {
                e.preventDefault();
                alert('请填写完整的V1 API配置信息');
                return;
            }
        } else if (apiVersion === 'v2') {
            const wxid = document.getElementById('wxid').value.trim();
            const deviceId = document.getElementById('device_id').value.trim();
            const baseUrl = document.getElementById('wechat_base_url_v2').value.trim();
            
            if (!wxid || !deviceId || !baseUrl) {
                e.preventDefault();
                alert('请填写完整的V2 API配置信息');
                return;
            }
        }
    });
});
</script>
{% endblock %}
