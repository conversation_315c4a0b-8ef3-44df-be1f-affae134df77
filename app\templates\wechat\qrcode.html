{% extends "base.html" %}

{% block title %}微信扫码登录 - 统一Web管理系统{% endblock %}

{% block page_title %}
微信扫码登录
{% if api_version %}
<span class="badge {% if api_version == 'v2' %}bg-success{% else %}bg-info{% endif %} ms-2">
    {% if api_version == 'v2' %}V2 API{% else %}V1 API{% endif %}
</span>
{% endif %}
{% endblock %}

{% block content %}
<div class="qrcode-container">
    <div class="row justify-content-center">
        <div class="col-lg-8">
            <div class="qrcode-card">
                <div class="card-header text-center">
                    <h4 class="mb-0">
                        <i class="bi bi-qr-code me-2"></i>微信扫码登录
                        {% if api_version %}
                        <span class="badge {% if api_version == 'v2' %}bg-success{% else %}bg-info{% endif %} ms-2">
                            {% if api_version == 'v2' %}V2 API{% else %}V1 API{% endif %}
                        </span>
                        {% endif %}
                    </h4>
                    <p class="text-muted mt-2">
                        请使用微信扫描下方二维码完成登录
                        {% if api_version == 'v2' %}
                        <br><small>使用V2 API系统 (WXID认证)</small>
                        {% else %}
                        <br><small>使用V1 API系统 (Token认证)</small>
                        {% endif %}
                    </p>
                </div>
                
                <div class="card-body text-center">
                    <!-- 二维码显示区域 -->
                    <div class="qrcode-display" id="qrcodeDisplay">
                        {% if qrcode_data and qrcode_data.qrcode %}
                        <div class="qrcode-wrapper">
                            <img src="data:image/png;base64,{{ qrcode_data.qrcode }}" 
                                 alt="微信登录二维码" 
                                 class="qrcode-image" 
                                 id="qrcodeImage">
                            <div class="qrcode-overlay" id="qrcodeOverlay" style="display: none;">
                                <div class="overlay-content">
                                    <i class="bi bi-hourglass-split"></i>
                                    <p>二维码已过期</p>
                                    <button class="btn btn-primary btn-sm" onclick="refreshQrcode()">
                                        重新获取
                                    </button>
                                </div>
                            </div>
                        </div>
                        {% else %}
                        <div class="qrcode-error">
                            <i class="bi bi-exclamation-triangle text-warning"></i>
                            <p>二维码获取失败，请重试</p>
                            <button class="btn btn-primary" onclick="refreshQrcode()">
                                重新获取
                            </button>
                        </div>
                        {% endif %}
                    </div>
                    
                    <!-- 状态信息 -->
                    <div class="status-section mt-4">
                        <div class="status-card">
                            <div class="status-icon" id="statusIcon">
                                <i class="bi bi-hourglass-split"></i>
                            </div>
                            <div class="status-content">
                                <div class="status-title" id="statusTitle">等待扫描</div>
                                <div class="status-message" id="statusMessage">请使用微信扫描上方二维码</div>
                                <div class="status-progress" id="statusProgress" style="display: none;">
                                    <div class="progress-bar">
                                        <div class="progress-fill" id="progressFill"></div>
                                    </div>
                                    <div class="progress-text" id="progressText"></div>
                                </div>
                            </div>
                        </div>

                        <!-- 网络状态指示器 -->
                        <div class="network-status" id="networkStatus">
                            <div class="network-indicator online" id="networkIndicator">
                                <i class="bi bi-wifi"></i>
                                <span>网络连接正常</span>
                            </div>
                        </div>
                        
                        <!-- 倒计时 -->
                        <div class="countdown-section mt-3">
                            <div class="countdown-bar">
                                <div class="countdown-progress" id="countdownProgress"></div>
                                <div class="countdown-glow" id="countdownGlow"></div>
                            </div>
                            <div class="countdown-display">
                                <div class="countdown-time">
                                    <span class="time-label">剩余时间</span>
                                    <div class="time-value">
                                        <span id="countdownMinutes">{{ (expired_time or 120) // 60 }}</span>
                                        <span class="time-separator">:</span>
                                        <span id="countdownSeconds">{{ '%02d' % ((expired_time or 120) % 60) }}</span>
                                    </div>
                                </div>
                                <div class="countdown-percentage">
                                    <span id="countdownPercent">100</span>%
                                </div>
                            </div>
                            <div class="countdown-warning" id="countdownWarning" style="display: none;">
                                <i class="bi bi-exclamation-triangle"></i>
                                <span>二维码即将过期，请尽快扫描！</span>
                            </div>
                        </div>
                    </div>
                    
                    <!-- 操作按钮 -->
                    <div class="action-buttons mt-4">
                        <button class="btn btn-outline-primary" onclick="refreshQrcode()" title="快捷键: Ctrl+R">
                            <i class="bi bi-arrow-clockwise me-2"></i>刷新二维码
                        </button>
                        <a href="{{ url_for('wechat.index') }}" class="btn btn-outline-secondary" title="快捷键: ESC">
                            <i class="bi bi-arrow-left me-2"></i>返回
                        </a>
                    </div>

                    <!-- 快捷键提示 -->
                    <div class="keyboard-shortcuts mt-3">
                        <small class="text-muted">
                            <i class="bi bi-keyboard me-1"></i>
                            快捷键: <kbd>Ctrl+R</kbd> 刷新 | <kbd>ESC</kbd> 返回 | <kbd>Enter</kbd> 确认跳转
                        </small>
                    </div>
                </div>
            </div>
            
            <!-- 使用说明 -->
            <div class="help-card mt-4">
                <div class="card-header">
                    <h6 class="mb-0">
                        <i class="bi bi-question-circle me-2"></i>使用说明
                    </h6>
                </div>
                <div class="card-body">
                    <div class="help-steps">
                        <div class="step-item">
                            <div class="step-number">1</div>
                            <div class="step-content">
                                <h6>打开微信</h6>
                                <p>在手机上打开微信应用</p>
                            </div>
                        </div>
                        
                        <div class="step-item">
                            <div class="step-number">2</div>
                            <div class="step-content">
                                <h6>扫描二维码</h6>
                                <p>点击微信右上角"+"号，选择"扫一扫"</p>
                            </div>
                        </div>
                        
                        <div class="step-item">
                            <div class="step-number">3</div>
                            <div class="step-content">
                                <h6>确认登录</h6>
                                <p>在手机上确认登录，等待页面自动跳转</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.qrcode-container {
    animation: fadeInUp 0.6s ease-out;
}

.qrcode-card {
    background: var(--bg-secondary);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    box-shadow: var(--shadow-lg);
    overflow: hidden;
}

.qrcode-display {
    margin: 2rem 0;
}

.qrcode-wrapper {
    position: relative;
    display: inline-block;
    padding: 2rem;
    background: white;
    border-radius: var(--border-radius);
    box-shadow: var(--shadow-md);
}

.qrcode-image {
    width: 280px;
    height: 280px;
    border-radius: 12px;
    transition: var(--transition);
}

.qrcode-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.8);
    border-radius: var(--border-radius);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
}

.overlay-content {
    text-align: center;
}

.overlay-content i {
    font-size: 3rem;
    margin-bottom: 1rem;
    opacity: 0.7;
}

.qrcode-error {
    padding: 3rem;
    color: var(--text-secondary);
}

.qrcode-error i {
    font-size: 4rem;
    margin-bottom: 1rem;
}

/* 状态显示样式优化 */
.status-section {
    max-width: 450px;
    margin: 0 auto;
}

.status-card {
    background: linear-gradient(135deg, #ffffff, #f8f9fa);
    border: 1px solid #e9ecef;
    border-radius: 12px;
    padding: 1.5rem;
    text-align: center;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.status-card.status-update {
    transform: scale(1.02);
    box-shadow: 0 6px 12px rgba(0, 0, 0, 0.15);
}

.status-card.success-animation {
    background: linear-gradient(135deg, #d4edda, #c3e6cb);
    border-color: #28a745;
    animation: success-pulse 2s infinite;
}

@keyframes success-pulse {
    0%, 100% { transform: scale(1); }
    50% { transform: scale(1.05); }
}

.status-icon {
    margin-bottom: 1rem;
}

.status-icon i {
    font-size: 3rem;
    transition: all 0.3s ease;
}

.status-title {
    font-size: 1.25rem;
    font-weight: 600;
    color: #495057;
    margin-bottom: 0.5rem;
}

.status-message {
    font-size: 1rem;
    color: #6c757d;
    margin-bottom: 1rem;
}

.status-progress {
    margin-top: 1rem;
}

.progress-bar {
    width: 100%;
    height: 8px;
    background: #e9ecef;
    border-radius: 4px;
    overflow: hidden;
    margin-bottom: 0.5rem;
}

.progress-fill {
    height: 100%;
    background: linear-gradient(90deg, #007bff, #28a745);
    border-radius: 4px;
    transition: width 0.5s ease;
    animation: progress-shimmer 2s infinite;
}

@keyframes progress-shimmer {
    0% { background-position: -200px 0; }
    100% { background-position: 200px 0; }
}

.progress-text {
    font-size: 0.875rem;
    color: #6c757d;
    font-weight: 500;
}

/* 网络状态样式 */
.network-status {
    margin-top: 1rem;
    padding: 0.75rem;
    background: #f8f9fa;
    border-radius: 8px;
    border: 1px solid #e9ecef;
}

.network-indicator {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
    font-size: 0.875rem;
    transition: all 0.3s ease;
}

.network-indicator.online {
    color: #28a745;
}

.network-indicator.offline {
    color: #dc3545;
}

.network-indicator.checking {
    color: #007bff;
}

.network-indicator.checking i {
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* 跳转控制样式 */
.jump-controls {
    text-align: center;
}

.jump-controls .btn {
    font-size: 0.875rem;
    padding: 0.375rem 0.75rem;
}

/* 快捷键提示样式 */
.keyboard-shortcuts {
    text-align: center;
    padding: 0.75rem;
    background: rgba(108, 117, 125, 0.1);
    border-radius: 8px;
    border: 1px solid rgba(108, 117, 125, 0.2);
}

.keyboard-shortcuts kbd {
    background: #495057;
    color: white;
    padding: 0.2rem 0.4rem;
    border-radius: 4px;
    font-size: 0.75rem;
    font-weight: 600;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

/* 二维码图片悬停效果 */
.qrcode-image {
    transition: all 0.3s ease;
    border-radius: 8px;
}

.qrcode-image:hover {
    transform: scale(1.05);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

/* 按钮悬停效果增强 */
.btn {
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.btn:active {
    transform: translateY(0);
}

/* 卡片悬停效果 */
.qrcode-card {
    transition: all 0.3s ease;
}

.qrcode-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
}

/* 响应式优化 */
@media (max-width: 768px) {
    .qrcode-container {
        padding: 1rem;
    }

    .qrcode-card {
        margin: 0;
    }

    .countdown-display {
        flex-direction: column;
        gap: 1rem;
    }

    .time-value {
        font-size: 1.5rem;
    }

    .keyboard-shortcuts {
        font-size: 0.75rem;
    }

    .keyboard-shortcuts kbd {
        font-size: 0.65rem;
        padding: 0.1rem 0.3rem;
    }
}

/* 加载动画 */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.qrcode-container {
    animation: fadeInUp 0.6s ease-out;
}

/* 脉冲动画用于重要提示 */
@keyframes pulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.05); }
    100% { transform: scale(1); }
}

.pulse-animation {
    animation: pulse 2s infinite;
}

/* 倒计时样式优化 */
.countdown-section {
    background: linear-gradient(135deg, #f8f9fa, #e9ecef);
    border-radius: 12px;
    padding: 1.5rem;
    border: 1px solid #dee2e6;
    margin-top: 1.5rem;
}

.countdown-bar {
    position: relative;
    width: 100%;
    height: 12px;
    background: #e9ecef;
    border-radius: 6px;
    overflow: hidden;
    margin-bottom: 1rem;
    box-shadow: inset 0 2px 4px rgba(0,0,0,0.1);
}

.countdown-progress {
    height: 100%;
    background: linear-gradient(90deg, #28a745, #20c997);
    border-radius: 6px;
    transition: all 0.8s ease-in-out;
    position: relative;
}

.countdown-glow {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    border-radius: 6px;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.countdown-glow.glow-warning {
    background: linear-gradient(90deg, rgba(255, 193, 7, 0.3), rgba(253, 126, 20, 0.3));
    opacity: 1;
}

.countdown-glow.glow-danger {
    background: linear-gradient(90deg, rgba(220, 53, 69, 0.4), rgba(253, 126, 20, 0.4));
    opacity: 1;
    animation: pulse-glow 1s infinite alternate;
}

@keyframes pulse-glow {
    0% { opacity: 0.4; }
    100% { opacity: 0.8; }
}

.countdown-display {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1rem;
}

.countdown-time {
    display: flex;
    flex-direction: column;
    align-items: center;
}

.time-label {
    font-size: 0.875rem;
    color: #6c757d;
    margin-bottom: 0.5rem;
    font-weight: 500;
}

.time-value {
    display: flex;
    align-items: center;
    font-size: 2rem;
    font-weight: 700;
    color: #28a745;
    font-family: 'Courier New', monospace;
    transition: all 0.2s ease;
}

.time-value.time-update {
    transform: scale(1.05);
    color: #007bff;
}

.time-separator {
    margin: 0 0.25rem;
    animation: blink 1s infinite;
}

@keyframes blink {
    0%, 50% { opacity: 1; }
    51%, 100% { opacity: 0.3; }
}

.countdown-percentage {
    display: flex;
    flex-direction: column;
    align-items: center;
    font-size: 1.25rem;
    font-weight: 600;
    color: #495057;
}

.countdown-warning {
    background: linear-gradient(135deg, #fff3cd, #ffeaa7);
    border: 1px solid #ffeaa7;
    border-radius: 8px;
    padding: 0.75rem;
    color: #856404;
    text-align: center;
    font-weight: 500;
}

.countdown-warning.warning-blink {
    animation: warning-pulse 1.5s infinite;
}

@keyframes warning-pulse {
    0%, 100% {
        background: linear-gradient(135deg, #fff3cd, #ffeaa7);
        transform: scale(1);
    }
    50% {
        background: linear-gradient(135deg, #ffeaa7, #ffeb3b);
        transform: scale(1.02);
    }
}

.urgent-blink {
    animation: urgent-flash 0.5s infinite alternate;
}

@keyframes urgent-flash {
    0% {
        color: #dc3545;
        text-shadow: 0 0 5px rgba(220, 53, 69, 0.5);
    }
    100% {
        color: #fd7e14;
        text-shadow: 0 0 10px rgba(253, 126, 20, 0.7);
    }
}

.action-buttons {
    display: flex;
    gap: 1rem;
    justify-content: center;
    flex-wrap: wrap;
}

.help-card {
    background: var(--bg-secondary);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    box-shadow: var(--shadow-sm);
}

.help-steps {
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
}

.step-item {
    display: flex;
    align-items: flex-start;
    gap: 1rem;
}

.step-number {
    width: 32px;
    height: 32px;
    background: var(--primary-color);
    color: white;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 600;
    font-size: 0.9rem;
    flex-shrink: 0;
}

.step-content h6 {
    margin: 0 0 0.25rem;
    color: var(--text-primary);
    font-weight: 600;
}

.step-content p {
    margin: 0;
    color: var(--text-secondary);
    font-size: 0.9rem;
}

/* 状态样式 */
.status-waiting {
    color: var(--primary-color);
}

.status-scanned {
    color: var(--warning-color);
}

.status-success {
    color: var(--success-color);
}

.status-error {
    color: var(--danger-color);
}

.status-expired {
    color: var(--secondary-color);
}
</style>

<script>
// 二维码相关变量
let checkTimer = null;
let countdownTimer = null;
let expiredTime = {{ expired_time or 120 }};
let remainingTime = expiredTime;
let checkInterval = 2000; // 2秒检查一次

// 页面加载完成后开始检查
$(document).ready(function() {
    startCountdown();
    startCheckingStatus();
});

// 开始倒计时
function startCountdown() {
    updateCountdown();
    countdownTimer = setInterval(function() {
        remainingTime--;
        updateCountdown();
        
        if (remainingTime <= 0) {
            clearInterval(countdownTimer);
            showExpired();
        }
    }, 1000);
}

// 更新倒计时显示
function updateCountdown() {
    const progress = (remainingTime / expiredTime) * 100;
    const minutes = Math.floor(remainingTime / 60);
    const seconds = remainingTime % 60;

    // 更新进度条
    $('#countdownProgress').css('width', progress + '%');

    // 更新时间显示
    $('#countdownMinutes').text(minutes);
    $('#countdownSeconds').text(seconds.toString().padStart(2, '0'));
    $('#countdownPercent').text(Math.round(progress));

    // 根据剩余时间改变进度条颜色和效果
    if (remainingTime <= 30) {
        $('#countdownProgress').css({
            'background': 'linear-gradient(90deg, #dc3545, #fd7e14)',
            'box-shadow': '0 0 10px rgba(220, 53, 69, 0.5)'
        });
        $('#countdownGlow').addClass('glow-danger');

        // 显示警告信息
        $('#countdownWarning').show().addClass('warning-blink');

        // 添加紧急闪烁效果
        if (remainingTime <= 10) {
            $('.countdown-time').addClass('urgent-blink');
        }
    } else if (remainingTime <= 60) {
        $('#countdownProgress').css({
            'background': 'linear-gradient(90deg, #fd7e14, #ffc107)',
            'box-shadow': '0 0 8px rgba(255, 193, 7, 0.4)'
        });
        $('#countdownGlow').addClass('glow-warning');
        $('#countdownWarning').hide().removeClass('warning-blink');
        $('.countdown-time').removeClass('urgent-blink');
    } else {
        $('#countdownProgress').css({
            'background': 'linear-gradient(90deg, #28a745, #20c997)',
            'box-shadow': '0 0 6px rgba(40, 167, 69, 0.3)'
        });
        $('#countdownGlow').removeClass('glow-danger glow-warning');
        $('#countdownWarning').hide().removeClass('warning-blink');
        $('.countdown-time').removeClass('urgent-blink');
    }

    // 添加数字变化动画
    $('.time-value').addClass('time-update');
    setTimeout(() => $('.time-value').removeClass('time-update'), 200);
}

// 开始检查登录状态
function startCheckingStatus() {
    checkLoginStatus();
}

// 检查登录状态
function checkLoginStatus() {
    if (remainingTime <= 0) {
        return;
    }

    // 显示网络检查状态
    updateNetworkStatus('checking');

    $.ajax({
        url: '{{ url_for("wechat.check_qrcode") }}',
        method: 'GET',
        timeout: 10000,
        beforeSend: function() {
            // 可以在这里添加请求前的处理
        },
        success: function(response) {
            console.log('检查状态响应:', response);
            updateNetworkStatus('online');

            // V2 API登录成功检测：Code=0, Success=true, Message="登录成功"
            if (response.Code === 0 && response.Success && response.Message === '登录成功' && response.Data) {
                // V2登录成功
                updateLoginStatus('LOGIN_SUCCESS', response.Data);
            } else if (response.Code === 200 && response.Data) {
                // V1 API或其他格式的响应处理
                const status = response.Data.status;
                updateLoginStatus(status, response.Data);
            } else {
                updateLoginStatus('ERROR', { message: response.Text || response.Message || '状态检查异常' });
            }

            // 如果二维码未过期，继续检查
            if (remainingTime > 0) {
                checkTimer = setTimeout(checkLoginStatus, checkInterval);
            }
        },
        error: function(xhr, status, error) {
            console.error('检查状态失败:', error);
            updateNetworkStatus('offline');
            updateLoginStatus('NETWORK_ERROR', { message: '网络连接失败，正在重试...' });

            // 网络错误时减少检查频率
            if (remainingTime > 0) {
                checkTimer = setTimeout(checkLoginStatus, checkInterval * 2);
            }
        }
    });
}

// 更新登录状态显示
function updateLoginStatus(status, data = {}) {
    let iconClass = '';
    let title = '';
    let message = '';
    let showProgress = false;
    let progressValue = 0;
    let progressText = '';

    switch (status) {
        case 'WAITING_SCAN':
            iconClass = 'bi-hourglass-split text-primary';
            title = '等待扫描';
            message = '请使用微信扫描上方二维码';
            break;

        case 'SCAN_SUCC':
            iconClass = 'bi-check-circle text-warning';
            title = '扫描成功';
            message = '请在手机上确认登录';
            showProgress = true;
            progressValue = 50;
            progressText = '等待确认...';
            break;

        case 'CONFIRMING':
            iconClass = 'bi-shield-check text-info';
            title = '正在确认';
            message = '正在验证登录信息...';
            showProgress = true;
            progressValue = 75;
            progressText = '验证中...';
            break;

        case 'LOGIN_SUCCESS':
            iconClass = 'bi-check-circle-fill text-success';
            title = '登录成功';
            message = '登录成功，正在跳转...';
            showProgress = true;
            progressValue = 100;
            progressText = '跳转中...';

            // 显示成功动画并跳转
            showLoginSuccess(data);
            return;

        case 'EXPIRED':
            showExpired();
            return;

        case 'NETWORK_ERROR':
            iconClass = 'bi-wifi-off text-danger';
            title = '网络错误';
            message = data.message || '网络连接失败，正在重试...';
            break;

        case 'ERROR':
            iconClass = 'bi-exclamation-triangle text-danger';
            title = '检查失败';
            message = data.message || '状态检查失败，请刷新页面';
            break;

        default:
            // 检查其他登录成功标志
            if (data.loginState === 1 || data.state === 2 ||
                (data.wxid && (data.nick_name || data.nickname))) {
                updateLoginStatus('LOGIN_SUCCESS', data);
                return;
            } else {
                updateLoginStatus('WAITING_SCAN');
                return;
            }
    }

    // 更新UI
    $('#statusIcon i').removeClass().addClass('bi ' + iconClass);
    $('#statusTitle').text(title);
    $('#statusMessage').text(message);

    if (showProgress) {
        $('#statusProgress').show();
        $('#progressFill').css('width', progressValue + '%');
        $('#progressText').text(progressText);
    } else {
        $('#statusProgress').hide();
    }

    // 添加状态变化动画
    $('.status-card').addClass('status-update');
    setTimeout(() => $('.status-card').removeClass('status-update'), 300);
}

// 更新网络状态
function updateNetworkStatus(status) {
    const indicator = $('#networkIndicator');
    const icon = indicator.find('i');
    const text = indicator.find('span');

    indicator.removeClass('online offline checking');

    switch (status) {
        case 'online':
            indicator.addClass('online');
            icon.removeClass().addClass('bi bi-wifi');
            text.text('网络连接正常');
            break;
        case 'offline':
            indicator.addClass('offline');
            icon.removeClass().addClass('bi bi-wifi-off');
            text.text('网络连接异常');
            break;
        case 'checking':
            indicator.addClass('checking');
            icon.removeClass().addClass('bi bi-arrow-clockwise');
            text.text('检查状态中...');
            break;
    }
}

// 显示登录成功动画和跳转
function showLoginSuccess(data) {
    // 清除所有定时器
    clearInterval(countdownTimer);
    clearTimeout(checkTimer);

    // 显示成功动画
    $('.status-card').addClass('success-animation');

    // 检查是否有心跳开启结果
    let heartbeatMessage = '';
    if (data && data.heartbeat_result) {
        const heartbeatResult = data.heartbeat_result;
        if (heartbeatResult.Code === 200 || heartbeatResult.Code === 1) {
            heartbeatMessage = '自动心跳已开启！';
        } else {
            heartbeatMessage = `自动心跳开启失败: ${heartbeatResult.Text || heartbeatResult.Message || '未知错误'}`;
        }
    } else {
        // V1 API或没有心跳结果
        heartbeatMessage = '';
    }

    // 显示跳转倒计时
    let jumpCountdown = 3;
    const jumpTimer = setInterval(() => {
        let message = `登录成功！${jumpCountdown} 秒后自动跳转...`;
        if (heartbeatMessage) {
            message = `登录成功！${heartbeatMessage} ${jumpCountdown} 秒后自动跳转...`;
        }
        $('#statusMessage').text(message);
        jumpCountdown--;

        if (jumpCountdown < 0) {
            clearInterval(jumpTimer);
            // 跳转到微信管理页面并刷新状态
            redirectToWechatIndex();
        }
    }, 1000);

    // 添加取消跳转按钮
    if (!$('#cancelJump').length) {
        $('#statusProgress').after(`
            <div class="jump-controls mt-3">
                <button id="cancelJump" class="btn btn-outline-secondary btn-sm">
                    <i class="bi bi-x-circle me-1"></i>取消自动跳转
                </button>
            </div>
        `);

        $('#cancelJump').click(function() {
            clearInterval(jumpTimer);
            $('#statusMessage').text('登录成功！您可以手动返回微信管理页面。');
            $(this).parent().remove();
        });
    }
}

// 显示过期状态
function showExpired() {
    clearInterval(countdownTimer);
    clearTimeout(checkTimer);

    $('#qrcodeOverlay').show();
    $('#statusMessage').html('<i class="bi bi-clock-history"></i><span>二维码已过期</span>')
                       .removeClass().addClass('status-message status-expired');
    $('#countdownProgress').css('width', '0%');
    $('#countdownMinutes').text('0');
    $('#countdownSeconds').text('00');
    $('#countdownPercent').text('0');
    $('#countdownWarning').hide();
}

// 刷新二维码
function refreshQrcode() {
    showLoading('正在获取新的二维码...');
    window.location.reload();
}

// 跳转到微信管理页面并刷新状态
function redirectToWechatIndex() {
    // 显示跳转加载状态
    $('#statusMessage').text('正在跳转并刷新状态...');
    $('#statusProgress').show();
    $('#progressFill').css('width', '100%');
    $('#progressText').text('跳转中...');

    // 设置会话标记，表示需要刷新状态
    sessionStorage.setItem('wechat_login_success', 'true');
    sessionStorage.setItem('login_timestamp', Date.now().toString());

    // 跳转到微信管理页面
    window.location.href = "{{ url_for('wechat.index') }}?refresh=1&from=qrcode";
}

// 手动跳转函数
function manualRedirect() {
    if (confirm('确定要跳转到微信管理页面吗？')) {
        redirectToWechatIndex();
    }
}

// 添加键盘快捷键支持
$(document).keydown(function(e) {
    switch(e.which) {
        case 82: // R键 - 刷新二维码
            if (e.ctrlKey || e.metaKey) {
                e.preventDefault();
                refreshQrcode();
            }
            break;
        case 27: // ESC键 - 返回
            e.preventDefault();
            window.location.href = "{{ url_for('wechat.index') }}";
            break;
        case 13: // Enter键 - 手动跳转（登录成功后）
            if ($('.success-animation').length > 0) {
                e.preventDefault();
                manualRedirect();
            }
            break;
    }
});

// 添加页面可见性检测
document.addEventListener('visibilitychange', function() {
    if (document.hidden) {
        // 页面隐藏时暂停检查
        if (checkTimer) {
            clearTimeout(checkTimer);
        }
    } else {
        // 页面显示时恢复检查
        if (remainingTime > 0) {
            checkLoginStatus();
        }
    }
});

// 页面卸载时清理定时器
$(window).on('beforeunload', function() {
    clearInterval(countdownTimer);
    clearTimeout(checkTimer);
});
</script>
{% endblock %}
