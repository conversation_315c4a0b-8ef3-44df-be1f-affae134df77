# YBA监控管理系统

一个现代化的Web管理系统，整合了微信登录管理和VIP会员管理功能，采用简洁的深色主题和响应式设计。

## 功能特性

### 🎯 核心功能
- **微信登录管理**: 二维码登录、状态监控、用户信息管理
- **🆕 启动时状态检查**: 系统启动时自动检查所有微信API连接状态
- **VIP会员管理**: 会员卡创建、用户绑定、积分管理
- **用户管理**: 代理商管理、权限控制、Token管理（管理员功能）
- **统一认证**: 支持管理员Token和代理商Token登录

### 🎨 界面设计
- **简洁深色主题**: 高对比度的深色界面设计，提升可读性
- **响应式布局**: 完美适配桌面端和移动端
- **可折叠侧边导航**: 优化的侧边导航栏，支持收缩展开
- **统一配色方案**: 简化的配色体系，减少视觉干扰
- **清晰的信息层级**: 精简的页面布局，突出重要功能

### 🔧 技术特性
- **Flask框架**: 基于Python Flask的Web应用
- **模块化设计**: 清晰的模块划分和蓝图架构
- **MySQL数据库**: 使用MySQL存储数据
- **Session管理**: 基于文件的会话管理
- **API集成**: 支持微信API调用

## 安装部署

### 环境要求
- Python 3.7+
- MySQL 5.7+
- 现代浏览器（Chrome、Firefox、Safari、Edge）

### 安装步骤

1. **克隆项目**
```bash
git clone <repository-url>
cd unified-admin-system
```

2. **安装依赖**
```bash
pip install -r requirements.txt
```

3. **配置环境变量**
```bash
# 复制环境变量模板
cp .env.example .env

# 编辑 .env 文件，设置数据库连接等配置
# 或者运行环境检查脚本自动创建模板
python check_env.py
```

4. **验证环境配置**
```bash
# 检查环境变量配置
python check_env.py

# 运行系统测试
python test_system.py
```

5. **数据库配置**
确保MySQL数据库中存在以下表结构：
- `agents`: 代理商表
- `vip_cards`: VIP会员卡表
- `user_binding_history`: 用户绑定历史表

6. **启动应用**
```bash
# 推荐使用快速启动脚本（包含环境检查）
python quick_start.py

# 或者直接启动
python start.py

# 或者使用Flask命令
python run.py
```

## 配置说明

### 环境变量自动加载

系统支持自动从 `.env` 文件加载环境变量，无需手动设置系统环境变量：

- **自动检测**: 系统启动时自动检测并加载 `.env` 文件
- **优先级**: 系统环境变量优先于 `.env` 文件中的配置
- **类型转换**: 支持字符串、整数、布尔值等类型自动转换
- **验证功能**: 内置必需环境变量验证和配置检查

### 环境变量配置

在 `.env` 文件中配置以下变量：

```env
# Flask配置
SECRET_KEY=your-secret-key-here

# 数据库配置
MYSQL_HOST=your-mysql-host
MYSQL_PORT=3306
MYSQL_USER=your-mysql-user
MYSQL_PASSWORD=your-mysql-password
MYSQL_DB=your-database-name

# 管理员配置
ADMIN_TOKEN=your-admin-token

# 应用配置
FLASK_ENV=development
FLASK_DEBUG=True
FLASK_HOST=0.0.0.0
FLASK_PORT=5000
```

### 数据库表结构

#### agents 表
```sql
CREATE TABLE `agents` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `group` varchar(50) NOT NULL COMMENT '代理分组',
  `name` varchar(100) NOT NULL COMMENT '代理名称',
  `contact` varchar(200) DEFAULT NULL COMMENT '联系方式',
  `token` varchar(100) NOT NULL COMMENT '访问Token',
  `wechat_base_url` varchar(500) DEFAULT NULL COMMENT '微信API基础URL',
  `proxy` varchar(200) DEFAULT NULL COMMENT '代理设置',
  `api_key` varchar(100) DEFAULT NULL COMMENT 'API密钥',
  `vip_is_active` tinyint(1) DEFAULT 1 COMMENT 'VIP功能是否激活',
  `wechat_is_active` tinyint(1) DEFAULT 0 COMMENT '微信功能是否激活',
  `wechat_login_status` tinyint(1) DEFAULT 0 COMMENT '微信登录状态',
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `group` (`group`),
  UNIQUE KEY `token` (`token`)
);
```

#### vip_cards 表
```sql
CREATE TABLE `vip_cards` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `card_number` varchar(50) NOT NULL COMMENT '会员卡号',
  `agent_id` int(11) NOT NULL COMMENT '代理商ID',
  `user_id` varchar(100) DEFAULT NULL COMMENT '绑定用户ID',
  `points` int(11) DEFAULT 0 COMMENT '积分',
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP,
  `expired_at` datetime DEFAULT NULL COMMENT '过期时间',
  `is_active` tinyint(1) DEFAULT 1 COMMENT '是否激活',
  PRIMARY KEY (`id`),
  UNIQUE KEY `card_number` (`card_number`),
  KEY `agent_id` (`agent_id`),
  KEY `user_id` (`user_id`)
);
```

## 使用说明

### 登录系统
1. 访问系统首页
2. 输入管理员Token或代理商Token
3. 登录成功后进入仪表盘

### 微信登录管理
1. 在侧边导航栏点击"微信登录管理"
2. 查看当前微信登录状态
3. 点击"获取二维码"进行微信登录
4. 使用微信扫描二维码完成登录

### VIP会员管理
1. 在侧边导航栏点击"VIP会员管理"
2. 查看VIP卡统计信息
3. 创建新的VIP会员卡
4. 管理现有VIP卡（绑定用户、更新积分等）

### 用户管理（管理员功能）
1. 使用管理员Token登录
2. 在侧边导航栏点击"用户管理"
3. 查看所有代理商信息
4. 创建、编辑、删除代理商账户
5. 管理代理商权限和功能激活状态

## 技术架构

### 目录结构
```
unified-admin-system/
├── app/                    # 应用主目录
│   ├── __init__.py        # Flask应用工厂
│   ├── config.py          # 配置文件
│   ├── models.py          # 数据模型
│   ├── auth/              # 认证模块
│   ├── wechat/            # 微信管理模块
│   ├── vip/               # VIP管理模块
│   ├── admin/             # 用户管理模块
│   ├── static/            # 静态资源
│   │   ├── css/           # 样式文件
│   │   ├── js/            # JavaScript文件
│   │   └── images/        # 图片资源
│   └── templates/         # 模板文件
├── requirements.txt       # Python依赖
├── .env.example          # 环境变量模板
├── start.py              # 启动脚本
├── run.py                # Flask运行文件
└── README.md             # 说明文档
```

### 模块说明
- **auth**: 认证和会话管理
- **wechat**: 微信API集成和登录管理
- **vip**: VIP会员卡管理和用户绑定
- **admin**: 代理商管理和系统配置

## 开发说明

### 添加新功能
1. 在相应模块下创建路由文件
2. 在模板目录下创建对应的HTML模板
3. 更新侧边导航栏菜单
4. 添加必要的权限检查

### 自定义样式
- 主要样式文件：`app/static/css/style.css`
- 使用CSS变量定义主题色彩
- 支持深色主题和响应式设计

### API集成
- 微信API客户端：`app/wechat/utils.py`
- 支持代理服务器配置
- 统一的错误处理和日志记录

## 故障排除

### 常见问题

1. **环境变量配置错误**
   - 运行 `python check_env.py` 检查配置
   - 确保 `.env` 文件格式正确
   - 验证必需的环境变量是否设置

2. **数据库连接失败**
   - 检查数据库配置是否正确
   - 确认数据库服务是否运行
   - 验证用户权限

3. **微信API调用失败**
   - 检查微信API基础URL配置
   - 验证API密钥是否正确
   - 确认网络连接和代理设置

4. **页面样式异常**
   - 清除浏览器缓存
   - 检查静态文件路径
   - 验证CSS文件是否正确加载

5. **登录失败**
   - 检查Token是否正确
   - 确认代理商账户是否激活
   - 查看应用日志获取详细错误信息

### 环境变量工具

系统提供了便捷的环境变量管理工具：

```bash
# 检查环境变量配置
python check_env.py

# 快速启动（包含环境检查）
python quick_start.py

# 系统测试（包含环境变量测试）
python test_system.py
```

### 微信登录状态检查
系统提供了专门的工具来检查微信API连接状态：

```bash
# 快速查看当前状态
python check_login_status.py --quick

# 完整检查所有代理商状态
python check_login_status.py --check

# 显示代理商详细信息
python check_login_status.py --details

# 执行所有检查
python check_login_status.py --all
```

### 日志查看
应用日志保存在 `app.log` 文件中，包含详细的错误信息和调试信息。

## 更新日志

### v1.2.0 (2025-01-29)
- 🚀 **启动时状态检查**: 系统启动时自动检查所有微信API连接状态
- ⚡ **并行检查**: 使用线程池并行检查多个代理商，提升效率75%
- 🛠️ **独立检查工具**: 新增命令行工具支持手动检查登录状态
- 📊 **数据库增强**: 添加last_check_time字段记录检查时间
- 🔧 **错误处理优化**: 完善的超时控制和智能降级机制
- 📋 **详细状态报告**: 提供格式化的状态报告和错误诊断

### v1.1.0 (2025-01-28)
- 🧹 **项目简化**: 删除不再使用的旧系统文件，使项目更加简洁
- 📊 **仪表盘优化**: 简化概览内容，聚焦核心指标（微信登录状态、已激活VIP数量、剩余积分）
- 🔧 **微信API修复**: 改进微信API错误处理，支持多种API端点和备用方案
- 📄 **模板完善**: 创建缺失的VIP管理模板，完善用户体验
- 🎨 **UI简化**: 移除过度装饰，提高界面简洁性和可读性
- 🌍 **环境变量增强**: 完善自动加载机制，提供更好的配置管理

### v1.0.0 (2025-01-28)
- 🎉 初始版本发布
- ✨ 整合微信登录管理和VIP会员管理功能
- 🎨 现代化深色主题UI设计
- 🔐 统一Token认证系统
- 📱 响应式设计，支持移动端
- 🛠️ 完整的管理员和代理商权限控制

## 许可证

本项目采用 MIT 许可证。

## 联系方式

如有问题或建议，请联系开发团队。
